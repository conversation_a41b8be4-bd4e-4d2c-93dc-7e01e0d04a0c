@if (!showEditor && cellRendererParams?.node?.group) {
  <div>
  {{ cellRendererParams?.valueFormatted ?? cellRendererParams?.value }}
  </div>
}
@if (showEditor) {
    <div class="col-12 p-inputgroup">
        <input type="text" pInputText [disabled]="true" [ngModel]="displayValue" />
        <span class="p-inputgroup-addon"><i class="pi pi-times" (click)="reset()"></i></span>
        <span class="p-inputgroup-addon"><i class="pi pi-pencil" (click)="showDialog(true)"></i></span>
    </div>
}
<p-dialog [(visible)]="dialogVisible" styleClass="p-dialog-default-value" [modal]="true"
    (onHide)="showDialog(false)" appendTo="body" [resizable]="false">
    <p-header>Configure {{columnConfig.title}} Values</p-header>
    @if (defaultValue) {
        <div class="card">
            <p class="section-description">Enter a default value or select from user profile values</p>
            <span class="p-float-label">
                <input type="text" class="my-3" pInputText [(ngModel)]="defaultValueInputText"
                    [disabled]="defaultValue.isUserProfileValue" />
                <label>Default Value</label>
            </span>
            <p-checkbox label="Set a user profile value as a default value" [binary]="true"
                [(ngModel)]="defaultValue.isUserProfileValue" (onChange)="resetValue()"></p-checkbox>
            @if (defaultValue.isUserProfileValue) {
                <p-dropdown class="my-3" [options]="profileProperties"
                    [(ngModel)]="defaultValue.value" optionLabel="name" optionValue="name"
                    placeholder="Select User Profile Value"></p-dropdown>
            }
        </div>
    }
    <ng-template pTemplate="footer">
        <p-button pRipple type="button" label="Save" [outlined]="true"  (onClick)="save()"></p-button>
        <p-button pRipple type="button" label="Cancel" [outlined]="true" severity="secondary"
            (onClick)="showDialog(false)"></p-button>
    </ng-template>
</p-dialog>