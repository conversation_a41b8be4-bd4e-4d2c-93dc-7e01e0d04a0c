
@import '../palette_variables';
@import '../../../../../node_modules/ag-grid-community/styles/ag-grid';
@import '../../../../../node_modules/ag-grid-community/styles/ag-theme-material.min';
@import '../../../../../node_modules/ag-grid-community/styles/ag-theme-alpine.min';
@import '../../../../../node_modules/ag-grid-community/styles/ag-theme-balham.min';
@import '../../../../../node_modules/ag-grid-community/styles/ag-theme-quartz.min';

//font-face-to-avoid-parathesis
@font-face {
  font-family: no-parens;
  src: url("data:application/x-font-woff;base64,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");
  unicode-range: U+0028, U+0029;
}

/* With AG Grid we identify our theme-extension and group them by class for their application
   Also we ensure that we fully include our base theme for ag-theme-material for overriding.

  Naming Convention Prefix Guidelines
    ag: Ag Grid classes for overriding
    sbi: Class prefix for Integration-Web-Apps */

/* Variables in use within this document (below) */
/* ToDo: Sass Variables to replace static values, needs to be validated through _Variables (files) */
$color-rev-text: var(--InverseText); /* Text color White for reversing text on dark backgrounds */
$color-vision-white: var(--surfacePrimary); /* White background for form elements and panels */
$color-vision-background-blu: var(--primary); /* Vision33 Blue background for headers */
$color-vision-background-blu-dark: var(--bgShade2); /* Vision33 Dark Blue order accent for headers */
$ag-color-alt-row: rgba(125, 125, 125, 0.1); /* Grey alternating row for data-table displays  */
$color-aggrid-statusbar: var(--surfaceSecondary); /* Color of the background for the AG-Grid status bar */


/* AG-Grid Theme Material */
.ag-theme-material  {

  --ag-cell-horizontal-padding: var(--ag-grid-size);
  --ag-row-group-indent-size: calc(var(--ag-grid-size) + var(--ag-icon-size));
  --ag-checkbox-checked-color: var(--primary);

  border-top: 1px solid var(--ag-border-color);
  border-bottom: 1px solid var(--ag-border-color);

  // This has been commented to avoid build errors
  // @include ag-theme-material ((

  // /* For use with theme parameters (where possible) */
  // cell-horizontal-border: solid ag-derived(secondary-border-color),
  //));

  /* Here we are overriding the grid odd rows for all of Integration's Ag-Grid uses within this app */
  .ag-row-odd {
    background-color: var(--surfaceSecondary);
  }
  /* Here we are overriding the grid header-cell for all of Integration's Ag-Grid uses within this app */
  .ag-header-cell {
    border-right: 1px solid ag-param(secondary-border-color);
  }

  .ag-header{
    max-width: fit-content;
  }
 
  .ag-paging-row-summary-panel-number{
    font-weight: bold;
  }

  .ag-paging-panel{
    height: 2rem;
    border-top: 0;
  }
 
  --ag-material-primary-color: var(--primary);
  --ag-material-accent-color: var(--text-secondary);

  --ag-header-foreground-color: var(--BaseText);
  --ag-secondary-foreground-color: var(--BaseText);

  /* For use styling header text on the grid for text wrapping at white-space*/
  .ag-header-cell-label .ag-header-cell-text {
    white-space: break-spaces;
  }

  input[type="text"], input[type="date"], input[type="number"], textarea, select, .p-inputtext{
    height: 32px !important;
    min-height: initial !important;
    padding-top: 0 !important;
    padding-bottom: 0 !important;
    background-color: var(--surfaceSecondary) !important;
    border-bottom: 2px solid !important;
    border-bottom-color: var(--BorderColor) !important;
    border-radius: var(--border-radius) !important;
    &:focus, &:hover{
      background-color: var(--surfaceBlue) !important;
      border-bottom-color: var(--primary) !important;
    }
  }
  .ag-header-group-cell:not(.ag-column-resizing) + .ag-header-group-cell:not(.ag-column-hover):not(.ag-header-cell-moving):hover, 
  .ag-header-cell:not(.ag-column-resizing) + .ag-header-cell:not(.ag-column-hover):not(.ag-header-cell-moving):hover,
  .ag-header-group-cell:first-of-type:not(.ag-header-cell-moving):hover,
  .ag-header-cell:not(.ag-column-hover):first-of-type:not(.ag-header-cell-moving):hover {
    input[type="text"], input[type="date"], input[type="number"], textarea, select, .p-inputtext{
      background-color: var(--surfaceBlue) !important;
      border-bottom-color: var(--primary) !important;
    }
  }
  .ag-header-icon{
    color: var(--TextLight);
  }

  .ag-root-wrapper-body{
    border-right: 1px solid var(--surfacePrimary);
    margin-right: -0.2rem;
  } 

  app-nested-form-cell-renderer, app-nested-grid-cell-renderer{
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0;
    margin: 0;
    height: 32px;
  }

  .p-inputgroup-addon {
    .p-button.p-button-text{
      color: var(--BaseText) !important;
      .p-button-icon{
        font-size: 0.875rem !important;
      }
      &:hover, &:active, &:focus{
        color: var(--primaryActive) !important;
      }
    }
    .p-button {
      min-height: 1.5rem !important;
    }
  }

  .p-inputnumber{
    .p-button.p-button-icon-only:not(.p-datepicker-trigger):not(.p-inputnumber-button) {
      width: 1rem !important;
      height: 0.5rem !important;
      padding: 0;
    }
    .p-inputnumber-button-group {
      .p-button {
        &.p-inputnumber-button-up {
          &, &:hover {
            margin-top: 0;
            margin-bottom: -1px;
          }
        }
        &.p-inputnumber-button-down {
          &, &:hover {
            margin-top: -1px;
            margin-bottom: 0;
          }
        }
        &.p-inputnumber-button-up, &.p-inputnumber-button-down {
          &, &:hover {
            margin-left: -1px;
          }
        }
      }
    }
  }


  /* For use with the ag-custom-renderer for tool-tips */
  /* background colors and font-colors  for custom-tooltips default without being set. Colors
   * can be set within the parameters for the aggrid-tooltip-renderer colDef */
  .ag-custom-tooltip {
    border-radius: 5px;
    padding:.75em;
    min-width: 120px;
    max-width: 300px;
    white-space: break-spaces;
    word-wrap: break-word;
    z-index: 10000000;
  }

  /* For use with the ag-custom-renderer for tool-tips */
  .ag-custom-tooltip p {
    margin:0;
    white-space: break-spaces;
    max-width: 300px;
  }

  /* For use with the ag-custom-renderer for tool-tips */
  .ag-custom-tooltip p:first-of-type {
    font-weight: bold;
  }

  .ag-ltr{ 
    input  {
      width: 100%;
      
      &[type=checkbox], &[type=button], &[type=submit], &[type=radio]{
        width: auto;
        margin-right: 0.5rem;
        vertical-align: middle;
      }
    }
    
    .ag-floating-filter-button{
      margin-left: 0.7rem !important;
      .ag-button{
        color: var(--TextLight);
      }
    }
  }
  .ag-row {
    border-color: var(--BorderColor);
  }
  .ag-row.ag-row-level-0 {
    background-color: var(--surfacePrimary);
    &.ag-row-odd {
      background-color: var(--surfaceSecondary);
    }
  }
  // .ag-row.ag-row-level-1 {
  //   background-color: #eee;
  // }
  // .ag-row.ag-row-level-2,
  // .ag-row.ag-row-level-3,
  // .ag-row.ag-row-level-4,
  // .ag-row.ag-row-level-5 {
  //   background-color: #ddd;
  // }
  .ag-group-value {
    flex: 1;
  }

  .ag-ltr {
    .ag-group-child-count {
      font-family: no-parens, sans-serif;
      background: var(--surfaceSecondary);
      color: var(--BaseText);
      font-weight: 700;
      border: 1px solid var(--BorderColor);
      font-size: 0.625rem;
      border-radius: 0.625rem;
      padding: 0.25rem 0.313rem;
      height: 1.25rem !important;
      min-width: 1.25rem;
      width: auto;
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;
      top: 0px;
      right: -0.25rem;
      margin-left: 0.5rem;
      margin-right: 0.5rem;
      &:empty {
        visibility: hidden;
      }
    }
  }

  .ag-layout-auto-height .ag-center-cols-container,
  .ag-layout-auto-height .ag-center-cols-viewport{
    min-height: 12rem;
  }

  .ag-floating-filter-input input{
    width: 100% !important;
  }
}

.ag-cell-wrap-text {
  word-break: break-word;
}

/* Mobile styles */

.sbi-actionable-grid {
  padding: 75px;
}
.mobile-ag-header-table {
  background: var(--secondary);
  color: $color-vision-white;
  padding: 15px;
  display: none;
}

@media (max-device-width: 767px) {
  .sbi-actionable-grid {
    padding: 47px 0 75px;
  }
  .ag-header-table {
    h3 {
      display: none;
    }
    .p-toolbar-group-end {
      text-align: left;
    }
  }
  .mobile-ag-header-table {
    display: block;
  }
}

.ag-cell-value{
  line-height: 1.5;
}

.ag-ltr .ag-cell, .ag-cell-wrapper.ag-row-group{
  display: flex;
  align-items: center;
  overflow: hidden;
  width: 100%;
}

.aggrid-radio-button-wrapper {
  padding: 0.75rem;
  font-size: 0.875rem;
  font-weight: 400;
  .p-field-radiobutton{
    p-radiobutton{
      vertical-align: middle;
      padding: 0.2rem 0.5rem 0.45rem 0;
    }   
  }  
}

/*Hide active cell outline */
.ag-ltr .ag-has-focus .ag-cell-focus:not(.ag-cell-range-selected){
  border-color: inherit !important;
  outline: none !important;
}

.sb-field-right-align {
    text-align: right;
    justify-content: end;
}

/*Checkbox renderer alignment*/

.ag-cell-value.ag-cell app-checkbox-renderer {
  display: flex;
  line-height: normal;
  justify-content: center;
  align-items: center;
  height: 100%;
  width: 100%;
}

/* The Integrations Messages Grid-display (messages-data-ag-grid.component) */
/* Note this set of styles includes the supporting status bar and search component styling */
/* use this class of styles paired with the ag-theme-material or ag-theme-alpine. */
/* NOTE: This class sets the Grid with traditional Vision33 theme styles overriding grid/PrimeNG defaults. */


.sbi-messages-grid-display {
  .ag-display-size {
    width: 100%;
    height: calc(100vh - 17rem);
  }

  .ag-header-table {
    background: var(--secondaryShade2);
    color: var(--InverseText);
    padding: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .ag-status-bar {
    background-color: transparent;
    height: fit-content;
    width: fit-content;
    border: none;
    padding: 0;
    margin: 0;
    position: absolute;
    bottom: -0.8rem;
  }
  &.status-bar-fix{
    .ag-status-bar{
      bottom: 0.25rem;
    }
  }
  &.ssrm-status-bar-fix{
    .ag-status-bar{
      bottom: 0.125rem;
      left: 0.25rem;

      p-floatLabel{
        .p-float-label{
          margin: 0;
          padding: 0;
        }
        .p-dropdown .p-inputtext{
          padding-top: 0.75rem !important;
        }
        label{
          left: 0.5rem;
          top: 0 !important;
          padding-top: 0;
        }
        .p-dropdown-trigger{
          margin-top: 0.25rem;
        }
      }
    }
  }
  
  .aggrid-statusbar-messaging {
    width: 100%;
    margin-left: 0.5rem;
    display: inline;
    .p-inputtext{
      padding-top: 0.8rem !important;
    }
  }

  .ag-search-input {
    height: 30px;
    background: $color-vision-white;
  }

  .ag-search-icon {
    margin: 0 4px 0 0;
    color: $color-rev-text;
  }

  .ag-copy-icon {
    &:hover {
      cursor: pointer;
    }
  }

  /* Overriding the bass padding of icons at 11% to center the icons within the grid. */
  .msg-icons {
    i {
      margin-top: 4%;
    }
    &.msg-undefined i{
      display: none;
    }
  }
  .ag-set-filter-item .ag-label {
    display: inline-flex;
    .msg-icons i{
      margin-top: 0.125rem;
    }
  }

  .messages-split {
    margin-top: 4px;
  }

  .ag-statusbar-renderer-index {
    z-index: 100;
    overflow: visible !important;
    width: 150px;
    margin-right: 15px;
    display: inline;
  }
}

.aggrid-radio-button-wrapper {
  padding: 0.75rem;
  font-size: 0.875rem;
  font-weight: 400;
  .p-field-radiobutton{
    p-radiobutton{
      vertical-align: middle;
      padding: 0.2rem 0.5rem 0.45rem 0;
    }   
  }  
}

.sbi-actionable-grid-display {

  .ag-display-size {
    width: 100%;
    height: 79.5vh;
    @media screen and (max-width:1536px) {
      height: 79.5vh;
    }
  }

  .ag-header-table {
    background: $color-vision-background-blu;
    color: $color-rev-text;
    border: 1px solid $color-vision-background-blu-dark;
    padding: 0.75rem 0.75rem;
    font-weight: 500;
    display: flex;
    align-items: center;
  }

  .ag-status-bar {
    background-color: $color-aggrid-statusbar;
    height:40px;
    align-items: center;
  }

  .aggrid-statusbar-messaging {
    width: 100%;
    margin-left:15px;
    display: inline;
  }

  .ag-search-input {
    height: 30px;
    background: $color-vision-white;
  }

  .ag-search-icon {
    margin: 0 4px 0 0;
    color: $color-rev-text;
  }

  .ag-copy-icon {
    &:hover {
      cursor: pointer;
    }
  }

  /* Overriding the bass padding of icons at 11% to center the icons within the grid. */
  .msg-icons{
    i {
      margin-top:4%;
    }
  }

  .messages-split {
    margin-top: 4px;
  }

  .ag-statusbar-renderer-index {
    z-index: 100;
    overflow: visible !important;
    width: 150px;
    margin-right: 15px;
    display: inline;
  }

  input, .p-inputtext {
    &, &:enabled:focus {
      height: 30px;
      border-radius: 4px;
      background: var(--surfacePrimary);
    }
    &, &:enabled:hover {
      border-top: 1px solid transparent !important;
      border-right: 1px solid transparent !important;
      border-left: 1px solid transparent !important;
    }
    &:enabled:focus {
      border-top: 1px solid rgba(0, 0, 0, 0.38) !important;
      border-right: 1px solid rgba(0, 0, 0, 0.38) !important;
      border-left: 1px solid rgba(0, 0, 0, 0.38) !important;
    }
    &::placeholder{
      color: var(--danger);
    }
    &[type="checkbox"]{
      height: 15px;
      width: 15px !important;
    }
  }

  .initial-row-pointer-events-auto{
    background-color: initial !important;
    pointer-events: auto;
    &.ag-row-odd {
      background-color: var(--surfaceSecondary) !important;
      &.updated-row-pointer-events-auto, &.updated-row-pointer-events-none{
        background-color: var(--surfaceYellow) !important;
      }
      &.has-errors {
        background-color: var(--surfaceRed) !important;
      }
    }
  }

  .updated-row-pointer-events-auto{
    background-color: var(--surfaceYellow) !important;
    pointer-events: auto;
  }

  .updated-row-pointer-events-none{
    background-color: var(--surfaceYellow) !important;
    pointer-events: none;
  }

  .has-errors {
    background-color: var(--surfaceRed) !important;
  }
}


.actionable-grid-config-dropdown .p-dropdown{
  width: 150px;
  height: 30px;
  vertical-align: middle;
  background: var(--surfacePrimary);
  border: 1px solid rgba(0, 0, 0, 0.38);
  transition: background-color 0.2s, border-color 0.2s, color 0.2s, box-shadow 0.2s, background-size 0.2s cubic-bezier(0.64, 0.09, 0.08, 1);
  border-radius: 4px;
  line-height: 30px;

  .p-inputtext{
    padding: 0px 10px;
    max-height: 100% !important;
  }

  .p-dropdown-trigger {
    margin-top: 4px;
  }
  .p-dropdown-label {
    padding: 0 0 0 6px!important;
    &.p-placeholder {
      margin: auto;
    }
  }
  &:not(.p-disabled).p-focus {
    border: 1px solid rgba(0, 0, 0, 0.38);
    color: var(--danger);
  }
}

.actionable-grid-config-dropdown-panel{
  width: -webkit-fill-available;
}
.alphine-p-highlight{
  li.p-dropdown-item.p-highlight{
    color:var(--danger) !important;
  }
}

.pi-status-circle-green{
  background-color: var(--successLight);
  height: 20px;
  width: 20px;
  border-radius: 50%;
  display: inline-block;
}

.pi-status-circle-red{
  background-color: var(--danger);
  height: 20px;
  width: 20px;
  border-radius: 50%;
  display: inline-block;
}

.pi-status-circle-amber{
  background-color: var(--warning);
  height: 20px;
  width: 20px;
  border-radius: 50%;
  display: inline-block;
}

.pi-status-circle-blue{
  background-color: var(--primaryActive);
  height: 20px;
  width: 20px;
  border-radius: 50%;
  display: inline-block;
}

body {
  app-message-symbol-renderer {
    .p-dropdown {
      padding-top: 6px;
      .p-dropdown-trigger {
        margin-top: 7px;
      }
    }
  }
}
.hide-dropdown-arrow {
  .p-dropdown-trigger {
    display: none;
  }
}

.p-dropdown-item.p-highlight.p-disabled.p-ripple {
  color: var(--danger) !important;
  opacity: 0.9;
}




body.saltbox-data-app.sbdapp,
body.saltbox-reporting.sbreport
{
  .ag-theme-material {
    input.p-inputtext{
      font-size: 13px !important;
    }
    .p-inputgroup-addon {
      p-button {
        display: flex;
        align-self: center;
        align-items: center;
      }
      .p-button {
        min-height: 1.5rem !important;
      }
    }
    .p-inputnumber .p-inputnumber-button-group .p-button {
      .p-inputnumber-button-up, .p-inputnumber-button-down {
        &, &:hover {
          margin-left: -1px !important;
        }
      }
    }
  }
}

.ag-custom-filter {
    .p-dropdown-label {
      height: 100%;
      max-height: 2rem;
      display: flex;
      align-items: center;
    }

    .p-dropdown {
      max-height: 2.2rem !important;
    }

    .p-card-body {
      padding: 0 !important;

      .p-card-content {
        padding: 0.75rem 0.5rem !important
      }
    }

    .p-inputnumber-input {
      min-width: 5rem !important;
      max-width: 5rem !important;
      height: 2.2rem !important;
    }

    .p-calendar {
      margin-top: 0.25rem !important;
    }

    .p-dropdown-item {
      padding: 0.25rem !important;
    }

    .p-hour-picker, .p-minute-picker, .p-second-picker, .p-datepicker-header {
      padding: 0 !important;
    } 

    .p-timepicker {
      padding-top: 0.25rem !important;
      padding-bottom: 0.25rem !important;
    }

    .p-datepicker-calendar td {
      padding: 0.25rem !important;
    }

    .p-datepicker-calendar, .p-datepicker-calendar td>span {
      margin: 0 !important;
    }

    .p-timepicker button {
      width: 2.2rem !important;
      height: 2.2rem !important;
    }

    .p-datepicker-buttonbar {
      padding-top: 0.5rem !important;
      padding-bottom: 0.5rem !important;
    }

    .p-timepicker span {
        font-size: 1rem !important;
    }
  }
