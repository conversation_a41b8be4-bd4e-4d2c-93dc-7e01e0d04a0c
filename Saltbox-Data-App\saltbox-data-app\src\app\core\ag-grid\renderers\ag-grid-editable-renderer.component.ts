import { Component, ElementRef, ViewChild } from '@angular/core';
import { formatDate } from 'src/app/shared/utilities/format.functions';
import { ICustomCellRendererParams } from './ag-grid-custom-cell-renderer-params';
import { CustomCellRenderer } from './ag-grid-custom-cell-renderer';
import { NotificationService } from '../../services/notification.service';
import { convertLocalDateToUTCDate, convertUTCDateToLocalDate, getDateByISO8601Format } from 'src/app/shared/utilities/date.functions';
import { EditorColumnType } from 'src/app/shared/enums/editor-column-type.enum';
import { ChangeHistoryService } from 'src/app/actionable-grid/services/change-history.service';
import { AGGridService } from '../../services/ag-grid.service';
import { CurrencySymbolPipe } from '../../../shared/pipes/currency-symbol.pipe';
import { InputNumberModule } from 'primeng/inputnumber';
import { CalendarModule } from 'primeng/calendar';
import { LookupSelectorComponent } from '../../../lookup/lookup-selector/lookup-selector.component';
import { InputTextModule } from 'primeng/inputtext';
import { FormsModule } from '@angular/forms';
import { TooltipModule } from 'primeng/tooltip';

@Component({
  selector: 'app-input-text-renderer',
  templateUrl: 'ag-grid-editable-renderer.component.html',
  standalone: true,
  imports: [TooltipModule, FormsModule, InputTextModule, LookupSelectorComponent, CalendarModule, InputNumberModule, CurrencySymbolPipe]
})
export class AgGridEditableRendererComponent extends CustomCellRenderer {
  @ViewChild('targetElement') elementRef;
  constructor(
    changeHistoryService: ChangeHistoryService,
    notificationService: NotificationService,
    aGGridService: AGGridService) {
    super(changeHistoryService, notificationService, aGGridService);
  }

  actionableGridColumnType = EditorColumnType;

  // This is used by p-calendar
  dateValue: Date;

  // This is used by p-inputNumber
  numericValue: number;

  // This is used by input
  stringValue: string;

  // Used by p-calendar and p-inputNumber to enable placeholder and tooltip that states the value is not valid.
  validValue = true;

  agInit(cellRendererParams: ICustomCellRendererParams): void {
    super.agInit(cellRendererParams);

    if (this.validateValue() && this.cellRendererParams.value != null)
      this.setInitialValue();
  }

  setLookupData(data: any) {
    if (data)
      super.groupUpdate(data);
  }

  setInitialValue() {
    switch (this.cellRendererParams.format.type) {
      case EditorColumnType.Date:
        this.dateValue = getDateByISO8601Format(this.cellRendererParams.value);
        break;
      case EditorColumnType.DateTime:
        // Note: currently we are ignoring the time zone, if we don't want to ignore the time zone it should be: new Date(this.cellRendererParams.value);
        this.dateValue = convertUTCDateToLocalDate(this.cellRendererParams.value);
        break;
      case EditorColumnType.Numeric:
      case EditorColumnType.Currency:
        this.numericValue = Number(this.cellRendererParams.value);
        break;
      case EditorColumnType.String:
        this.stringValue = this.cellRendererParams.value;
        break;
    }
  }

  onFocusOut() {
    // **Note: We use a short delay for the calendars because when the datepicker is open and the user is clicking on specific date, 
    // first onFocusOut happens then onChange and it resets the datetime to the initial value!! 
    // Also we don't run the same function with 0 timer because if the next editor is a datepicker it open twice
    if ([EditorColumnType.Date, EditorColumnType.DateTime].includes(this.cellRendererParams.format.type)) {
      setTimeout(() => {
        if (this.setParamValue())
          super.setData();
      }, 200);
    }
    else {
      if (this.setParamValue())
        super.setData();
    }
  }

  setParamValue(): boolean {
    switch (this.cellRendererParams.format.type) {
      case EditorColumnType.DateTime:
      case EditorColumnType.Date:
        // if user selected date and datarow dates are different
        if (this.cellRendererParams.value != this.dateValue &&
          this.dateValue?.toISOString() != convertUTCDateToLocalDate(this.cellRendererParams.value)?.toISOString()) {

          // Note: currently we are ignoring the time zone, if we don't want to ignore the time zone it should be returning: this.dateValue?.toISOString()
          this.cellRendererParams.value = this.cellRendererParams.format.type == EditorColumnType.DateTime
            ? this.dateValue ? convertLocalDateToUTCDate(this.dateValue) : undefined
            : this.dateValue ? formatDate(this.dateValue.toDateString()) : undefined;
          return true;
        }

        return false;
      case EditorColumnType.Numeric:
      case EditorColumnType.Currency:
        if (this.cellRendererParams.value != this.numericValue) {
          this.cellRendererParams.value = this.numericValue;
          return true;
        }

        return false;
      case EditorColumnType.String:
        if (this.cellRendererParams.value != this.stringValue?.trim()) {
          this.cellRendererParams.value = this.stringValue?.trim()
          return true;
        }

        return false;
    }
  }

  // This function is to check whether the value is valid according to it's type.
  validateValue(): boolean {
    // This is to check if the value is a Date.
    if (this.cellRendererParams.value != null && [EditorColumnType.Date, EditorColumnType.DateTime].includes(this.cellRendererParams.format.type)) {
      this.validValue = formatDate(this.cellRendererParams.value) !== 'Invalid date';
    }

    // This is to check if the value is a Number.
    if ([EditorColumnType.Numeric, EditorColumnType.Currency].includes(this.cellRendererParams.format.type)) {
      this.validValue = this.cellRendererParams.value != null && !isNaN(Number(this.cellRendererParams.value));
    }

    return this.validValue;
  }

  focusElement(): void {
    let elementRef: ElementRef;

    switch (this.cellRendererParams.format.type) {
      case EditorColumnType.String:
        elementRef = this.elementRef;
        break;
      case EditorColumnType.DateTime:
      case EditorColumnType.Date:
        elementRef = this.elementRef?.inputfieldViewChild;
        break;
      case EditorColumnType.Numeric:
      case EditorColumnType.Currency:
        elementRef = this.elementRef?.input;
        break;
    }

    elementRef?.nativeElement?.focus();
    elementRef?.nativeElement?.select();
  }

  onKeydownEvent(event: KeyboardEvent): void {
    // Stop propagation for certain keys
    if ((event.ctrlKey && ['C', 'c', 'V', 'v', 'X', 'x'].includes(event.key))
      || (['ArrowLeft', 'ArrowRight', 'ArrowUp', 'ArrowDown', 'Home', 'End'].includes(event.key))) {
      event.stopPropagation();
    }
  }
}
