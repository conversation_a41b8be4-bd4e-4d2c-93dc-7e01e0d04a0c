@if (!showEditor && cellRendererParams?.node?.group) {
       <div>
       {{ cellRendererParams?.valueFormatted ?? cellRendererParams?.value }}
       </div>
}
@if (showEditor) {
       <div class="actionable-grid-renderer" pTooltip="Invalid Value" tooltipPosition="top"
              [tooltipDisabled]="validValue">
              @if (cellRendererParams.format.type === actionableGridColumnType.String) {
                     <div class="inline-flex p-inputgroup">
                            <input #targetElement type="text" pInputText [(ngModel)]="stringValue" [required]="required"
                                   (focusout)="onFocusOut()" (keydown)="onKeydownEvent($event)">
                            @if(cellRendererParams.enableLookup && cellRendererParams.fieldBaseSchema?.presentationProperties?.enableLookup) {
                                   <app-lookup-selector
                                          [fieldName]="cellRendererParams.fieldName"
                                          [fieldDisplayName]="cellRendererParams.fieldDisplayName ?? cellRendererParams.fieldName"
                                          [fieldSchema]="cellRendererParams.fieldBaseSchema" (selectedDataChanged)="setLookupData($event)"
                                          class="p-inputgroup-addon" [projectVersionId]="cellRendererParams.projectVersionId"
                                          [selectedValue]="stringValue"
                                          [lookupConfig]="cellRendererParams.fieldBaseSchema?.presentationProperties?.lookupConfig"></app-lookup-selector>
                            }
                     </div>
              }
              @if ([actionableGridColumnType.Date, actionableGridColumnType.DateTime].includes(this.cellRendererParams.format.type)) {
                     <p-calendar #targetElement
                            [(ngModel)]="dateValue" appendTo="body" [showOtherMonths]="true" [selectOtherMonths]="true"
                            [showSeconds]="cellRendererParams.format.type === actionableGridColumnType.DateTime"
                            [showTime]="cellRendererParams.format.type === actionableGridColumnType.DateTime" [showWeek]="false"
                            [iconDisplay]="'input'" [showIcon]="true" [showOnFocus]="false" [monthNavigator]="true"
                            [yearNavigator]="true" yearRange="1900:2100" [readonlyInput]="false"
                            [placeholder]="validValue ? '' : cellRendererParams.value" dateFormat="mm/dd/yy" [required]="required"
                            (focusout)="onFocusOut()" (keydown)="onKeydownEvent($event)" (onHide)="onFocusOut()">
                     </p-calendar>
              }
              @if ([actionableGridColumnType.Currency, actionableGridColumnType.Numeric].includes(this.cellRendererParams.format.type)) {
                     <p-inputNumber #targetElement showButtons="true"
                            [required]="required" [(ngModel)]="numericValue" [prefix]="(cellRendererParams.format.type === actionableGridColumnType.Currency) ?
                                          (cellRendererParams.format.currency | currencySymbol) : ''"
                            [minFractionDigits]="cellRendererParams.format.decimalPlaces"
                            [maxFractionDigits]="cellRendererParams.format.decimalPlaces" (focusout)="onFocusOut()"
                            [placeholder]="validValue ? '':cellRendererParams.value" (keydown)="onKeydownEvent($event)">
                     </p-inputNumber>
              }
       </div>
}