import { formatNumber } from '@angular/common';
import moment from 'moment';
import { CurrencySymbolPipe } from 'src/app/shared/pipes/currency-symbol.pipe';
import { getDateByISO8601Format } from './date.functions';

export function formatDate(date: any, includeTime = false) {
  // null, empty strings and numbers are invalid values and will be treated as invalid date
  if (!date || !isNaN(Number(date)) || !moment(date).isValid()) {
    return 'Invalid date';
  }
  const utcDate = getDateByISO8601Format(date);
  return moment(utcDate).format(includeTime ? 'L HH:mm:ss' : 'L');
}

export function formatCurrency(value: any, currency: string, decimalPlaces: number, userLocale = 'en-US') {
  if (value === null || value === undefined) value = 0;

  const currencySymbolPipe: CurrencySymbolPipe = new CurrencySymbolPipe();
  if (currency && !isNaN(Number(value))) {
    const format = formatNumber(value, userLocale, `1.${decimalPlaces}-${decimalPlaces}`);
    return `${currencySymbolPipe.transform(currency)}${' '}${format}`;
  }
}

// This is being used by agGrid functionality
export function dateValueFormatter(date: any, includeTime = false) {
  if (!date || ['', 'NA', 'N/A'].indexOf(date.toString().trim()) > -1) {
    return '';
  }

  const formattedDate = formatDate(date, includeTime);
  return formattedDate !== 'Invalid date' ? formattedDate : date;
}

export function numericValueFormatter(value: any, decimalPlaces: number, userLocale = 'en-US'): string {
  if (value === null || value === undefined) value = 0;
  
  if (isNaN(Number(value))) return value;

  return formatNumber(value, userLocale, `1.${decimalPlaces}-${decimalPlaces}`);
}
