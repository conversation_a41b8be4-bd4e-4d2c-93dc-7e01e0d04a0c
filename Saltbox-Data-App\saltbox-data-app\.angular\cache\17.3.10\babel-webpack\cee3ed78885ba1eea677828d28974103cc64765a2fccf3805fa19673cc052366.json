{"ast": null, "code": "import { formatNumber } from '@angular/common';\nimport moment from 'moment';\nimport { CurrencySymbolPipe } from 'src/app/shared/pipes/currency-symbol.pipe';\nimport { getDateByISO8601Format } from './date.functions';\nexport function formatDate(date, includeTime = false) {\n  // null, empty strings and numbers are invalid values and will be treated as invalid date\n  if (!date || !isNaN(Number(date)) || !moment(date).isValid()) {\n    return 'Invalid date';\n  }\n  const utcDate = getDateByISO8601Format(date);\n  return moment(utcDate).format(includeTime ? 'L HH:mm:ss' : 'L');\n}\nexport function formatCurrency(value, currency, decimalPlaces, userLocale = 'en-US') {\n  if (value === null || value === undefined) value = 0;\n  const currencySymbolPipe = new CurrencySymbolPipe();\n  if (currency && !isNaN(Number(value))) {\n    const format = formatNumber(value, userLocale, `1.${decimalPlaces}-${decimalPlaces}`);\n    return `${currencySymbolPipe.transform(currency)}${' '}${format}`;\n  }\n}\n// This is being used by agGrid functionality\nexport function dateValueFormatter(date, includeTime = false) {\n  if (!date || ['', 'NA', 'N/A'].indexOf(date.toString().trim()) > -1) {\n    return '';\n  }\n  const formattedDate = formatDate(date, includeTime);\n  return formattedDate !== 'Invalid date' ? formattedDate : date;\n}\nexport function numericValueFormatter(value, decimalPlaces, userLocale = 'en-US') {\n  if (value === null || value === undefined) value = 0;\n  if (isNaN(Number(value))) return value;\n  return formatNumber(value, userLocale, `1.${decimalPlaces}-${decimalPlaces}`);\n}", "map": {"version": 3, "names": ["formatNumber", "moment", "CurrencySymbolPipe", "getDateByISO8601Format", "formatDate", "date", "includeTime", "isNaN", "Number", "<PERSON><PERSON><PERSON><PERSON>", "utcDate", "format", "formatCurrency", "value", "currency", "decimalPlaces", "userLocale", "undefined", "currencySymbolPipe", "transform", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "indexOf", "toString", "trim", "formattedDate", "numericValueFormatter"], "sources": ["C:\\Projects\\SaltboxActionableGrid\\Saltbox-Data-App\\saltbox-data-app\\src\\app\\shared\\utilities\\format.functions.ts"], "sourcesContent": ["import { formatNumber } from '@angular/common';\r\nimport moment from 'moment';\r\nimport { CurrencySymbolPipe } from 'src/app/shared/pipes/currency-symbol.pipe';\r\nimport { getDateByISO8601Format } from './date.functions';\r\n\r\nexport function formatDate(date: any, includeTime = false) {\r\n  // null, empty strings and numbers are invalid values and will be treated as invalid date\r\n  if (!date || !isNaN(Number(date)) || !moment(date).isValid()) {\r\n    return 'Invalid date';\r\n  }\r\n  const utcDate = getDateByISO8601Format(date);\r\n  return moment(utcDate).format(includeTime ? 'L HH:mm:ss' : 'L');\r\n}\r\n\r\nexport function formatCurrency(value: any, currency: string, decimalPlaces: number, userLocale = 'en-US') {\r\n  if (value === null || value === undefined) value = 0;\r\n\r\n  const currencySymbolPipe: CurrencySymbolPipe = new CurrencySymbolPipe();\r\n  if (currency && !isNaN(Number(value))) {\r\n    const format = formatNumber(value, userLocale, `1.${decimalPlaces}-${decimalPlaces}`);\r\n    return `${currencySymbolPipe.transform(currency)}${' '}${format}`;\r\n  }\r\n}\r\n\r\n// This is being used by agGrid functionality\r\nexport function dateValueFormatter(date: any, includeTime = false) {\r\n  if (!date || ['', 'NA', 'N/A'].indexOf(date.toString().trim()) > -1) {\r\n    return '';\r\n  }\r\n\r\n  const formattedDate = formatDate(date, includeTime);\r\n  return formattedDate !== 'Invalid date' ? formattedDate : date;\r\n}\r\n\r\nexport function numericValueFormatter(value: any, decimalPlaces: number, userLocale = 'en-US'): string {\r\n  if (value === null || value === undefined) value = 0;\r\n  \r\n  if (isNaN(Number(value))) return value;\r\n\r\n  return formatNumber(value, userLocale, `1.${decimalPlaces}-${decimalPlaces}`);\r\n}\r\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,OAAOC,MAAM,MAAM,QAAQ;AAC3B,SAASC,kBAAkB,QAAQ,2CAA2C;AAC9E,SAASC,sBAAsB,QAAQ,kBAAkB;AAEzD,OAAM,SAAUC,UAAUA,CAACC,IAAS,EAAEC,WAAW,GAAG,KAAK;EACvD;EACA,IAAI,CAACD,IAAI,IAAI,CAACE,KAAK,CAACC,MAAM,CAACH,IAAI,CAAC,CAAC,IAAI,CAACJ,MAAM,CAACI,IAAI,CAAC,CAACI,OAAO,EAAE,EAAE;IAC5D,OAAO,cAAc;EACvB;EACA,MAAMC,OAAO,GAAGP,sBAAsB,CAACE,IAAI,CAAC;EAC5C,OAAOJ,MAAM,CAACS,OAAO,CAAC,CAACC,MAAM,CAACL,WAAW,GAAG,YAAY,GAAG,GAAG,CAAC;AACjE;AAEA,OAAM,SAAUM,cAAcA,CAACC,KAAU,EAAEC,QAAgB,EAAEC,aAAqB,EAAEC,UAAU,GAAG,OAAO;EACtG,IAAIH,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAKI,SAAS,EAAEJ,KAAK,GAAG,CAAC;EAEpD,MAAMK,kBAAkB,GAAuB,IAAIhB,kBAAkB,EAAE;EACvE,IAAIY,QAAQ,IAAI,CAACP,KAAK,CAACC,MAAM,CAACK,KAAK,CAAC,CAAC,EAAE;IACrC,MAAMF,MAAM,GAAGX,YAAY,CAACa,KAAK,EAAEG,UAAU,EAAE,KAAKD,aAAa,IAAIA,aAAa,EAAE,CAAC;IACrF,OAAO,GAAGG,kBAAkB,CAACC,SAAS,CAACL,QAAQ,CAAC,GAAG,GAAG,GAAGH,MAAM,EAAE;EACnE;AACF;AAEA;AACA,OAAM,SAAUS,kBAAkBA,CAACf,IAAS,EAAEC,WAAW,GAAG,KAAK;EAC/D,IAAI,CAACD,IAAI,IAAI,CAAC,EAAE,EAAE,IAAI,EAAE,KAAK,CAAC,CAACgB,OAAO,CAAChB,IAAI,CAACiB,QAAQ,EAAE,CAACC,IAAI,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE;IACnE,OAAO,EAAE;EACX;EAEA,MAAMC,aAAa,GAAGpB,UAAU,CAACC,IAAI,EAAEC,WAAW,CAAC;EACnD,OAAOkB,aAAa,KAAK,cAAc,GAAGA,aAAa,GAAGnB,IAAI;AAChE;AAEA,OAAM,SAAUoB,qBAAqBA,CAACZ,KAAU,EAAEE,aAAqB,EAAEC,UAAU,GAAG,OAAO;EAC3F,IAAIH,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAKI,SAAS,EAAEJ,KAAK,GAAG,CAAC;EAEpD,IAAIN,KAAK,CAACC,MAAM,CAACK,KAAK,CAAC,CAAC,EAAE,OAAOA,KAAK;EAEtC,OAAOb,YAAY,CAACa,KAAK,EAAEG,UAAU,EAAE,KAAKD,aAAa,IAAIA,aAAa,EAAE,CAAC;AAC/E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}