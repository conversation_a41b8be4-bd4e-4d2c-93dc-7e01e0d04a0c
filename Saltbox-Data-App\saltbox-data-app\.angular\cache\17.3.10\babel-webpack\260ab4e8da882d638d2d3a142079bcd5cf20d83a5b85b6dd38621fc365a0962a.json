{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component, ViewChild } from '@angular/core';\nimport { CustomCellRenderer } from './ag-grid-custom-cell-renderer';\nimport { FormsModule } from '@angular/forms';\nimport { DropdownModule } from 'primeng/dropdown';\nlet AgGridDropDownRendererComponent = class AgGridDropDownRendererComponent extends CustomCellRenderer {\n  constructor(changeHistoryService, notificationService, aGGridService) {\n    super(changeHistoryService, notificationService, aGGridService);\n    this.dropdownArray = [];\n  }\n  agInit(cellRendererParams) {\n    super.agInit(cellRendererParams);\n    this.cellRendererParams = cellRendererParams;\n    // ensures the current value is in the dropdown if it is not currently an option\n    if (this.cellRendererParams.value !== undefined && this.cellRendererParams.value !== '' && !this.cellRendererParams.allowedValues.find(x => x.value === this.cellRendererParams.value)) {\n      this.dropdownArray.push({\n        label: this.cellRendererParams.value,\n        value: this.cellRendererParams.value,\n        disabled: !this.cellRendererParams.allowedValues.find(x => x.value === this.cellRendererParams.value)\n      });\n    }\n    for (const allowedValue of this.cellRendererParams.allowedValues) {\n      if (!this.dropdownArray.find(v => v.value === allowedValue.value)) {\n        this.dropdownArray.push({\n          label: allowedValue.value,\n          value: allowedValue.value,\n          disabled: !this.cellRendererParams.allowUserUpdate\n        });\n      }\n    }\n  }\n  focusElement() {\n    this.elementRef?.focus();\n  }\n  onChangeData() {\n    super.setData();\n  }\n};\n__decorate([ViewChild('targetElement')], AgGridDropDownRendererComponent.prototype, \"elementRef\", void 0);\nAgGridDropDownRendererComponent = __decorate([Component({\n  selector: 'app-dropdown-renderer',\n  templateUrl: './ag-grid-dropdown-renderer.component.html',\n  standalone: true,\n  imports: [DropdownModule, FormsModule]\n})], AgGridDropDownRendererComponent);\nexport { AgGridDropDownRendererComponent };", "map": {"version": 3, "names": ["Component", "ViewChild", "CustomCellRenderer", "FormsModule", "DropdownModule", "AgGridDropDownRendererComponent", "constructor", "changeHistoryService", "notificationService", "aGGridService", "dropdownArray", "agInit", "cellRendererParams", "value", "undefined", "<PERSON><PERSON><PERSON><PERSON>", "find", "x", "push", "label", "disabled", "allowedValue", "v", "allowUserUpdate", "focusElement", "elementRef", "focus", "onChangeData", "setData", "__decorate", "selector", "templateUrl", "standalone", "imports"], "sources": ["C:\\Projects\\SaltboxActionableGrid\\Saltbox-Data-App\\saltbox-data-app\\src\\app\\core\\ag-grid\\renderers\\ag-grid-dropdown-renderer.component.ts"], "sourcesContent": ["import { Component, ViewChild } from '@angular/core';\r\nimport { ICustomCellRendererParams } from './ag-grid-custom-cell-renderer-params';\r\nimport { CustomCellRenderer } from './ag-grid-custom-cell-renderer';\r\nimport { NotificationService } from '../../services/notification.service';\r\nimport { ChangeHistoryService } from 'src/app/actionable-grid/services/change-history.service';\r\nimport { AGGridService } from '../../services/ag-grid.service';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { DropdownModule } from 'primeng/dropdown';\r\n\r\n@Component({\r\n    selector: 'app-dropdown-renderer',\r\n    templateUrl: './ag-grid-dropdown-renderer.component.html',\r\n    standalone: true,\r\n    imports: [DropdownModule, FormsModule]\r\n})\r\nexport class AgGridDropDownRendererComponent extends CustomCellRenderer {\r\n    @ViewChild('targetElement') elementRef;\r\n    constructor(\r\n        changeHistoryService: ChangeHistoryService,\r\n        notificationService: NotificationService,\r\n        aGGridService: AGGridService) {\r\n        super(changeHistoryService, notificationService, aGGridService);\r\n      }\r\n\r\n    dropdownArray = [];\r\n\r\n    agInit(cellRendererParams: ICustomCellRendererParams): void {\r\n        super.agInit(cellRendererParams);\r\n        this.cellRendererParams = cellRendererParams;\r\n\r\n        // ensures the current value is in the dropdown if it is not currently an option\r\n        if (this.cellRendererParams.value !== undefined && this.cellRendererParams.value !== '' && !this.cellRendererParams.allowedValues.find(x => x.value === this.cellRendererParams.value)) {\r\n            this.dropdownArray.push({\r\n                label: this.cellRendererParams.value,\r\n                value: this.cellRendererParams.value,\r\n                disabled: !this.cellRendererParams.allowedValues.find(x => x.value === this.cellRendererParams.value)\r\n            });\r\n        }\r\n\r\n        for (const allowedValue of this.cellRendererParams.allowedValues) {\r\n            if (!this.dropdownArray.find(v => v.value === allowedValue.value)) {\r\n                this.dropdownArray.push({\r\n                    label: allowedValue.value,\r\n                    value: allowedValue.value,\r\n                    disabled: !this.cellRendererParams.allowUserUpdate\r\n                });\r\n            }\r\n        }\r\n    }\r\n\r\n    focusElement(): void {\r\n        this.elementRef?.focus();\r\n    }\r\n\r\n    onChangeData() {\r\n        super.setData();\r\n    }\r\n}\r\n"], "mappings": ";AAAA,SAASA,SAAS,EAAEC,SAAS,QAAQ,eAAe;AAEpD,SAASC,kBAAkB,QAAQ,gCAAgC;AAInE,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,cAAc,QAAQ,kBAAkB;AAQ1C,IAAMC,+BAA+B,GAArC,MAAMA,+BAAgC,SAAQH,kBAAkB;EAEnEI,YACIC,oBAA0C,EAC1CC,mBAAwC,EACxCC,aAA4B;IAC5B,KAAK,CAACF,oBAAoB,EAAEC,mBAAmB,EAAEC,aAAa,CAAC;IAGnE,KAAAC,aAAa,GAAG,EAAE;EAFhB;EAIFC,MAAMA,CAACC,kBAA6C;IAChD,KAAK,CAACD,MAAM,CAACC,kBAAkB,CAAC;IAChC,IAAI,CAACA,kBAAkB,GAAGA,kBAAkB;IAE5C;IACA,IAAI,IAAI,CAACA,kBAAkB,CAACC,KAAK,KAAKC,SAAS,IAAI,IAAI,CAACF,kBAAkB,CAACC,KAAK,KAAK,EAAE,IAAI,CAAC,IAAI,CAACD,kBAAkB,CAACG,aAAa,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACJ,KAAK,KAAK,IAAI,CAACD,kBAAkB,CAACC,KAAK,CAAC,EAAE;MACpL,IAAI,CAACH,aAAa,CAACQ,IAAI,CAAC;QACpBC,KAAK,EAAE,IAAI,CAACP,kBAAkB,CAACC,KAAK;QACpCA,KAAK,EAAE,IAAI,CAACD,kBAAkB,CAACC,KAAK;QACpCO,QAAQ,EAAE,CAAC,IAAI,CAACR,kBAAkB,CAACG,aAAa,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACJ,KAAK,KAAK,IAAI,CAACD,kBAAkB,CAACC,KAAK;OACvG,CAAC;IACN;IAEA,KAAK,MAAMQ,YAAY,IAAI,IAAI,CAACT,kBAAkB,CAACG,aAAa,EAAE;MAC9D,IAAI,CAAC,IAAI,CAACL,aAAa,CAACM,IAAI,CAACM,CAAC,IAAIA,CAAC,CAACT,KAAK,KAAKQ,YAAY,CAACR,KAAK,CAAC,EAAE;QAC/D,IAAI,CAACH,aAAa,CAACQ,IAAI,CAAC;UACpBC,KAAK,EAAEE,YAAY,CAACR,KAAK;UACzBA,KAAK,EAAEQ,YAAY,CAACR,KAAK;UACzBO,QAAQ,EAAE,CAAC,IAAI,CAACR,kBAAkB,CAACW;SACtC,CAAC;MACN;IACJ;EACJ;EAEAC,YAAYA,CAAA;IACR,IAAI,CAACC,UAAU,EAAEC,KAAK,EAAE;EAC5B;EAEAC,YAAYA,CAAA;IACR,KAAK,CAACC,OAAO,EAAE;EACnB;CACH;AAzC+BC,UAAA,EAA3B5B,SAAS,CAAC,eAAe,CAAC,C,kEAAY;AAD9BI,+BAA+B,GAAAwB,UAAA,EAN3C7B,SAAS,CAAC;EACP8B,QAAQ,EAAE,uBAAuB;EACjCC,WAAW,EAAE,4CAA4C;EACzDC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CAAC7B,cAAc,EAAED,WAAW;CACxC,CAAC,C,EACWE,+BAA+B,CA0C3C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}