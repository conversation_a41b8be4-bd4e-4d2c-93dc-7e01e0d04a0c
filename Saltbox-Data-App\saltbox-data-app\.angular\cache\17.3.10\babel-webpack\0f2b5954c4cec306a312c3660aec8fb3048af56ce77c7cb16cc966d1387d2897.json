{"ast": null, "code": "import { CustomCellRenderer } from './ag-grid-custom-cell-renderer';\nimport { FormsModule } from '@angular/forms';\nimport { DropdownModule } from 'primeng/dropdown';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/actionable-grid/services/change-history.service\";\nimport * as i2 from \"../../services/notification.service\";\nimport * as i3 from \"../../services/ag-grid.service\";\nimport * as i4 from \"primeng/dropdown\";\nimport * as i5 from \"@angular/forms\";\nconst _c0 = [\"targetElement\"];\nfunction AgGridDropDownRendererComponent_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    let tmp_1_0;\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (tmp_1_0 = ctx_r0.cellRendererParams == null ? null : ctx_r0.cellRendererParams.valueFormatted) !== null && tmp_1_0 !== undefined ? tmp_1_0 : ctx_r0.cellRendererParams == null ? null : ctx_r0.cellRendererParams.value, \" \");\n  }\n}\nfunction AgGridDropDownRendererComponent_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 1)(1, \"p-dropdown\", 2, 0);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function AgGridDropDownRendererComponent_Conditional_1_Template_p_dropdown_ngModelChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r0.cellRendererParams.value, $event) || (ctx_r0.cellRendererParams.value = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"onChange\", function AgGridDropDownRendererComponent_Conditional_1_Template_p_dropdown_onChange_1_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.onChangeData());\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"options\", ctx_r0.dropdownArray);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r0.cellRendererParams.value);\n    i0.ɵɵproperty(\"required\", ctx_r0.required);\n  }\n}\nexport class AgGridDropDownRendererComponent extends CustomCellRenderer {\n  constructor(changeHistoryService, notificationService, aGGridService) {\n    super(changeHistoryService, notificationService, aGGridService);\n    this.dropdownArray = [];\n  }\n  agInit(cellRendererParams) {\n    super.agInit(cellRendererParams);\n    this.cellRendererParams = cellRendererParams;\n    // ensures the current value is in the dropdown if it is not currently an option\n    if (this.cellRendererParams.value !== undefined && this.cellRendererParams.value !== '' && !this.cellRendererParams.allowedValues.find(x => x.value === this.cellRendererParams.value)) {\n      this.dropdownArray.push({\n        label: this.cellRendererParams.value,\n        value: this.cellRendererParams.value,\n        disabled: !this.cellRendererParams.allowedValues.find(x => x.value === this.cellRendererParams.value)\n      });\n    }\n    for (const allowedValue of this.cellRendererParams.allowedValues) {\n      if (!this.dropdownArray.find(v => v.value === allowedValue.value)) {\n        this.dropdownArray.push({\n          label: allowedValue.value,\n          value: allowedValue.value,\n          disabled: !this.cellRendererParams.allowUserUpdate\n        });\n      }\n    }\n  }\n  focusElement() {\n    this.elementRef?.focus();\n  }\n  onChangeData() {\n    super.setData();\n  }\n  static {\n    this.ɵfac = function AgGridDropDownRendererComponent_Factory(t) {\n      return new (t || AgGridDropDownRendererComponent)(i0.ɵɵdirectiveInject(i1.ChangeHistoryService), i0.ɵɵdirectiveInject(i2.NotificationService), i0.ɵɵdirectiveInject(i3.AGGridService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AgGridDropDownRendererComponent,\n      selectors: [[\"app-dropdown-renderer\"]],\n      viewQuery: function AgGridDropDownRendererComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.elementRef = _t.first);\n        }\n      },\n      standalone: true,\n      features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n      decls: 2,\n      vars: 2,\n      consts: [[\"targetElement\", \"\"], [1, \"actionable-grid-config-dropdown\"], [\"panelStyleClass\", \"actionable-grid-config-dropdown-panel\", \"styleClass\", \"alphine-p-highlight\", \"appendTo\", \"body\", \"placeholder\", \" \", \"optionLabel\", \"value\", \"optionValue\", \"value\", 3, \"ngModelChange\", \"onChange\", \"options\", \"ngModel\", \"required\"]],\n      template: function AgGridDropDownRendererComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, AgGridDropDownRendererComponent_Conditional_0_Template, 2, 1, \"div\")(1, AgGridDropDownRendererComponent_Conditional_1_Template, 3, 3, \"div\", 1);\n        }\n        if (rf & 2) {\n          i0.ɵɵconditional(0, !ctx.showEditor && (ctx.cellRendererParams == null ? null : ctx.cellRendererParams.node == null ? null : ctx.cellRendererParams.node.group) ? 0 : -1);\n          i0.ɵɵadvance();\n          i0.ɵɵconditional(1, ctx.showEditor ? 1 : -1);\n        }\n      },\n      dependencies: [DropdownModule, i4.Dropdown, FormsModule, i5.NgControlStatus, i5.RequiredValidator, i5.NgModel],\n      encapsulation: 2\n    });\n  }\n}", "map": {"version": 3, "names": ["CustomCellRenderer", "FormsModule", "DropdownModule", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "tmp_1_0", "ctx_r0", "cellRendererParams", "valueFormatted", "undefined", "value", "ɵɵtwoWayListener", "AgGridDropDownRendererComponent_Conditional_1_Template_p_dropdown_ngModelChange_1_listener", "$event", "ɵɵrestoreView", "_r2", "ɵɵnextContext", "ɵɵtwoWayBindingSet", "ɵɵresetView", "ɵɵlistener", "AgGridDropDownRendererComponent_Conditional_1_Template_p_dropdown_onChange_1_listener", "onChangeData", "ɵɵproperty", "dropdownArray", "ɵɵtwoWayProperty", "required", "AgGridDropDownRendererComponent", "constructor", "changeHistoryService", "notificationService", "aGGridService", "agInit", "<PERSON><PERSON><PERSON><PERSON>", "find", "x", "push", "label", "disabled", "allowedValue", "v", "allowUserUpdate", "focusElement", "elementRef", "focus", "setData", "ɵɵdirectiveInject", "i1", "ChangeHistoryService", "i2", "NotificationService", "i3", "AGGridService", "selectors", "viewQuery", "AgGridDropDownRendererComponent_Query", "rf", "ctx", "ɵɵtemplate", "AgGridDropDownRendererComponent_Conditional_0_Template", "AgGridDropDownRendererComponent_Conditional_1_Template", "ɵɵconditional", "showEditor", "node", "group", "i4", "Dropdown", "i5", "NgControlStatus", "RequiredValidator", "NgModel", "encapsulation"], "sources": ["C:\\Projects\\SaltboxActionableGrid\\Saltbox-Data-App\\saltbox-data-app\\src\\app\\core\\ag-grid\\renderers\\ag-grid-dropdown-renderer.component.ts", "C:\\Projects\\SaltboxActionableGrid\\Saltbox-Data-App\\saltbox-data-app\\src\\app\\core\\ag-grid\\renderers\\ag-grid-dropdown-renderer.component.html"], "sourcesContent": ["import { Component, ViewChild } from '@angular/core';\r\nimport { ICustomCellRendererParams } from './ag-grid-custom-cell-renderer-params';\r\nimport { CustomCellRenderer } from './ag-grid-custom-cell-renderer';\r\nimport { NotificationService } from '../../services/notification.service';\r\nimport { ChangeHistoryService } from 'src/app/actionable-grid/services/change-history.service';\r\nimport { AGGridService } from '../../services/ag-grid.service';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { DropdownModule } from 'primeng/dropdown';\r\n\r\n@Component({\r\n    selector: 'app-dropdown-renderer',\r\n    templateUrl: './ag-grid-dropdown-renderer.component.html',\r\n    standalone: true,\r\n    imports: [DropdownModule, FormsModule]\r\n})\r\nexport class AgGridDropDownRendererComponent extends CustomCellRenderer {\r\n    @ViewChild('targetElement') elementRef;\r\n    constructor(\r\n        changeHistoryService: ChangeHistoryService,\r\n        notificationService: NotificationService,\r\n        aGGridService: AGGridService) {\r\n        super(changeHistoryService, notificationService, aGGridService);\r\n      }\r\n\r\n    dropdownArray = [];\r\n\r\n    agInit(cellRendererParams: ICustomCellRendererParams): void {\r\n        super.agInit(cellRendererParams);\r\n        this.cellRendererParams = cellRendererParams;\r\n\r\n        // ensures the current value is in the dropdown if it is not currently an option\r\n        if (this.cellRendererParams.value !== undefined && this.cellRendererParams.value !== '' && !this.cellRendererParams.allowedValues.find(x => x.value === this.cellRendererParams.value)) {\r\n            this.dropdownArray.push({\r\n                label: this.cellRendererParams.value,\r\n                value: this.cellRendererParams.value,\r\n                disabled: !this.cellRendererParams.allowedValues.find(x => x.value === this.cellRendererParams.value)\r\n            });\r\n        }\r\n\r\n        for (const allowedValue of this.cellRendererParams.allowedValues) {\r\n            if (!this.dropdownArray.find(v => v.value === allowedValue.value)) {\r\n                this.dropdownArray.push({\r\n                    label: allowedValue.value,\r\n                    value: allowedValue.value,\r\n                    disabled: !this.cellRendererParams.allowUserUpdate\r\n                });\r\n            }\r\n        }\r\n    }\r\n\r\n    focusElement(): void {\r\n        this.elementRef?.focus();\r\n    }\r\n\r\n    onChangeData() {\r\n        super.setData();\r\n    }\r\n}\r\n", "@if (!showEditor && cellRendererParams?.node?.group) {\r\n  <div>\r\n  {{ cellRendererParams?.valueFormatted ?? cellRendererParams?.value }}\r\n  </div>\r\n}\r\n@if (showEditor) {\r\n    <div class=\"actionable-grid-config-dropdown\">\r\n        <p-dropdown #targetElement panelStyleClass=\"actionable-grid-config-dropdown-panel\" styleClass=\"alphine-p-highlight\"\r\n            [options]=\"dropdownArray\" [(ngModel)]=\"cellRendererParams.value\" appendTo=\"body\" placeholder=\" \"\r\n            optionLabel=\"value\" optionValue=\"value\" [required]=\"required\" (onChange)=onChangeData()>\r\n        </p-dropdown>\r\n    </div>\r\n}"], "mappings": "AAEA,SAASA,kBAAkB,QAAQ,gCAAgC;AAInE,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,cAAc,QAAQ,kBAAkB;;;;;;;;;;ICN/CC,EAAA,CAAAC,cAAA,UAAK;IACLD,EAAA,CAAAE,MAAA,GACA;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IADNH,EAAA,CAAAI,SAAA,EACA;IADAJ,EAAA,CAAAK,kBAAA,OAAAC,OAAA,GAAAC,MAAA,CAAAC,kBAAA,kBAAAD,MAAA,CAAAC,kBAAA,CAAAC,cAAA,cAAAH,OAAA,KAAAI,SAAA,GAAAJ,OAAA,GAAAC,MAAA,CAAAC,kBAAA,kBAAAD,MAAA,CAAAC,kBAAA,CAAAG,KAAA,MACA;;;;;;IAIMX,EADJ,CAAAC,cAAA,aAA6C,uBAGmD;IAD9DD,EAAA,CAAAY,gBAAA,2BAAAC,2FAAAC,MAAA;MAAAd,EAAA,CAAAe,aAAA,CAAAC,GAAA;MAAA,MAAAT,MAAA,GAAAP,EAAA,CAAAiB,aAAA;MAAAjB,EAAA,CAAAkB,kBAAA,CAAAX,MAAA,CAAAC,kBAAA,CAAAG,KAAA,EAAAG,MAAA,MAAAP,MAAA,CAAAC,kBAAA,CAAAG,KAAA,GAAAG,MAAA;MAAA,OAAAd,EAAA,CAAAmB,WAAA,CAAAL,MAAA;IAAA,EAAsC;IACFd,EAAA,CAAAoB,UAAA,sBAAAC,sFAAA;MAAArB,EAAA,CAAAe,aAAA,CAAAC,GAAA;MAAA,MAAAT,MAAA,GAAAP,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAmB,WAAA,CAAWZ,MAAA,CAAAe,YAAA,EAAc;IAAA;IAE/FtB,EADI,CAAAG,YAAA,EAAa,EACX;;;;IAHEH,EAAA,CAAAI,SAAA,EAAyB;IAAzBJ,EAAA,CAAAuB,UAAA,YAAAhB,MAAA,CAAAiB,aAAA,CAAyB;IAACxB,EAAA,CAAAyB,gBAAA,YAAAlB,MAAA,CAAAC,kBAAA,CAAAG,KAAA,CAAsC;IACxBX,EAAA,CAAAuB,UAAA,aAAAhB,MAAA,CAAAmB,QAAA,CAAqB;;;ADMzE,OAAM,MAAOC,+BAAgC,SAAQ9B,kBAAkB;EAEnE+B,YACIC,oBAA0C,EAC1CC,mBAAwC,EACxCC,aAA4B;IAC5B,KAAK,CAACF,oBAAoB,EAAEC,mBAAmB,EAAEC,aAAa,CAAC;IAGnE,KAAAP,aAAa,GAAG,EAAE;EAFhB;EAIFQ,MAAMA,CAACxB,kBAA6C;IAChD,KAAK,CAACwB,MAAM,CAACxB,kBAAkB,CAAC;IAChC,IAAI,CAACA,kBAAkB,GAAGA,kBAAkB;IAE5C;IACA,IAAI,IAAI,CAACA,kBAAkB,CAACG,KAAK,KAAKD,SAAS,IAAI,IAAI,CAACF,kBAAkB,CAACG,KAAK,KAAK,EAAE,IAAI,CAAC,IAAI,CAACH,kBAAkB,CAACyB,aAAa,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACxB,KAAK,KAAK,IAAI,CAACH,kBAAkB,CAACG,KAAK,CAAC,EAAE;MACpL,IAAI,CAACa,aAAa,CAACY,IAAI,CAAC;QACpBC,KAAK,EAAE,IAAI,CAAC7B,kBAAkB,CAACG,KAAK;QACpCA,KAAK,EAAE,IAAI,CAACH,kBAAkB,CAACG,KAAK;QACpC2B,QAAQ,EAAE,CAAC,IAAI,CAAC9B,kBAAkB,CAACyB,aAAa,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACxB,KAAK,KAAK,IAAI,CAACH,kBAAkB,CAACG,KAAK;OACvG,CAAC;IACN;IAEA,KAAK,MAAM4B,YAAY,IAAI,IAAI,CAAC/B,kBAAkB,CAACyB,aAAa,EAAE;MAC9D,IAAI,CAAC,IAAI,CAACT,aAAa,CAACU,IAAI,CAACM,CAAC,IAAIA,CAAC,CAAC7B,KAAK,KAAK4B,YAAY,CAAC5B,KAAK,CAAC,EAAE;QAC/D,IAAI,CAACa,aAAa,CAACY,IAAI,CAAC;UACpBC,KAAK,EAAEE,YAAY,CAAC5B,KAAK;UACzBA,KAAK,EAAE4B,YAAY,CAAC5B,KAAK;UACzB2B,QAAQ,EAAE,CAAC,IAAI,CAAC9B,kBAAkB,CAACiC;SACtC,CAAC;MACN;IACJ;EACJ;EAEAC,YAAYA,CAAA;IACR,IAAI,CAACC,UAAU,EAAEC,KAAK,EAAE;EAC5B;EAEAtB,YAAYA,CAAA;IACR,KAAK,CAACuB,OAAO,EAAE;EACnB;;;uBAzCSlB,+BAA+B,EAAA3B,EAAA,CAAA8C,iBAAA,CAAAC,EAAA,CAAAC,oBAAA,GAAAhD,EAAA,CAAA8C,iBAAA,CAAAG,EAAA,CAAAC,mBAAA,GAAAlD,EAAA,CAAA8C,iBAAA,CAAAK,EAAA,CAAAC,aAAA;IAAA;EAAA;;;YAA/BzB,+BAA+B;MAAA0B,SAAA;MAAAC,SAAA,WAAAC,sCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;;;UCV5CxD,EALA,CAAA0D,UAAA,IAAAC,sDAAA,cAAsD,IAAAC,sDAAA,iBAKpC;;;UALlB5D,EAAA,CAAA6D,aAAA,KAAAJ,GAAA,CAAAK,UAAA,KAAAL,GAAA,CAAAjD,kBAAA,kBAAAiD,GAAA,CAAAjD,kBAAA,CAAAuD,IAAA,kBAAAN,GAAA,CAAAjD,kBAAA,CAAAuD,IAAA,CAAAC,KAAA,WAIC;UACDhE,EAAA,CAAAI,SAAA,EAOC;UAPDJ,EAAA,CAAA6D,aAAA,IAAAJ,GAAA,CAAAK,UAAA,UAOC;;;qBDCa/D,cAAc,EAAAkE,EAAA,CAAAC,QAAA,EAAEpE,WAAW,EAAAqE,EAAA,CAAAC,eAAA,EAAAD,EAAA,CAAAE,iBAAA,EAAAF,EAAA,CAAAG,OAAA;MAAAC,aAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}