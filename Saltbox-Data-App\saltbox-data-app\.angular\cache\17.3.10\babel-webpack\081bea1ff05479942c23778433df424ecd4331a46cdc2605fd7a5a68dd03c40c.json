{"ast": null, "code": "import { CustomCellRenderer } from './ag-grid-custom-cell-renderer';\nimport { JsonDataTypes } from 'src/app/shared/enums/json-schema.enum';\nimport { FormsModule } from '@angular/forms';\nimport { CheckboxModule } from 'primeng/checkbox';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/actionable-grid/services/change-history.service\";\nimport * as i2 from \"../../services/notification.service\";\nimport * as i3 from \"../../services/ag-grid.service\";\nimport * as i4 from \"primeng/checkbox\";\nimport * as i5 from \"@angular/forms\";\nconst _c0 = [\"targetElement\"];\nfunction AgGridCheckBoxRendererComponent_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    let tmp_1_0;\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (tmp_1_0 = ctx_r0.cellRendererParams == null ? null : ctx_r0.cellRendererParams.valueFormatted) !== null && tmp_1_0 !== undefined ? tmp_1_0 : ctx_r0.cellRendererParams == null ? null : ctx_r0.cellRendererParams.value, \" \");\n  }\n}\nfunction AgGridCheckBoxRendererComponent_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 1)(1, \"p-checkbox\", 2, 0);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function AgGridCheckBoxRendererComponent_Conditional_1_Template_p_checkbox_ngModelChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r0.checked, $event) || (ctx_r0.checked = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"onChange\", function AgGridCheckBoxRendererComponent_Conditional_1_Template_p_checkbox_onChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.onCheckboxChange($event));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r0.checked);\n    i0.ɵɵproperty(\"binary\", true)(\"disabled\", !ctx_r0.cellRendererParams.allowUserUpdate);\n  }\n}\n/* for format config driven checkbox only*/\nexport class AgGridCheckBoxRendererComponent extends CustomCellRenderer {\n  constructor(changeHistoryService, notificationService, aGGridService) {\n    super(changeHistoryService, notificationService, aGGridService);\n    this.checked = false;\n  }\n  agInit(cellRendererParams) {\n    super.agInit(cellRendererParams);\n    const matchResult = this.cellRendererParams.value;\n    if (this.cellRendererParams.format.baseType === JsonDataTypes.String) {\n      const resultIndex = this.cellRendererParams.allowedValues.findIndex(x => x.value.toString().toLowerCase() === matchResult?.toString().toLowerCase());\n      this.checked = resultIndex > -1 ? this.cellRendererParams.allowedValues[resultIndex].key.toString().toLowerCase() === 'checked' : this.cellRendererParams.allowedValues.find(x => x.isDefault === true).key.toString().toLowerCase() === 'checked';\n    } else {\n      this.checked = matchResult === true || matchResult === 'true';\n    }\n  }\n  onCheckboxChange(event) {\n    let value;\n    if (this.cellRendererParams.format.baseType === JsonDataTypes.String) {\n      value = this.cellRendererParams.allowedValues.find(x => x.key.toString().toLowerCase() === (event.checked ? 'checked' : 'unchecked'))?.value;\n    } else {\n      value = event.checked;\n    }\n    if (value !== undefined) {\n      this.cellRendererParams.value = value;\n      super.setData();\n    }\n  }\n  focusElement() {\n    this.elementRef?.inputViewChild?.nativeElement.focus();\n  }\n  static {\n    this.ɵfac = function AgGridCheckBoxRendererComponent_Factory(t) {\n      return new (t || AgGridCheckBoxRendererComponent)(i0.ɵɵdirectiveInject(i1.ChangeHistoryService), i0.ɵɵdirectiveInject(i2.NotificationService), i0.ɵɵdirectiveInject(i3.AGGridService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AgGridCheckBoxRendererComponent,\n      selectors: [[\"app-checkbox-renderer\"]],\n      viewQuery: function AgGridCheckBoxRendererComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.elementRef = _t.first);\n        }\n      },\n      standalone: true,\n      features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n      decls: 2,\n      vars: 2,\n      consts: [[\"targetElement\", \"\"], [1, \"actionable-grid-renderer\"], [3, \"ngModelChange\", \"onChange\", \"ngModel\", \"binary\", \"disabled\"]],\n      template: function AgGridCheckBoxRendererComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, AgGridCheckBoxRendererComponent_Conditional_0_Template, 2, 1, \"div\")(1, AgGridCheckBoxRendererComponent_Conditional_1_Template, 3, 3, \"div\", 1);\n        }\n        if (rf & 2) {\n          i0.ɵɵconditional(0, !ctx.showEditor && (ctx.cellRendererParams == null ? null : ctx.cellRendererParams.node == null ? null : ctx.cellRendererParams.node.group) ? 0 : -1);\n          i0.ɵɵadvance();\n          i0.ɵɵconditional(1, ctx.showEditor && !(ctx.cellRendererParams == null ? null : ctx.cellRendererParams.pivotMode) ? 1 : -1);\n        }\n      },\n      dependencies: [CheckboxModule, i4.Checkbox, FormsModule, i5.NgControlStatus, i5.NgModel],\n      encapsulation: 2\n    });\n  }\n}", "map": {"version": 3, "names": ["CustomCellRenderer", "JsonDataTypes", "FormsModule", "CheckboxModule", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "tmp_1_0", "ctx_r0", "cellRendererParams", "valueFormatted", "undefined", "value", "ɵɵtwoWayListener", "AgGridCheckBoxRendererComponent_Conditional_1_Template_p_checkbox_ngModelChange_1_listener", "$event", "ɵɵrestoreView", "_r2", "ɵɵnextContext", "ɵɵtwoWayBindingSet", "checked", "ɵɵresetView", "ɵɵlistener", "AgGridCheckBoxRendererComponent_Conditional_1_Template_p_checkbox_onChange_1_listener", "onCheckboxChange", "ɵɵtwoWayProperty", "ɵɵproperty", "allowUserUpdate", "AgGridCheckBoxRendererComponent", "constructor", "changeHistoryService", "notificationService", "aGGridService", "agInit", "matchResult", "format", "baseType", "String", "resultIndex", "<PERSON><PERSON><PERSON><PERSON>", "findIndex", "x", "toString", "toLowerCase", "key", "find", "isDefault", "event", "setData", "focusElement", "elementRef", "inputViewChild", "nativeElement", "focus", "ɵɵdirectiveInject", "i1", "ChangeHistoryService", "i2", "NotificationService", "i3", "AGGridService", "selectors", "viewQuery", "AgGridCheckBoxRendererComponent_Query", "rf", "ctx", "ɵɵtemplate", "AgGridCheckBoxRendererComponent_Conditional_0_Template", "AgGridCheckBoxRendererComponent_Conditional_1_Template", "ɵɵconditional", "showEditor", "node", "group", "pivotMode", "i4", "Checkbox", "i5", "NgControlStatus", "NgModel", "encapsulation"], "sources": ["C:\\Projects\\SaltboxActionableGrid\\Saltbox-Data-App\\saltbox-data-app\\src\\app\\core\\ag-grid\\renderers\\ag-grid-checkbox-renderer.component.ts", "C:\\Projects\\SaltboxActionableGrid\\Saltbox-Data-App\\saltbox-data-app\\src\\app\\core\\ag-grid\\renderers\\ag-grid-checkbox-renderer.component.html"], "sourcesContent": ["import { Component, ViewChild } from '@angular/core';\r\nimport { ICustomCellRendererParams } from './ag-grid-custom-cell-renderer-params';\r\nimport { CustomCellRenderer } from './ag-grid-custom-cell-renderer';\r\nimport { NotificationService } from '../../services/notification.service';\r\nimport { JsonDataTypes } from 'src/app/shared/enums/json-schema.enum';\r\nimport { ChangeHistoryService } from 'src/app/actionable-grid/services/change-history.service';\r\nimport { AGGridService } from '../../services/ag-grid.service';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { CheckboxModule } from 'primeng/checkbox';\r\n\r\n/* for format config driven checkbox only*/\r\n@Component({\r\n    selector: 'app-checkbox-renderer',\r\n    templateUrl: './ag-grid-checkbox-renderer.component.html',\r\n    standalone: true,\r\n    imports: [CheckboxModule, FormsModule]\r\n})\r\nexport class AgGridCheckBoxRendererComponent extends CustomCellRenderer {\r\n  @ViewChild('targetElement') elementRef;\r\n  constructor(\r\n    changeHistoryService: ChangeHistoryService,\r\n    notificationService: NotificationService,\r\n    aGGridService: AGGridService) {\r\n    super(changeHistoryService, notificationService, aGGridService);\r\n  }\r\n\r\n  checkedValue: any;\r\n  uncheckedValue: any;\r\n  checked = false;\r\n\r\n  agInit(cellRendererParams: ICustomCellRendererParams): void {\r\n    super.agInit(cellRendererParams);\r\n\r\n    const matchResult = this.cellRendererParams.value;\r\n\r\n    if (this.cellRendererParams.format.baseType === JsonDataTypes.String) {\r\n      const resultIndex = this.cellRendererParams.allowedValues.findIndex(x => x.value.toString().toLowerCase()\r\n        === matchResult?.toString().toLowerCase());\r\n      this.checked = (resultIndex > -1)\r\n        ? this.cellRendererParams.allowedValues[resultIndex].key.toString().toLowerCase() === 'checked'\r\n        : this.cellRendererParams.allowedValues.find(x => x.isDefault === true).key.toString().toLowerCase() === 'checked';\r\n    }\r\n    else {\r\n      this.checked = matchResult === true || matchResult === 'true';\r\n    }\r\n  }\r\n\r\n  onCheckboxChange(event: any) {\r\n    let value;\r\n\r\n    if (this.cellRendererParams.format.baseType === JsonDataTypes.String) {\r\n      value = this.cellRendererParams.allowedValues\r\n        .find(x => x.key.toString().toLowerCase() === (event.checked ? 'checked' : 'unchecked'))?.value;\r\n    }\r\n    else {\r\n      value = event.checked;\r\n    }\r\n\r\n    if (value !== undefined) {\r\n      this.cellRendererParams.value = value;\r\n      super.setData();\r\n    }\r\n  }\r\n\r\n  focusElement(): void {\r\n    this.elementRef?.inputViewChild?.nativeElement.focus();\r\n  }\r\n}\r\n", "@if (!showEditor && cellRendererParams?.node?.group) {\r\n  <div>\r\n  {{ cellRendererParams?.valueFormatted ?? cellRendererParams?.value }}\r\n  </div>\r\n}\r\n@if (showEditor && !cellRendererParams?.pivotMode) {\r\n  <div class=\"actionable-grid-renderer\">\r\n    <p-checkbox #targetElement [(ngModel)]=\"checked\" [binary]=\"true\" [disabled]=\"!cellRendererParams.allowUserUpdate\"\r\n      (onChange)=\"onCheckboxChange($event)\"></p-checkbox>\r\n  </div>\r\n}"], "mappings": "AAEA,SAASA,kBAAkB,QAAQ,gCAAgC;AAEnE,SAASC,aAAa,QAAQ,uCAAuC;AAGrE,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,cAAc,QAAQ,kBAAkB;;;;;;;;;;ICP/CC,EAAA,CAAAC,cAAA,UAAK;IACLD,EAAA,CAAAE,MAAA,GACA;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IADNH,EAAA,CAAAI,SAAA,EACA;IADAJ,EAAA,CAAAK,kBAAA,OAAAC,OAAA,GAAAC,MAAA,CAAAC,kBAAA,kBAAAD,MAAA,CAAAC,kBAAA,CAAAC,cAAA,cAAAH,OAAA,KAAAI,SAAA,GAAAJ,OAAA,GAAAC,MAAA,CAAAC,kBAAA,kBAAAD,MAAA,CAAAC,kBAAA,CAAAG,KAAA,MACA;;;;;;IAIEX,EADF,CAAAC,cAAA,aAAsC,uBAEI;IADbD,EAAA,CAAAY,gBAAA,2BAAAC,2FAAAC,MAAA;MAAAd,EAAA,CAAAe,aAAA,CAAAC,GAAA;MAAA,MAAAT,MAAA,GAAAP,EAAA,CAAAiB,aAAA;MAAAjB,EAAA,CAAAkB,kBAAA,CAAAX,MAAA,CAAAY,OAAA,EAAAL,MAAA,MAAAP,MAAA,CAAAY,OAAA,GAAAL,MAAA;MAAA,OAAAd,EAAA,CAAAoB,WAAA,CAAAN,MAAA;IAAA,EAAqB;IAC9Cd,EAAA,CAAAqB,UAAA,sBAAAC,sFAAAR,MAAA;MAAAd,EAAA,CAAAe,aAAA,CAAAC,GAAA;MAAA,MAAAT,MAAA,GAAAP,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAoB,WAAA,CAAYb,MAAA,CAAAgB,gBAAA,CAAAT,MAAA,CAAwB;IAAA,EAAC;IACzCd,EAD0C,CAAAG,YAAA,EAAa,EACjD;;;;IAFuBH,EAAA,CAAAI,SAAA,EAAqB;IAArBJ,EAAA,CAAAwB,gBAAA,YAAAjB,MAAA,CAAAY,OAAA,CAAqB;IAAiBnB,EAAhB,CAAAyB,UAAA,gBAAe,cAAAlB,MAAA,CAAAC,kBAAA,CAAAkB,eAAA,CAAiD;;;ADGrH;AAOA,OAAM,MAAOC,+BAAgC,SAAQ/B,kBAAkB;EAErEgC,YACEC,oBAA0C,EAC1CC,mBAAwC,EACxCC,aAA4B;IAC5B,KAAK,CAACF,oBAAoB,EAAEC,mBAAmB,EAAEC,aAAa,CAAC;IAKjE,KAAAZ,OAAO,GAAG,KAAK;EAJf;EAMAa,MAAMA,CAACxB,kBAA6C;IAClD,KAAK,CAACwB,MAAM,CAACxB,kBAAkB,CAAC;IAEhC,MAAMyB,WAAW,GAAG,IAAI,CAACzB,kBAAkB,CAACG,KAAK;IAEjD,IAAI,IAAI,CAACH,kBAAkB,CAAC0B,MAAM,CAACC,QAAQ,KAAKtC,aAAa,CAACuC,MAAM,EAAE;MACpE,MAAMC,WAAW,GAAG,IAAI,CAAC7B,kBAAkB,CAAC8B,aAAa,CAACC,SAAS,CAACC,CAAC,IAAIA,CAAC,CAAC7B,KAAK,CAAC8B,QAAQ,EAAE,CAACC,WAAW,EAAE,KACnGT,WAAW,EAAEQ,QAAQ,EAAE,CAACC,WAAW,EAAE,CAAC;MAC5C,IAAI,CAACvB,OAAO,GAAIkB,WAAW,GAAG,CAAC,CAAC,GAC5B,IAAI,CAAC7B,kBAAkB,CAAC8B,aAAa,CAACD,WAAW,CAAC,CAACM,GAAG,CAACF,QAAQ,EAAE,CAACC,WAAW,EAAE,KAAK,SAAS,GAC7F,IAAI,CAAClC,kBAAkB,CAAC8B,aAAa,CAACM,IAAI,CAACJ,CAAC,IAAIA,CAAC,CAACK,SAAS,KAAK,IAAI,CAAC,CAACF,GAAG,CAACF,QAAQ,EAAE,CAACC,WAAW,EAAE,KAAK,SAAS;IACtH,CAAC,MACI;MACH,IAAI,CAACvB,OAAO,GAAGc,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,MAAM;IAC/D;EACF;EAEAV,gBAAgBA,CAACuB,KAAU;IACzB,IAAInC,KAAK;IAET,IAAI,IAAI,CAACH,kBAAkB,CAAC0B,MAAM,CAACC,QAAQ,KAAKtC,aAAa,CAACuC,MAAM,EAAE;MACpEzB,KAAK,GAAG,IAAI,CAACH,kBAAkB,CAAC8B,aAAa,CAC1CM,IAAI,CAACJ,CAAC,IAAIA,CAAC,CAACG,GAAG,CAACF,QAAQ,EAAE,CAACC,WAAW,EAAE,MAAMI,KAAK,CAAC3B,OAAO,GAAG,SAAS,GAAG,WAAW,CAAC,CAAC,EAAER,KAAK;IACnG,CAAC,MACI;MACHA,KAAK,GAAGmC,KAAK,CAAC3B,OAAO;IACvB;IAEA,IAAIR,KAAK,KAAKD,SAAS,EAAE;MACvB,IAAI,CAACF,kBAAkB,CAACG,KAAK,GAAGA,KAAK;MACrC,KAAK,CAACoC,OAAO,EAAE;IACjB;EACF;EAEAC,YAAYA,CAAA;IACV,IAAI,CAACC,UAAU,EAAEC,cAAc,EAAEC,aAAa,CAACC,KAAK,EAAE;EACxD;;;uBAjDWzB,+BAA+B,EAAA3B,EAAA,CAAAqD,iBAAA,CAAAC,EAAA,CAAAC,oBAAA,GAAAvD,EAAA,CAAAqD,iBAAA,CAAAG,EAAA,CAAAC,mBAAA,GAAAzD,EAAA,CAAAqD,iBAAA,CAAAK,EAAA,CAAAC,aAAA;IAAA;EAAA;;;YAA/BhC,+BAA+B;MAAAiC,SAAA;MAAAC,SAAA,WAAAC,sCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;;;UCZ5C/D,EALA,CAAAiE,UAAA,IAAAC,sDAAA,cAAsD,IAAAC,sDAAA,iBAKF;;;UALpDnE,EAAA,CAAAoE,aAAA,KAAAJ,GAAA,CAAAK,UAAA,KAAAL,GAAA,CAAAxD,kBAAA,kBAAAwD,GAAA,CAAAxD,kBAAA,CAAA8D,IAAA,kBAAAN,GAAA,CAAAxD,kBAAA,CAAA8D,IAAA,CAAAC,KAAA,WAIC;UACDvE,EAAA,CAAAI,SAAA,EAKC;UALDJ,EAAA,CAAAoE,aAAA,IAAAJ,GAAA,CAAAK,UAAA,MAAAL,GAAA,CAAAxD,kBAAA,kBAAAwD,GAAA,CAAAxD,kBAAA,CAAAgE,SAAA,WAKC;;;qBDKazE,cAAc,EAAA0E,EAAA,CAAAC,QAAA,EAAE5E,WAAW,EAAA6E,EAAA,CAAAC,eAAA,EAAAD,EAAA,CAAAE,OAAA;MAAAC,aAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}