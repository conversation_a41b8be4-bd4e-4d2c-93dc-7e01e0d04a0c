import { Component, ElementRef, ViewChild } from '@angular/core';
import { ICustomCellRendererParams } from './ag-grid-custom-cell-renderer-params';
import { CustomCellRenderer } from './ag-grid-custom-cell-renderer';
import { NotificationService } from '../../services/notification.service';
import { ChangeHistoryService } from 'src/app/actionable-grid/services/change-history.service';
import { IRowActionsParamsContext } from '../../models/row-actions-params-context';
import { AGGridService } from '../../services/ag-grid.service';
import { FormlyFieldConfig } from '@ngx-formly/core';
import { SBFormlyService } from 'src/app/sb-formly-renderer/services/sb-formly.services';
import { UserPermissionLevels } from '../../enums/shared';
import { SBFromlyRendererDialogComponent } from '../../../sb-formly-renderer/sb-formly-renderer-dialog/sb-formly-renderer-dialog.component';
import { TooltipModule } from 'primeng/tooltip';
import { ButtonModule } from 'primeng/button';

/* for format config driven checkbox only*/
@Component({
    selector: 'app-button-renderer',
    templateUrl: './ag-grid-button-renderer.component.html',
    standalone: true,
    imports: [ButtonModule, TooltipModule, SBFromlyRendererDialogComponent]
})
export class AgGridButtonRendererComponent extends CustomCellRenderer {
    @ViewChild('targetElement', { static: false }) elementRef: ElementRef;

    // Action button variables
    showObjectFieldDialog = false;
    objectFieldConfig: FormlyFieldConfig[];
    btnIcon = 'pi pi-pencil';

    constructor(
        changeHistoryService: ChangeHistoryService,
        notificationService: NotificationService,
        aGGridService: AGGridService,
        private sBFormlyService: SBFormlyService) {
        super(changeHistoryService, notificationService, aGGridService);
    }

    agInit(cellRendererParams: ICustomCellRendererParams): void {
        super.agInit(cellRendererParams);

        if (!this.paramsContext.userPermissionLevel || [UserPermissionLevels.View, UserPermissionLevels.None].includes(this.paramsContext.userPermissionLevel))
            this.btnIcon = 'pi pi-ellipsis-h';
        else if (this.paramsContext.buttonIcon)
            this.btnIcon = this.paramsContext.buttonIcon
    }

    buttonClick(): void {
        if (this.paramsContext?.buttonClick)
            this.paramsContext?.buttonClick(this);
        else
            this.handleActionButtonClick();
    }

    // Not this is just to open the object forms (default form) just to view the data, 
    // for custom action or edit the data please implement the buttonClick
    handleActionButtonClick(): void {
        if (!this.cellRendererInstance?.fieldBaseSchema)
            return;

        this.objectFieldConfig = this.sBFormlyService.getDefaultFormlyFieldsBySchema(this.cellRendererInstance.fieldBaseSchema);

        if (this.objectFieldConfig)
            this.showObjectFieldDialog = true;
    }

    get paramsContext(): IRowActionsParamsContext {
        if (!this.cellRendererParams?.context) {
            return undefined;
        }

        return this.cellRendererParams?.context as IRowActionsParamsContext;
    }

    focusElement(): void {
        this.elementRef?.nativeElement?.focus();
    }
}
