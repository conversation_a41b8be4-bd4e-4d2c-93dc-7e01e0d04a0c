body.saltbox-data-app.sbdapp {

  // Text Colors
  .text-saltbox-blue{
    color: var(--saltbox-blue) !important;
  }
  .text-saltbox-red{
    color: var(--saltbox-red) !important;
  }
  
  .text-base-text{
    color: var(--BaseText) !important;
  }
  .text-light{
    color: var(--TextLight) !important;
  }
  .text-dark{
    color: var(--TextDark) !important;
  }
  .text-text-secondary{
    color: var(--text-secondary) !important;
  }
  .text-placeholder-text{
    color: var(--placeholderText) !important;
  }
  .text-inverse-text{
    color: var(--InverseText) !important;
  }
  
  .text-border-color{
    color: var(--BorderColor) !important;
  }
  
  .text-primary-override{
    color: var(--primary) !important;
  }
  .text-primary-active{
    color: var(--primaryActive) !important;
  }
  .text-secondary{
    color: var(--secondary) !important;
  }
  .text-secondary-active{
    color: var(--secondaryActive) !important;
  }
  .text-secondary-shade-1{
    color: var(--secondaryShade1) !important;
  }
  .text-secondary-shade-2{
    color: var(--secondaryShade2) !important;
  }
  .text-secondary-shade-3{
    color: var(--secondaryShade3) !important;
  }
  
  .text-surface-primary{
    color: var(--surfacePrimary) !important;
  }
  .text-surface-primary-alt{
    color: var(--surfacePrimaryAlt) !important;
  }
  .text-surface-secondary{
    color: var(--surfaceSecondary) !important;
  }
  .text-surface-secondary-alt{
    color: var(--surfaceSecondaryAlt) !important;
  }
  .text-highlight-primary{
    color: var(--highlightPrimary) !important;
  }
  .text-highlight-high-contrast{
    color: var(--highlightHighContrast) !important;
  }
  .text-surface-nav{
    color: var(--surfaceNav) !important;
  }
  .text-surface-blue{
    color: var(--surfaceBlue) !important;
  }
  .text-surface-green{
    color: var(--surfaceGreen) !important;
  }
  .text-surface-yellow{
    color: var(--surfaceYellow) !important;
  }
  .text-surface-orange{
    color: var(--surfaceOrange) !important;
  }
  .text-surface-red{
    color: var(--surfaceRed) !important;
  }
  
  .text-success{
    color: var(--success) !important;
  }
  .text-success-light{
    color: var(--successLight) !important;
  }
  .text-success-dark{
    color: var(--successDark) !important;
  }
  .text-success-color-dull{
    color: var(--successColorDull) !important;
  }
  
  .text-warning{
    color: var(--warning) !important;
  }
  .text-warning-light{
    color: var(--warningLight) !important;
  }
  .text-warning-dark{
    color: var(--warningDark) !important;
  }
  
  .text-danger{
    color: var(--danger) !important;
  }
  .text-danger-light{
    color: var(--dangerLight) !important;
  }
  .text-danger-dark{
    color: var(--dangerDark) !important;
  }
  .text-fail-color-dull{
    color: var(--FailColorDull) !important;
  }
  
  .text-data-1{
    color: var(--data1) !important;
  }
  .text-data-2{
    color: var(--data2) !important;
  }
  .text-data-3{
    color: var(--data3) !important;
  }
  .text-data-4{
    color: var(--data4) !important;
  }
  .text-data-5{
    color: var(--data5) !important;
  }
  .text-data-6{
    color: var(--data6) !important;
  }
  .text-data-7{
    color: var(--data7) !important;
  }
  .text-data-8{
    color: var(--data8) !important;
  }
  .text-data-9{
    color: var(--data9) !important;
  }
  
  
  //BG Background Colors
  .bg-saltbox-blue{
    background-color: var(--saltbox-blue) !important;
  }
  .bg-saltbox-red{
    background-color: var(--saltbox-red) !important;
  }
  
  .bg-base-text{
    background-color: var(--BaseText) !important;
  }
  .bg-text-light{
    background-color: var(--TextLight) !important;
  }
  .bg-text-dark{
    background-color: var(--TextDark) !important;
  }
  .bg-text-secondary{
    background-color: var(--text-secondary) !important;
  }
  .bg-placeholder-text{
    background-color: var(--placeholderText) !important;
  }
  .bg-inverse-text{
    background-color: var(--InverseText) !important;
  }
  
  .bg-border-color{
    background-color: var(--BorderColor) !important;
  }
  
  .bg-primary-override{
    background-color: var(--primary) !important;
  }
  .bg-primary-active{
    background-color: var(--primaryActive) !important;
  }
  
  .bg-secondary{
    background-color: var(--secondary) !important;
  }
  .bg-secondary-active{
    background-color: var(--secondaryActive) !important;
  }
  .bg-secondary-shade-1{
    background-color: var(--secondaryShade1) !important;
  }
  .bg-secondary-shade-2{
    background-color: var(--secondaryShade2) !important;
  }
  .bg-secondary-shade-3{
    background-color: var(--secondaryShade3) !important;
  }
  
  .bg-shade-2{
    background-color: var(--bgShade2) !important;
  }
  .bg-shade-3{
    background-color: var(--bgShade3) !important;
  }
  .bg-shade-4{
    background-color: var(--bgShade4) !important;
  }
  .bg-inverse{
    background-color: var(--bgInverse) !important;
  }
  .bg-inverse-shade-1{
    background-color: var(--bgInverseShade1) !important;
  }
  .bg-inverse-shade-2{
    background-color: var(--bgInverseShade2) !important;
  }
  .bg-inverse-shade-3{
    background-color: var(--bgInverseShade3) !important;
  }
  
  .bg-surface-primary{
    background-color: var(--surfacePrimary) !important;
  }
  .bg-surface-primary-alt{
    background-color: var(--surfacePrimaryAlt) !important;
  }
  .bg-surface-secondary{
    background-color: var(--surfaceSecondary) !important;
  }
  .bg-surface-secondary-alt{
    background-color: var(--surfaceSecondaryAlt) !important;
  }
  .bg-highlight-primary{
    background-color: var(--highlightPrimary) !important;
  }
  .bg-highlight-high-contrast{
    background-color: var(--highlightHighContrast) !important;
  }
  .bg-surface-nav{
    background-color: var(--surfaceNav) !important;
  }
  .bg-surface-blue{
    background-color: var(--surfaceBlue) !important;
  }
  .bg-surface-green{
    background-color: var(--surfaceGreen) !important;
  }
  .bg-surface-yellow{
    background-color: var(--surfaceYellow) !important;
  }
  .bg-surface-orange{
    background-color: var(--surfaceOrange) !important;
  }
  .bg-surface-red{
    background-color: var(--surfaceRed) !important;
  }
  
  .bg-success{
    background-color: var(--success) !important;
  }
  .bg-success-light{
    background-color: var(--successLight) !important;
  }
  .bg-success-dark{
    background-color: var(--successDark) !important;
  }
  .bg-success-color-dull{
    background-color: var(--successColorDull) !important;
  }
  
  .bg-warning{
    background-color: var(--warning) !important;
  }
  .bg-warning-light{
    background-color: var(--warningLight) !important;
  }
  .bg-warning-dark{
    background-color: var(--warningDark) !important;
  }
  
  .bg-danger{
    background-color: var(--danger) !important;
  }
  .bg-danger-light{
    background-color: var(--dangerLight) !important;
  }
  .bg-danger-dark{
    background-color: var(--dangerDark) !important;
  }
  .bg-fail-color-dull{
    background-color: var(--FailColorDull) !important;
  }
  
  .bg-data-1{
    background-color: var(--data1) !important;
  }
  .bg-data-2{
    background-color: var(--data2) !important;
  }
  .bg-data-3{
    background-color: var(--data3) !important;
  }
  .bg-data-4{
    background-color: var(--data4) !important;
  }
  .bg-data-5{
    background-color: var(--data5) !important;
  }
  .bg-data-6{
    background-color: var(--data6) !important;
  }
  .bg-data-7{
    background-color: var(--data7) !important;
  }
  .bg-data-8{
    background-color: var(--data8) !important;
  }
  .bg-data-9{
    background-color: var(--data9) !important;
  }
  
    /*Wizard Styles*/
/*Wizard Styles*/

.wizard-wrapper{
    background-color: var(--surfaceSecondary);

    &.p-dialog-maximized{
        .col-11 .wizard-scroll{
        height: calc(100vh - 9rem);
        }
    }

    &.lookup-wizard{
        .col-11 .wizard-scroll{
        height: calc(100vh - 14rem);
        }
    }

    .col-11{
      max-width: calc(100% - 80px);

      .wizard-scroll{
        width: 100%;
        overflow: auto;
        height: 31rem;
        }
  
      .col-11{
        max-width: initial;
      }
  
      .field{
        max-width: 100%;
      }
  
      p-inputnumber, .p-inputnumber{
        width: 100%;
      }
    }
  
    .tree-card{
      .p-card{
        .p-card-body{
          padding: 0;
        }
      }
    }
  
    .p-float-label.search-box{
      background: var(--primary);
      max-width: 660px;
      color: var(--InverseText);
  
      input{
        color: var(--InverseText);
      }
  
      .pi-icon-search{
        vertical-align: middle;
      }
  
      label{
        color: var(--InverseText);
      }
  
      .p-inputwrapper-filled ~ label, .p-inputwrapper-focus ~ label{
        color: var(--primary);
      }
  
    }
  
    &.add-new-project{
      &.p-dialog{
        .p-dialog-content{
          .p-panel-content{
            max-height: 60vh;
            overflow-x: hidden;
            overflow-y: auto;
          }
        }
      }
    }
  
   .p-tabview .p-tabview-panels{
    padding: 0;
    max-height: 450px;
   }
   .cron .p-tabview .p-tabview-panels{
    max-height: initial;
   }
  
    &.p-dialog{
  
      min-width: 400px;
  
      .p-dialog-content{
        background-color: var(--surfaceSecondary);
        padding: 0 0 0 1.5rem !important;
        overflow: hidden; //needs to be hidden or content overflows on resize
      }
  
      .p-dialog-footer{
        padding: 0.714em 1em !important;
        font-size: 14px;
        background-color: var(--surfaceSecondary)
      }
    }
  
    .label{
      color: var(--primary);
    }
  
    .col-11{
      .grid{
        margin: 0;
      }
    }
  
    h3{
      margin: 0.5rem 0;
      color: var(--primary);
      display: flex;
      font-size: 1em;
      font-weight: normal;
  
      i{
        margin-right: 5px;
      }
    }
  
    .p-toolbar-secondary h3{
      color: var(--InverseText);
      margin: 0;
    }
  
    .p-panel {
      p-header{
        width: 100%;
      }
  
      .p-panel-header {
        color: var(--primary);
        background: var(--surfaceSecondary);
        padding: 0;
  
        .p-panel-title{
          line-height: 2;
          font-size: 0.95rem;
        }
        .square-icon{
          float: right;
          clear:right;
        }
      }
      .p-dropdown{
        padding-left: 7px;
      }
    }
  
  
    .p-tabview-nav-link{
      padding: 0.75rem !important;
    }
  
    .btn-config {
      margin-top: 1rem;
    }
  
    .p-dropdown{
      display: flex;
    }
  
    .p-card-content{
      padding: 0;
    }
  
    p-header{
      margin-left: 0 !important;
    }
  
    input, .p-dropdown, .p-multiselect, .p-autocomplete, textarea{
      width: 100%;
      max-width: 100%;
      min-width: 30rem;
  
      &#iconTextbox{
        min-width: 28rem;
      }
    }
  
    .p-fileupload {
      p-messages {
        display: none;
      }
  
      .p-fileupload-choose {
        margin-top: 8px;
  
        .pi {
          transition: all 0.8s;
        }
      }
    }
  
    .scrollPanel-wrap {
      hr {
        margin: 16px 0;
        border: none;
        border-bottom: 2px dashed var(--primaryActive);
        width: 100%;
      }
  
      .p-scrollpanel {
        border: none !important;
        padding: 0 5px;
      }
    }
  
  
    .p-treetable {
      .p-treetable-tbody{
        display: block;
        tr {
          display: block;
          border: 0;
          pointer-events: none;
          background-color: var(--surfacePrimary);
  
          &.p-highlight {
            background: inherit !important;
          }
  
          &.p-highlight>td {
            background: inherit;
            border: var(--dropShadowMain);
          }
  
          td{
            border: 0;
            font-size: 14px;
            display: flex;
            justify-content: space-between;
            border-bottom: 1px solid var(--bgShade2);
            padding: 0;
            background-color: var(--surfacePrimary);
  
            div{
              display: flex;
              align-items: center;
              flex: 1;
  
              p-treetabletoggler {
                pointer-events: visible;
              }
  
              div{
                display: contents;
              }
  
              .pi-minus{
                color: var(--bgShade2);
              }
  
              input[type=checkbox] {
                width: 20px !important;
                height: 18px;
                pointer-events: visible;
                min-width: initial;
              }
            }
          }
        }
      }
    }
  
    .include-btn {
      font-size: 10px;
      padding: 5px;
      display: flex;
      flex-direction: column;
      align-items: center;
      width: 100%;
      height: 45px;
      max-width: 45px;
      cursor: pointer;
      background-color: var(--primary);
      color: var(--InverseText);
  
      .pi{
        color: var(--InverseText) !important;
      }
    }
  
    .exclude-btn {
      background: var(--surfaceSecondary);
      color: var(--bgInverseShade1);
      max-width: 45px;
    }
  
    .new{
      background-color: var(--surfaceSecondary);
      border: 1px solid var(--BorderColor);
      border-radius: 10px;
      padding: 0px 7px;
      font-size: 12px;
    }
  
  .empty-notification input {
    color: var(--warningLight);
    font-size: 12px;
  }
  
    .p-card-content{
      .empty-content {
        .watermark-cust {
            background-color: transparent;
            display: flex;
            justify-content: center;
            align-items: center;
            flex-direction: column;
            .content{
                margin: 0;
                strong {
                  font-weight: 500;
                  opacity: 1;
                }
            }
            .saltbox-watermark {
              width: 30px;
              height: 30px;
              position: relative;
              margin-bottom: 0.5rem;
              svg {
                opacity: .5!important;
              }
            }
          }
      }
    }
  }
  
  .wizard-menu {
    padding: 1.5em 10px 1.5em 0;
    min-width: 80px;
    max-width: 80px;
  
    .col-12 {
      margin-bottom: 0.5rem;
      border-radius: 6px;
      background: var(--surfacePrimary);
      box-shadow: 0 1px 3px 2px var(--dropShadowStrong);
      padding: 0;
  
      a {
        display: flex;
        color: var(--BaseText);
        justify-content: center;
        align-items: center;
        padding: 1.3rem;
        font-size: 1.2rem;
  
        svg {
          path {
            fill: var(--primary);
          }
        }
      }
  
      a.disabled {
        background: var(--surfaceSecondary) !important;
        color: var(--bgShade3)!important;
        fill: var(--bgShade3)!important;
        cursor: unset;
  
        svg {
          path {
            background: var(--surfaceSecondary) !important;
            color: var(--bgShade3)!important;
            fill: var(--bgShade3)!important;
          }
        }
      }
    }
  
    .col-12.active {
      background: var(--primary);
  
      a {
        color: var(--surfacePrimary);
  
        svg path {
          fill: var(--surfacePrimary);
        }
      }
    }
  }
    /**/
    /*Embedded Iframes*/

    .iframe-embed {
        height: calc(100vh - 11.5rem);
        width: calc(100vw - 4.4rem);
        margin: -1.7rem -1rem 0rem;
        border: none;

        &.nested {
            width: -webkit-fill-available;
            height: calc(100vh - 9rem);
            margin: 0;
            max-width: 100%;
        }
    }

    .p-sidebar-content .iframe-embed {
        margin: -1.7rem 0 0;
        max-width: 100%;
    }
    /*SideBar*/

    .sidebar-lg {
        width: 80vw;
        max-width: 100vw;
    }

    .sidebar-md {
        width: 50vw;
        max-width: 100vw;
    }

    .sidebar-lg, .sidebar-md, .sidebar-sm {
        .p-card {
            box-shadow: none;
            font-size: 0.875rem;
        }

        .form-designer .form-layout{
            max-height: none;
            margin: 0;
            padding: 0;
        }

        &.p-sidebar-full{
            .p-sidebar-header{
                .pi-window-maximize:before{
                    content: "\e93a";
                }
            }
        }

        .p-sidebar-header {
            border: 1px solid var(--BorderColor);
            margin: 0.5rem;
            border-radius: 0.2rem;
            padding: 0.3rem 1rem;
        }

        .json-schema-form .p-inputnumber input {
            min-width: calc(50vw - 3.5rem);
            max-width: calc(100vw - 48rem);
        }

        input, textarea, .p-dropdown, .p-multiselect {
            max-width: 100%;
        }

        app-form-renderer, app-dynamic-form-body {
            font-size: 0.875rem;
            
            .field .p-float-label{
                margin: 0;
            }

            .px-3.w-full{
                padding: 0 !important;
            }

            .p-panel {
                box-shadow: none;
                border: none;

                .p-panel-header {
                    display: none;
                }

            }

            .p-panel-secondary{
                .p-panel {
                    .p-panel-header {
                        display: flex;
                        margin: 0;
                    }   
                }
            }

            .form-designer.grid{
                margin: -0.5rem;
            }

        }

        app-json-schema-form {
            .field {
                margin-bottom: 0;
                max-width: -webkit-fill-available;
            }

            .p-card .p-card-body {
                padding: 0;
            }

            .p-card-content {
                height: calc(100vh - 13rem);
                overflow-y: auto;
                overflow-x: hidden;

                .grid {
                    max-width: 100%;
                }
            }

            .p-card-footer {
                display: flex;
                justify-content: end;
            }

            ag-grid-angular {
                border: none;
            }
        }

        .data-entry-form {
            max-width: 100%;
            width: 100%;

            .p-panel-content {
                overflow: visible;
                padding: 0;
            }

            .p-panel .p-panel-header {
                margin-bottom: 1rem;
            }

            .p-card .p-card-body {
                padding: 0 0 0 0.75rem;
            }
        }
    }
    /**/

    .section-description {
        margin: 0.5rem 0;
        padding: 0 0.5rem;
        width: 100%;
        border-left: 0.25rem solid var(--primary);
        display: inline-block;

        &.description-gray {
            border-color: var(--bgShade3);
        }

        &.description-yellow {
            border-color: var(--warningLight);
        }

        &.description-red {
            border-color: var(--danger);
        }

        &.grey {
            border-color: var(--BorderColor);
            color: var(--text-secondary);
        }

        p {
            font-size: 0.875rem;
        }

        p:first-of-type {
            margin-top: 0px;
        }

        p:last-of-type {
            margin-bottom: 0px;
        }
    }

    .description-complex {
        p:first-of-type {
            margin-top: 0px;
        }

        p:last-of-type {
            margin-bottom: 0px;
        }
    }

    .p-treenode-droppoint {
        border: 1px solid transparent;
        height: auto;
    }

    .p-treenode-dragover {
        .p-treenode-droppoint {
            border: 1px dashed var(--primary) !important;
            padding: 0.5rem;
            background-color: var(--primary)BGShade;
            margin: 0.5rem;

            &.p-treenode-droppoint-active {
                border: 1px dashed var(--primary) !important;
                padding: 0.5rem;
                background-color: var(--primary)BGShade;
                margin: 0.5rem;
            }
        }
    }
    /* Formly */

    formly-form {
        margin: 0 0.5rem;
        display: flex;

        .p-inputtext {
            height: 100%;
            min-height: 2.7rem;
        }
    }

    formly-group {
        width: 100%;
    }

    formly-field, app-formly-field-grid, app-formly-array-type, app-formly-null-type, app-formly-object-type, app-formly-multi-schema-type, app-signature-capture {
        position: relative;
        width: 100%;
        display: flex;
        margin: 0;
        padding: 0;

        .field{
            margin-bottom: 0;
        }
    }

    app-formly-field-grid, app-signature-capture, app-formly-object-type {
        display: block;
    }

    app-formly-field-grid {
        .grid {
            min-height: 300px !important;

            .formly-grid-scroll {
                max-width: 98%;
                overflow-y: auto;
                overflow-x: visible;
            }
        }
    }

    app-formly-cell-renderer {
        formly-field {
            margin: 0;
        }
    }

    app-formly-array-type {
        margin: 0;
    }

    formly-wrapper-primeng-form-field {
        width: 100%;

        label {
            top: 0.25rem;
            left: 0.5rem;
            position: absolute;
            line-height: 1;
            font-size: 0.66rem;
            color: var(--primary);
        }
    }

    app-formly-array-type {
        .row {
            display: flex;
            padding: 0 1.5rem;
            border-bottom: 1px solid var(--BorderColor);

            .grid {
                width: 100%;
            }

            formly-field {
                width: 48%;
                margin: 0.5rem 0.5rem -0.1rem 0;
            }

            .col-2 {
                width: 3rem;
            }
        }
    }

    app-nested-form {
        width: 100%;
    }

    signature-pad{
        background-color: var(--surfacePrimary) !important;
    }

    .p-dialog-content app-formly-field-grid {
        height: 15rem;
    }

    .signature-image {
        img {
            max-width: 100% !important;
        }
    }
    /**/
    /*Main container desktop margins*/
    .appBuilderParent {
        margin-left: 4.3em;
        margin-top: 1.3rem;
    }
    /* Saltbox-Data-App Styles */
    .sbi-view-pad {
        padding: 0 0 1rem;
    }

    .sbi-view-pad-none {
        padding: 0;
        margin-left: -3.75rem;
        display: block;
    }
    /* Icon Color Correction Classes */
    .custom__blue-icon {
        color: var(--primary) !important;
    }

    .custom__white-icon {
        color: var(--InverseText);
    }
    /* Refresh Confirmation Dialog */
    .refreshModal {
        width: 420px;
    }
    /* User Entered Parameters */
    .uep {
        padding: 8px;
        border-left: 1px solid var(--BorderColor);
        border-right: 1px solid var(--BorderColor);
        border-bottom: 1px solid var(--BorderColor);
    }

    .uep .p-accordion .p-accordion-header:not(.p-disabled).p-highlight .p-accordion-header-link,
    .uep .p-accordion .p-accordion-header .p-accordion-header-link,
    .uep .p-accordion .p-accordion-header:not(.p-highlight):not(.p-disabled):hover .p-accordion-header-link {
        background-color: var(--InverseText) !important;
        color: var(--BaseText);
        border-bottom: 1px solid var(--BorderColor);
        border-left: 1px solid var(--BorderColor);
        border-right: 1px solid var(--BorderColor);
    }

    .uep .p-accordion .p-accordion-header .p-accordion-header-link .p-accordion-toggle-icon {
        margin-right: 12px;
        color: var(--BaseText);
    }

    /* JSON Schema Form Layout */

    .json-schema-form {
        border-radius: 4px;
        width: 100%;
        height: auto;

        input {
            max-width: -webkit-fill-available;
        }

        .p-inputnumber input {
            min-width: 29rem;
        }
    }
    /* Prime Dialog for Row Renderer Embed */
    .p-dialog-attachments {
        width: 85vw;
    }
    /* Styles for AG-Grid File Uploads Renderer */
    .noUploadedFiles {
        visibility: hidden;
        z-index: 0;
    }

    .noUploadedFiles i {
        visibility: hidden;
        position: absolute;
        top: -1px;
    }

    .hasUploadedFiles {
        position: absolute;
        display: block;
        top: 5px;
        left: 22px;
        visibility: visible;
        border-radius: .35rem;
        height: .7rem;
        width: .7rem;
        background-color: var(--primary);
        font-size: .55rem;
        z-index: 100;
    }

    .hasUploadedFiles i {
        color: var(--InverseText);
        position: absolute;
        top: -1px;
    }

    .in-cell-holder {
        display: flex;
        flex-direction: row;
    }

    .actionable-grid-checkbox{
      justify-items: center;
      align-content: center;
      .p-checkbox .p-checkbox-box {
        width: 1.25rem;
        height: 1.25rem;
        border-width: 0.125rem;
      }
      .p-checkbox:not(.p-checkbox-checked) .p-checkbox-box {
        padding: 0 0.5rem;
      }
      
      .p-checkbox .p-checkbox-icon {
          width: 1rem;
          height: 1rem;
          transform: scale(1);
      }
      
      .p-checkbox .p-checkbox-icon svg {
          width: 100%; /* Ensures it takes full width */
          height: 100%; /* Ensures it takes full height */
          viewBox: "0 0 20 20"; /* Adjust this based on the original SVG size */
      }
    }
    /*Calendar Button inside Grids*/

    .ag-theme-material .p-calendar .p-button {
        height: 1.8rem !important;
        width: 2.4rem;
        border: 1px solid var(--bgShade2);
        margin: 0.12rem;
        border-left: 0;
        background-color: var(--surfacePrimary);
        color: var(--BaseText);
    }
    /*Remove after navigation app clean up*/
    .horizontal-sub-nav .sub-title {
        padding: 0 0.5rem;
    }

    .in-cell-display {
        width: fit-content;
        opacity: 0.7;

        .p-button-outlined {
            .pi {
                color: var(--primary) !important;
            }

            &:enabled:not(:focus):hover {
                .pi {
                    color: var(--InverseText) !important;
                }
            }
        }
    }

    .ag-theme-material .ag-cell.ag-cell-last-left-pinned:not(.ag-cell-range-right):not(.ag-cell-range-single-cell) {
        padding: 0;
    }

    app-file-upload-renderer, .actionable-grid-renderer.action-btns {
        width: 4.5rem;
        padding: 0;
        margin: 0 auto;
        display: flex;

        .attachment-pill {
            position: absolute;
            top: 0; 
            right: -0.25rem; 
            width: 1.25rem; 
            height: 1.25rem;
            background-color: var(--secondary);
            color: var(--InverseText);
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;

            .pi.pi-paperclip {
              font-size: 0.75rem;
            }
            
          }
    }

    .icon-displace {
        cursor: initial;
        opacity: 0.2;
        padding-top: 1px;
    }

    .in-cell-deleteIcon {
        opacity: 0.7;
        padding-top: 1px;

        :hover {
            color: var(--danger);
        }
    }

    .in-cell-editIcon {
        opacity: 0.7;
        padding-top: 1px;
    }

    .in-cell-icon-focus {
        background-color: transparent;
        color: var(--primaryActive);
    }
    /* Default File-Upload Container, no borders, 1 rem top & btm margins and no padding */
    .fileUpload-default {
        border: none;
        padding: 0;
        margin: 1rem 0;
        background-color: var(--bgShade2);
    }
    /* Prime Upload Template customizations */
    .fileUpload-file-template {
        margin: 0.85rem;
        padding: 5px;
        background-color: var(--bgShade3);
        width: 100%;
    }

    .fileUpload-content-template {
        height: 90px;
        width: 100%;
        border: .2rem dashed var(--BorderColor);
        text-align: center;
        padding-top: 30px;
        font-size: .9rem;
        color: var(--BaseText);
        background-color: var(--bgShade3);
        opacity: .5;
    }
    /* Prime DataTable Customizations */
    .fileUpload-datatable {
        .p-datatable-wrapper {
            border: 1px solid var(--BorderColor);
            border-radius: var(--border-radius);
        }

        .p-datatable .p-datatable-thead > tr > th {
            background-color: var(--surfacePrimaryAlt);
            font-size: .8rem;
        }

        .p-datatable .p-datatable-tbody > tr > td {
            font-size: .85rem;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
            max-width: 1px;
        }

        p-button .p-button{
          min-height: unset;
        }
    }

    .p-fileupload .p-fileupload-content {
        padding: 0 1rem 1.25rem 1rem;
        border: none;
    }

    .template-footer-text {
        font-size: .85rem;
        padding: 1rem;
    }
    /* File Upload Renderer Buttons */
    .fileUpload-button-trash {
        margin-right: 1rem;
        background-color: transparent;
        border: none;
    }

    .fileUpload-button-download {
        margin-right: 1rem;
        background-color: transparent;
        border: none;
    }

    .fileUpload-toolbar-text {
        top: 0;
        font-size: .9rem;
        padding-left: 1rem;
        border-left: 4px solid var(--primary);
    }
    /* Prime P-Upload customizations */
    .customFileUploader .p-fileupload .p-fileupload-buttonbar {
        border: none;
        height: auto;
    }

    .customFileUploader .p-fileupload .p-fileupload-buttonbar .p-button {
        float: right;
        margin-top: 0.25rem;
    }

    .p-fileupload-buttonbar {
        height: 75px;
    }
    /* Global icon padding for embedded icons */
    .grid-icon {
        float: right;
        cursor: pointer;
        font-size: 1.1rem;
        margin-left: 1rem;
        
    }
    /* JSON Schema embedded File-Upload Form Styles */
    .embedded-upload-form {
        margin-top: -0.5rem;

        .p-fileupload .p-fileupload-buttonbar {
            padding: 0;
            margin: 0;
        }
    }
    /* Drag & Drop Layout FormBuilder Styles */
    /* wrapper for multi-input area etc for images or content */

    .controlDropArea {
        background-color: rgba(226, 226, 226, 0.3);
        border: 2px dashed var(--BorderColor);
        border-radius: 5px;
        cursor: pointer;
        color: var(--BaseText);
    }

    .controlDropArea:hover {
        color: var(--BaseText);
        border: 2px dashed var(--primary);
    }

    .centerDropAreaText {
        text-align: center;
    }

    .previewFormArea {
        border: 2px dashed var(--BorderColor);
        background-color: rgb(245, 245, 245, .3);
        border-radius: 5px;
        text-align: center;
        color: rgb(204, 204, 204);
    }

    .form-settings .p-tabview-nav {
        display: none;
    }

    .form-settings .p-panel {
        box-shadow: none;
    }
    /* P-Toolbar Customizations */

    .mobile-menu-tb, .mobile-app-menu {
        display: none;
    }
    /* "Settings" Buttons and Panels - Reworked */
    .sbda-parent {
        margin-top: 50px;

        .tab-view {
            position: relative;

            &.col {
                max-width: calc(100vw - 12rem);
            }
        }

        .sbda-settings {
            background: var(--surfacePrimaryAlt);
            border-radius: 0;
            box-shadow: none;
            margin: 0;
            min-height: 80vh;

            .p-tabview-nav {
                display: none;
            }

            .p-tabview-panels{
                .p-tabview-nav {
                    display: flex;
                }
            }

            .p-panel {
                box-shadow: none;
            }

            .stroke-box {
                width: 100%;
                position: relative;
                border: var(--BorderColor) solid;
                border-radius: 0 0 4px 4px;
                padding: 20px 20px 5px;
                border-width: 0 1px 1px;

                .card {
                    padding: 0 15px 15px;
                }
            }
        }
    }
    /* Setting MenuU */
    .sbda-settings-menu {
        .p-button {
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 1.5rem;
            height: 100%;
            width: 100%;
            border-radius: 6px;
            background: var(--surfacePrimary) !important;
            box-shadow: 0 1px 3px 2px var(--dropShadowStrong);
            padding: 0;
            border: none;
            .pi {
                color: var(--BaseText);
                font-size: 1.7em;
            }
        }

        .active, &:hover, &:active, &:focus{
            .p-button {
                background-color: var(--primary) !important;

                .pi {
                    color: var(--InverseText)
                }
            }
        }        
    }

    /* Content Editor for Prime NG Customizations for Form Template */
    .editorLabel {
        font-size: .9rem;
        font-weight: 500;
    }

    .reverbEditor {
        min-height: 5rem;
    }

    .reverbDropArea {
        border: 2px dotted rgba(128, 128, 128, 0.25);
        border-radius: 5px;
        background: rgba(0, 122, 181, 0.025);

        p {
            margin: 0;
            padding: 0;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
    }

    .formDisplayArea {
        p {
            margin: 0;
            padding: 0;
            word-wrap: break-word;
        }
    }

    .disabled-text {
        color: rgba(0, 102, 211, 0.5);
    }

    .p-toolbar.sticky {
        position: sticky;
        top: 53px;
        z-index: 2;
    }

    .dynamicFormPreviewContainer {
        border: var(--BorderColor) solid;
        border-radius: 0 0 4px 4px;
        padding: 20px 20px 5px;
        border-width: 1px;
    }

    .formTemplateAreaImage {
        width: 200px;
        max-height: 200px;
        max-width: 100%;
    }

    .data-entry-form {

        .p-panel {
            .p-panel-header {
                background: var(--saltbox-blue);
                color: var(--surfacePrimary);
                padding: 0 0.5rem;
            }
        }

        .card-container {
            width: 100%;

            formly-field{
                margin: 0;

                formly-field{
                    margin: 0.5rem 0;
                }
            }

            .p-card{
                padding: 0;
                margin: 0;
                box-shadow: none;
                border: none;
                .p-card-body{
                    padding: 0;

                    .col-12.mb-2{
                        padding-left: 0;
                    }
                }
                .p-card-content{
                    padding: 0;
                }
            }
        }

        p-footer {
            display: flex;
            justify-content: right;
        }
    }

    .sbda-panel-container {
        .p-card {
            background: var(--surfaceSecondary);
        }

        .p-panel {
            margin: -8px 0 0 0;

            .p-toggleable-content {
                .p-panel-content {
                    .sbda-panel-body-container {
                        .p-card {
                            margin: -20px 0 10px 0;
                        }
                    }
                }
            }

            .p-panel-header {
                color: var(--bgInverse);
                background-color: var(--primary);
                height: 51px;
            }
        }
    }

    .data-definition-grid-container {

        .p-tabview-nav{
            display: flex !important;
        }

        .p-panel-header {
            padding: 0.5rem;
            display: flex;
            height: 2.4rem;

            .p-panel-title {
                align-self: center;
            }
        }

        .p-panel-content {
            box-shadow: rgba(0, 0, 0, 0.2) 0px 1px 3px 0px, rgba(0, 0, 0, 0.14) 0px 1px 1px 0px, rgba(0, 0, 0, 0.12) 0px 2px 1px -1px;
            border-radius: 2px;
            padding: 1em;
            margin-bottom: 1em;
            box-sizing: border-box;
            border: 1px solid var(--BorderColor);
            border-top: none;
        }

        .data-definition-tree-container {
            .p-tree {
                padding: 0;

                .p-tree-wrapper {
                    .p-tree-container {
                        p-treenode {
                            .p-treenode {
                                margin: 0;
                                padding: 0;
                                border-top: 1px solid var(--BorderColor);
                                
                                .p-treenode-content {
                                    &:focus{
                                        &.p-highlight{
                                            background-color: var(--surfaceBlue);
                                        }
                                    }
                                    &:hover {
                                      color: var(--TextDark);
                                    }
                                    .p-treenode-label {
                                        width: 100%;

                                        &:hover {
                                            background-color: transparent;
                                            color: var(--TextDark) !important;
                                        }

                                        .p-inputgroup-addon:first-of-type {
                                            border-left: 1px solid var(--bgInverseShade2);
                                        }
                                    }
                                }
                            }

                            .p-treenode-leaf {
                                &:hover {
                                    background: var(--dropShadowLight);
                                }

                                &:focus {
                                    background: none !important;
                                }

                                .p-treenode-selectable {
                                    .p-treenode-label {
                                        width: 100%;

                                        &:hover {
                                            background-color: transparent;
                                            color: var(--primary);
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        .data-entry-confirmation-message {
            width: 600px;
            max-width: 90vw;
            color: var(--primary);
            text-align: center;
            font-size: 1.5rem;
            line-height: 1.6rem;

            p {
                margin: 0;
                padding: 0;
            }
        }
    }

    .form-viewer-file-upload {
        .embedded-upload-form {
            .customFileUploader {
                .p-fileupload {
                    .p-fileupload-content {
                        padding: 0;
                        margin: 10px 0
                    }
                }
            }
        }
    }

    .format-column-settings-grid {
        .ag-root-wrapper.ag-layout-normal.ag-ltr {
            width: 100%;
        }

        .ag-drag-handle:hover {
            color: var(--primary);
        }

        .ag-theme-material .ag-ltr .ag-has-focus .ag-cell-focus:not(.ag-cell-range-selected) {
            border-color: var(--primary);
        }
    }

    .format-columns-data {

        p-dropdown {
            width: 7.5rem;

            .p-dropdown {
                padding: 0 5px 0 5px;

                .p-dropdown-label {
                    display: contents;
                }
            }
        }

        .ag-header-cell {
            background: var(--bgShade2);
            color: var(--BaseText);
            border-right: var(--BorderColor) 1px solid;

            &:hover {
                color: var(--InverseText);
            }
        }

        .ag-ltr .ag-cell {
            border-right: 1px solid var(--BorderColor);
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .ag-root {
            border-radius: 5px;
            overflow: hidden;
            border: var(--bgShade2) 1px solid;
        }

        .ag-drag-handle {
            position: relative;

            &::before,
            &::after {
                display: none;
                transition: all 0.3s;
            }

            &::before {
                content: "Reorder Column";
                position: absolute;
                top: 0.9rem;
                left: 1.5rem;
                background: var(--bgInverse);
                padding: 0 10px;
                line-height: 1.25rem;
                font-size: 0.7rem;
                border-radius: 3px;
                color: var(--InverseText);
            }

            &::after {
                top: 1.3rem;
                left: 1.3rem;
                border-style: solid;
                border-image: initial;
                content: "";
                height: 0px;
                width: 0px;
                position: absolute;
                pointer-events: none;
                border-color: black rgba(0, 0, 0, 0) rgba(0, 0, 0, 0);
                border-width: 3px;
                margin-left: -3px;
                transform: rotate(90deg);
            }

            &:hover {

                &::before,
                &::after {
                    display: block;
                }
            }
        }

        .ag-icon-grip:before {
            font-family: "sb";
            content: "\e824";
            color: var(--primaryActive);
        }
    }

    .confirm-message-container {
        .p-editor-container {
            .p-editor-toolbar {
                background-color: var(--bgShade2);
            }
        }
    }

    .jf-text-input .p-inputgroup-addon {
        min-width: 0;
    }

    .show-required .ag-header-cell-text:after {
        content: ' *';
    }
    /*No Border Accordions*/

    .accordion-nb, .p-panel .accordion-nb {
        .p-accordion {
            .p-accordion-tab, .p-accordion-header, .p-accordion-header-link {
                box-shadow: none !important;
                border: none !important;
            }

            p-accordtion-tab .p-accordion-tab {
                margin-bottom: 0;
            }
        }
    }
    /*Gauges*/

    .reading-block {
        font-size: 2rem !important;
    }
    /*KPIs*/

    .vis-wrap:empty {
        display: none !important;
    }

    .kpi-minmax {
        overflow: auto;
    }
    /*Visualization Tiles*/

    .tile-grid {
        margin-bottom: 0.25rem;

        .vis-wrap {
            max-width: fit-content;
        }

        .visualization-card {
            min-height: 8.5rem;
            color: var(--bgInverseShade2);
            height: 100%;
            width: 100%;
            margin: 0;

            h4 {
                color: var(--bgInverseShade3) !important;
                font-weight: normal !important;
                font-size: 0.95rem !important;
            }

            span.block.mb-3 {
                min-height: 2.5rem;
            }

            .tile-sm {
                width: 20rem;
                max-width: 100%;
                height: 6.55rem;
                position: relative;

                .tile-sm-error {
                    display: flex;
                    margin-top: -6rem;
                    position: relative;
                    z-index: 2;
                }
            }

            .tile-icon {
                width: 2.5rem;
                height: 2.5rem;
                border: 1px solid var(--surfacePrimaryAlt);
                border-radius: 0.3rem;
                background-color: var(--secondaryShade3);

                i {
                    color: var(--secondaryShade2);
                }
            }

            .kpi-value {
                font-size: 1.1rem !important;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                max-width: 100%;
                position: absolute;
                bottom: 1.8rem;
            }

            .kpi-bottom {
                font-size: 0.95rem;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                max-width: 100%;
                position: absolute;
                bottom: -0.4rem;
            }

            .tile-desc, .gauge-tile-desc, .mm-value {
                overflow: hidden;
                text-overflow: ellipsis;
                -webkit-line-clamp: 2;
                overflow-wrap: break-word;
                display: -webkit-box;
                -webkit-box-orient: vertical;
            }

            .mm-value {
                position: absolute;
                bottom: 1.8rem;
            }

            .mm-value, .kpi-bottom {
                max-height: 1.5rem;
            }

            .tile-desc {
                max-height: 2.8rem;
                max-width: 14rem;
            }
        }
    }
    /**/

    .tiles-spinner .p-progress-spinner-circle {
        animation: custom-progress-spinner-dash 1.5s ease-in-out infinite, tiles-loading-spinner-color 6s ease-in-out infinite;
    }

    @keyframes tiles-loading-spinner-color {
        100%, 0% {
            stroke: #d16b9b;
        }

        40% {
            stroke: #489fb5;
        }

        66% {
            stroke: #82c0cc;
        }

        80%, 90% {
            stroke: #ffa62b;
        }
    }

    // .app-fromat-components {
    //     align-items: center;
    //     height: 100%;
    //     padding-top: 0.5rem;

    //     .col {
    //         p-dropdown {
    //             width: 8.438rem;

    //             .p-dropdown {
    //                 align-items: center;
    //                 max-height: 3rem;

    //                 .p-dropdown-label {
    //                     display: table;
    //                 }
    //             }
    //         }
    //     }

    //     .format-inputs {
    //         border: 1px solid var(--BorderColor);
    //         height: 2.875rem;
    //         margin-top: 0.563rem
    //     }

    //     .currency-code-input {
    //         max-width: 5rem;
    //         text-align: center;
    //         display: flex;
    //         height: 2.813rem !important;
    //         margin-top: 0.375rem;
    //         padding: 0;
    //         font-size: 0.875rem;
    //     }

    //     .p-inputgroup-addon {
    //         border: 1px solid var(--bgShade2);
    //         background-color: var(--surfacePrimaryAlt);
    //     }

    //     p-inputnumber {
    //         .p-inputnumber {
    //             align-items: center;
    //             font-size: 0.8rem;

    //             input {
    //                 width: 3.125rem;
    //                 max-width: 3.125rem;
    //                 min-width: 3.125rem;
    //                 text-align: -webkit-center;
    //                 border: none;

    //                 &:hover {
    //                     border: none;
    //                     background-color: transparent;
    //                 }
    //             }
    //         }
    //     }
    // }

    .format-value-p-dropdown {
        align-items: center;
        max-height: 3rem;

        .p-dropdown-label {
            display: table !important;
        }
    }

    .format-symbol-p-dropdown {
        width: fit-content !important;

        .p-dropdown {
            height: inherit;
        }

        .p-dropdown-label {
            display: block !important;
        }
    }

    app-icon-renderer {
        display: flex;
        align-items: center !important;
    }

    app-symbol-dropdown-renderer {
        margin: 0 0 0 -1.188rem !important;
    }

    .external-link-icon {
        display: none;
    }

    /*Admin Screens*/

    .admin-wrapper {

        .card {
            width: fit-content;
            margin: 0px auto 3rem;
            max-width: 100%;
            border: 1px solid var(--BorderColor);
            box-shadow: none;
            border-radius: 0.3rem;
            padding: 1rem 2rem;
        }

        .admin-step {
            width: 30rem;
            max-width: 100%;
            display: flex;
            justify-content: center;
            padding: 0.5rem;
        }

        .admin-step-lg {
            max-width: 100%;
            min-width: calc(100vw - 15rem)
        }
    }
    /*Form Designer*/

    .notice-card.not-supported {
        display: none;
    }

    &.editor-full-width {
        .fullscreen-btn {
            transform: rotate(180deg);
        }

        .top-step{
            display: none;
        }

        .card.px-4{
            padding: 0;
            max-height: calc(100vh);
            height: calc(100vh - 16rem);
        }

        .form-designer .form-layout, p-steps {
            display: none;
        }

        .drawer {
            max-width: 100%;
            width: 100vw;

            p-checkbox, .section-description {
                display: none;
            }
        }

        .CodeMirror {
            height: calc(100vh - 21rem);
            width: 100%;
        }

        .field{
            max-width: 100%;
            width: 100%;
        }

        .admin-step .card {
            padding-top: 2rem;
        }
    }

    .p-tabview.admin .p-tabview-panels{
        padding: 0;
    }

    .section-controls {
        color: var(--BorderColor);
        font-size: 0.8rem;
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
    }

    .form-designer-elements {

        .form-section-btns{
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 0px !important;
            padding-right: 0.2rem;
        }

    }

    .form-designer {

        .form-layout {
            background: transparent;
            padding: 1rem 0.75rem;
            margin: 0.75rem 0;
            display: flex;
            justify-content: center;
            max-height: calc(100vh - 11.5rem);
            overflow: auto;

            .data-entry-confirmation-message {
                max-width: 37.5rem;
                min-height: calc(100vh - 15rem);
            }

            .card {
                width: 100%;
                display: flex;
                flex-direction: column;
                flex-wrap: nowrap;
                height: fit-content;
                max-height: none;
            }

            .drag-outline {
                background-image: repeating-linear-gradient(0deg, var(--bgShade2), var(--bgShade2) 0.313rem, transparent 0.313rem, transparent 0.625rem, var(--bgShade2) 0.625rem), repeating-linear-gradient(90deg, var(--bgShade2), var(--bgShade2) 0.313rem, transparent 0.313rem, transparent 0.625rem, var(--bgShade2) 0.625rem), repeating-linear-gradient(180deg, var(--bgShade2), var(--bgShade2) 0.313rem, transparent 0.313rem, transparent 0.625rem, var(--bgShade2) 0.625rem), repeating-linear-gradient(270deg, var(--bgShade2), var(--bgShade2) 0.313rem, transparent 0.313rem, transparent 0.625rem, var(--bgShade2) 0.625rem);
                background-size: 0.1rem 100%, 100% 0.1rem, 0.1rem 100%, 100% 0.1rem;
                background-position: 0 0, 0 0, 100% 0, 0 100%;
                background-repeat: no-repeat;
                margin: 0.5rem 0 0;
                height: auto;
                min-height: 4rem;
            }

            app-dynamic-form-section{
                .drag-outline{
                    background: none;
                    margin: 0;
                    padding: 0;
                }
            }

            .assets-dropzone {
                height: auto;
                min-height: 10rem;
                flex-grow: 1;
            }

            .template-content {
                h1, h2 {
                    margin: 0;
                }
                img{
                    max-height: 3.8rem;
                    margin: auto;
                    vertical-align: middle;
                }
            }
            
            .form-section-btns{
                margin-top: -0.5rem;
                margin-bottom: 0;
            }

            .header-wrap {
                background-color: var(--surfacePrimaryAlt);
                height: fit-content;
                overflow: hidden;
                border-top-right-radius: 0.3rem;
                border-top-left-radius: 0.3rem;

                &:empty {
                    display: none;
                }

                .form-layout-header-text-editor {

                    .section-controls {
                        margin-bottom: 0 !important;
                        width: 100%;
                        height: fit-content;
                    }

                    .template-content {
                        width: 100%;
                        color: var(--secondary);
                        overflow: visible;
                        height: 100%;

                        h1{
                            font-size: 1.8rem;
                        }

                        p{
                            max-height: 3.8rem;
                            line-height: 1.1;
                            overflow: hidden;
                            vertical-align: middle;
                            margin: 0;
                        }
                    }

                    app-dynamic-form-section {
                        background-image: repeating-linear-gradient(0deg, var(--BorderColor), var(--BorderColor) 0.313rem, transparent 0.313rem, transparent 0.625rem, var(--BorderColor) 0.625rem), repeating-linear-gradient(90deg, var(--BorderColor), var(--BorderColor) 0.313rem, transparent 0.313rem, transparent 0.625rem, var(--BorderColor) 0.625rem), repeating-linear-gradient(180deg, var(--BorderColor), var(--BorderColor) 0.313rem, transparent 0.313rem, transparent 0.625rem, var(--BorderColor) 0.625rem), repeating-linear-gradient(270deg, var(--BorderColor), var(--BorderColor) 0.313rem, transparent 0.313rem, transparent 0.625rem, var(--BorderColor) 0.625rem);
                        background-size: 0.1rem 100%, 100% 0.1rem, 0.1rem 100%, 100% 0.1rem;
                        background-position: 0 0, 0 0, 100% 0, 0 100%;
                        background-repeat: no-repeat;
                        width: 100%;
                        margin: 0;
                        display: inline-flex;
                        height: 100%;
                    }
                }
            }         
        }

    }

    app-dynamic-form-setup{
        .form-designer .form-layout{
            background-color: var(--surfacePrimary);
        }
    }

    app-form-layout-preview {
      .ql-editor{
          padding: 0.25rem 0.5rem;
          ol, ul {
            padding: 0;
          }
      }
    }

    app-form-renderer{
      .p-panel{
          box-shadow: none;
      }


      .ql-editor{
          padding: 0.25rem 0.5rem;
          ol, ul {
            padding: 0;
          }
      }

    }

    .drawer {
        max-width: 24rem;
        overflow-y: auto;
        min-height: calc(100vh - 10rem);

        .admin-step-nav {
            .p-steps, p-stepper {
                padding:0;
            }
        }

        .admin-btns{
            display: flex;
            flex-direction: row-reverse; 
            justify-content: space-between;
            flex-grow: 1;
            width: 100%;
            align-items: flex-end;

            .p-button{
                height: fit-content;
            }
        }

        .asset-wrapper{
            background-image: repeating-linear-gradient(0deg, var(--surfacePrimaryAlt), var(--surfacePrimaryAlt) 0.313rem, transparent 0.313rem, transparent 0.625rem, var(--surfacePrimaryAlt) 0.625rem), repeating-linear-gradient(90deg, var(--surfacePrimaryAlt), var(--surfacePrimaryAlt) 0.313rem, transparent 0.313rem, transparent 0.625rem, var(--surfacePrimaryAlt) 0.625rem), repeating-linear-gradient(180deg, var(--surfacePrimaryAlt), var(--surfacePrimaryAlt) 0.313rem, transparent 0.313rem, transparent 0.625rem, var(--surfacePrimaryAlt) 0.625rem), repeating-linear-gradient(270deg, var(--surfacePrimaryAlt), var(--surfacePrimaryAlt) 0.313rem, transparent 0.313rem, transparent 0.625rem, var(--surfacePrimaryAlt) 0.625rem);
            background-size: 0.1rem 100%, 100% 0.1rem, 0.1rem 100%, 100% 0.1rem;
            background-position: 0 0, 0 0, 100% 0, 0 100%;
            background-repeat: no-repeat;
            padding: 0.5rem;
            min-height: calc(100vh - 30rem);
            margin: 1rem 0;
        }

        app-dynamic-form-advanced-settings{
            .card{
                max-height: calc(100vh - 22rem);
                overflow: auto;
            }
        }

        .p-accordion .p-tabview .p-tabview-nav li {
            &.p-highlight .p-tabview-nav-link {
                box-shadow: none;
                color: var(--primary);
            }

            .p-tabview-nav-link {
                background-color: var(--surfacePrimary);
                color: var(--BaseText);
            }

            &:not(.p-highlight):not(.p-disabled):hover .p-tabview-nav-link {
                border: none;
            }
        }

        .draggable-assets {
            max-height: calc(100vh - 32rem);
            overflow-x: auto;

            .asset-card {
                background: var(--surfacePrimary);
                border: 1px solid var(--primary);
                border-radius: 0.3rem;
                padding: 0.5rem 1rem;
                display: flex;
                font-size: 1rem;
                margin: 0.5rem 0;
                justify-content: space-between;
                cursor: grab;

                span {
                    display: inline-flex;
                    align-items: center;

                    h4 {
                        font-weight: normal;
                        display: inline-block;
                        margin: 0 1rem;
                    }

                    i {
                        font-size: 1.4rem;
                    }
                }

                i.sb-icon-drag {
                    align-self: center;
                    font-size: 1.5rem;
                }
            }
        }
    }

    .form-designer .form-layout {
        p-accordion.p-panel{
                .p-accordion{
                    .p-accordion-tab{
                        box-shadow: none;
                    }
                    .p-accordion-content{
                    box-shadow: var(--dropShadowLight) 0px 3px 1px -2px, var(--dropShadowLight) 0px 2px 2px 0px, var(--dropShadowLight) 0px 1px 5px 0px;
                    }
                }
        }
        
        .data-entry-form {
            background: var(--surfacePrimary);
            display: flex;
            justify-content: center;

            .section-controls {
                display: none;
            }

            &.card{
                box-shadow: 0 1px 5px 2px var(--dropShadowLight), 0 1px 1px 0 var(--dropShadowLight), 0 2px 1px -1px var(--dropShadowLight);
                -webkit-box-shadow: 0 1px 5px 2px var(--dropShadowLight), 0 1px 1px 0 var(--dropShadowLight), 0 2px 1px -1px var(--dropShadowLight);
                -moz-box-shadow: 0 1px 5px 2px var(--dropShadowLight), 0 1px 1px;
            }

            .header-wrap {
                height: fit-content;
                border-top-right-radius: 0.3rem;
                border-top-left-radius: 0.3rem;

                &:empty {
                    display: none;
                }

                .formTemplateAreaImage {
                    margin: 0;
                }

                .form-layout-header-text-editor {
                    
                    app-dynamic-form-section{
                        background-image: none;
                    }

                    .section-controls {
                        display: none;
                    }

                    .template-content {
                        width: 100%;
                        color: var(--secondary);
                        max-height: 100%;
                        margin: 0;
                        display: flex;
                        align-items: center;

                        div{
                            height: 100%;

                            .section-content.ql-editor{
                                height: 100%;
                                padding: 0;
                                margin: 0;
                                line-height: 1;
                                overflow: visible;
                            }
                        }

                        p, h1, h2, pre {
                            margin: 0;
                            overflow: visible;
                            line-height: 1;
                        }
                    }

                    app-template-area {
                        width: 100%;
                        margin: 0;
                        display: inline-flex;
                        height: 100%;
                    }
                }
            }

        }
    }

    .form-settings-checkbox {
        input {
            height: 1.25rem;
            width: 1.25rem !important;
        }
    }

    .hyperlink-renderer:hover .external-link-icon {
        display: inline-block;
    }

    .form-settings-checkbox {
        input {
            height: 1.25rem;
            width: 1.25rem !important;
        }
    }

    .actionable-grid-format-pinput {
        width: 6rem;
        .p-inputnumber .p-inputnumber-input.p-inputtext {
          width: 5rem;
          min-width: initial;
          max-width: 5rem;
          padding-top: 0.25rem;
          padding-bottom: 0;
          border: none;
          height: 2.675rem !important;
        }
    }

    app-master-detail-renderer {
        padding: 0.5rem;
        background: var(--surfacePrimaryAlt);
        .ag-root-wrapper.ag-layout-normal{
            height: calc(100% + 0.8rem);
        }
    }

    .nested-grid{
        .p-panel-secondary{
            .p-panel .p-panel-content{ 
                .nested-grid{
                    border: none;
                }
                .card{
                    margin: 0 0.1rem 0.1rem;
                }
            }

            .p-panel-toggleable{
                &.p-panel-expanded .p-toggleable-content:not(.ng-animating){
                   overflow: hidden !important;
                }
                .p-toggleable-content:not(.ng-animating){
                   overflow: hidden !important;
                }
               }

            .p-toolbar-group-center{
                text-align: center;

                .p-button-text span{
                    color: var(--InverseText) !important;
                }
            }

            .p-panel-header{
                position: sticky;
                top: -1rem;
                z-index: 3;
                background-color: var(--secondaryShade2);
                border: none;
                flex-direction: row-reverse;

                .p-panel-header-icon{
                    width: 1rem;
                    height: 1rem;
                    color: var(--InverseText);
                }
            }

            .p-panel-content{
                padding: 0;
            }

            .p-panel-header .p-toolbar.p-toolbar-secondary{
                margin: 0;
            }

            .sb-field-collection, .sb-field-object {

                .p-panel-header{
                    position: sticky;
                    top: 2rem;
                    z-index: 2;

                    .p-panel-header-icon{
                        color: var(--BaseText);
                    }
                }

                .p-toolbar.p-toolbar-secondary, .p-panel-header {
                    background-color: var(--bgShade2);
                    padding: 0 0 0 1rem;
                    margin-top: -0.1rem;

                    h3 {
                        color: var(--BaseText);
                        margin: 0.5rem 0.5rem 0.5rem 0;

                        i {
                            transform: rotate(-180deg) scaleX(-1) !important;

                            &.hidden{
                                display: inline-block !important;
                            }
                        }
                    }

                    .p-toolbar-group-center{
                        text-align: center;
                        color: var(--BaseText);
            
                        .p-button-text span{
                            color: var(--primary) !important;
                        }
                    }
            
                }
            }
        }
    }

    .p-dialog-content {
        app-sb-field-collection-form, app-sb-field-object, app-sb-field-collection-grid, app-sb-field-collection-repeat {
            .p-panel-secondary{

                .p-panel-header-icon{
                    color: var(--BaseText);
                }

                .p-panel .p-panel-content .nested-grid{
                    border: none;
                }

                .p-panel-header {
                    position: sticky;
                    top: 0;
                    z-index: 3;

                    .p-panel-header-icon{
                        color: var(--BaseText);
                    }
                }

                .p-toolbar.p-toolbar-secondary, .p-panel-header {
                    background-color: var(--bgShade2);
                    padding: 0 0 0 0.5rem;
                    margin-top: -0.1rem;

                    h3 {
                        color: var(--BaseText);
                        margin: 0.5rem 0.5rem 0.5rem 0;

                        i {
                            transform: rotate(-180deg) scaleX(-1) !important;

                            &.hidden{
                                display: inline-block !important;
                            }
                        }
                    }

                    .p-toolbar-group-center{
                        text-align: center;
                        color: var(--BaseText);
            
                        .p-button-text span{
                            color: var(--primary) !important;
                        }
                    }

                    .p-toolbar-group-end{
                        margin-right: 0.25rem;
                    }
                }

                app-sb-field-collection-form, app-sb-field-object, app-sb-field-collection-grid, app-sb-field-collection-repeat {
                    .p-panel-header{   
                        top: 2rem;
                        z-index: 2;
                    }
                }
            }
        }
    }

    .p-panel .p-panel-content {
        .nested-grid {
            border: 1px solid var(--bgShade3);
            background-color: var(--surfacePrimary);

            app-master-detail-renderer {
                padding: 0;
            }

            .ag-body-vertical-scroll-viewport {
                overflow: hidden;
            }

            .nested-grid {
                border: none;
                padding: 0.5rem;
                background-color: var(--surfacePrimaryAlt);

                .nested-grid {
                    .nested-grid {
                        //Reduce left/right padding to almost 0 after 4 levels deep
                        padding: 0.1rem;
                    }
                }

                ag-grid-angular {
                    border-right: 1px solid var(--bgShade3);
                    border-left: 1px solid var(--bgShade3);
                    border-bottom: 1px solid var(--bgShade3);
                }

                .p-toolbar.p-toolbar-secondary {
                    border-right: 1px solid var(--bgShade3);
                    border-left: 1px solid var(--bgShade3);
                    border-top: 1px solid var(--bgShade3);
                }
            }

            .ag-cell-wrapper.ag-cell-expandable.ag-row-group {
                padding-left: 0;
            }

            .ag-theme-material {
                border-bottom: 2px solid var(--surfacePrimary);
            }

            .p-toolbar.p-toolbar-secondary {
                background-color: var(--bgShade2);
                padding: 0 1rem;
                margin-top: -0.1rem;

                h3 {
                    color: var(--BaseText);
                    margin: 0.5rem 0.5rem 0.5rem 0;

                    i {
                        transform: rotate(-180deg) scaleX(-1) !important;
                    }
                }
            }
        }
    }

    .business-validation-sidebar{
        .ag-theme-material{ 
            .ag-row-odd{
                background-color: var(--surfacePrimary);
            }
            .ag-row.ag-row-level-0{
                background-color: var(--surfacePrimaryAlt);
                .ag-group-value{
                    max-width: fit-content;
                    font-weight: bold;
                }
                .ag-group-child-count{
                    border-color: var(--primary);
                    background-color: var(--surfacePrimary);
                    color: var(--primary);
                }
            }
            .ag-header, .ag-header-cell{
                height: 2.5rem !important;
                min-height: 2.5rem !important;
            }
        }
    }

    app-validation-rules{
        .ag-header, .ag-header-cell{
            height: 2.5rem !important;
            min-height: 2.5rem !important;
        }
        margin-top: 0.1rem;
    }

    .javascript-editor-container {
        display: flex;
        flex-direction: column;
        height: 100%;

        .top-container {
            flex-grow: 1;

            .code-editor {
                display: flex;
                flex-direction: column;
                height: 100%;

                .CodeMirror {
                    width: 100%;
                    flex-grow: 1;

                    .CodeMirror-scroll {
                        margin-right: 0;
                    }
                }
            }

            .tips {
                position: relative;
                overflow: hidden;
                flex-grow: 1;

                .p-sidebar {
                    min-width: auto;
                }

                .p-sidebar .p-sidebar-content {
                    padding: 0.75rem;
                }

                .p-sidebar .p-sidebar-header {
                    padding: 0;
                }

                .p-sidebar .p-sidebar-footer {
                    padding: 0;
                }
            }
        }

        .bottom-container {
            .lint-errors {
                max-height: 8.125rem;
                min-height: 8.125rem;
                overflow-y: auto;

                table {
                    width: 100%;
                    border-collapse: collapse;
                    font-size: small;

                    th,
                    td {
                        border: 1px solid var(--BorderColor);
                        ;
                        padding: 0.313rem;
                        text-align: left;
                    }

                    tr.error {
                        background-color: var(--surfaceRed);
                    }

                    tr.warning {
                        background-color: var(--surfaceOrange);
                        ;
                    }
                }
            }
        }
    }

    .CodeMirror-hints,
    .CodeMirror-hint,
    .CodeMirror-hint-active {
        z-index: 99999999 !important;
    }

    .validation-errors-icon {
      font-size: 2.1em;
      color: var(--warning);
      vertical-align: bottom;
      margin-right: 0.8rem;
    
      .p-badge {
        font-size: small !important;
        min-width: 1.2rem;
        height: 1.2rem;
        line-height: 1.2rem;
        top: 0.4rem;
        background-color: var(--surfacePrimary);
        border: 1px solid;
      }
  
      &.failure .p-badge {
        color: var(--danger)Bright;
      }
    
      &.warning .p-badge {
        color: var(--warningLight);
      }
    }

    .p-panel .p-panel-content{
        .table-tile{
            .ag-header, .ag-header-cell{
                height: 2.5rem !important;
                min-height: 2.5rem !important;
            }

            .p-toolbar.p-toolbar-secondary {
                background-color: var(--bgShade2);
                h3 {
                    color: var(--BaseText);
                    i {
                        transform: rotate(-180deg) scaleX(-1) !important;
                    }
                }
            }       
        }
    }

    .actionable-grid-d-container{
        .p-panel-content{
            padding: 0;
        }
    }

    .form-calendar-widget {
        width: 100%;

        p-calendar {
            width: 100%;

            .p-button {
                background-color: var(--surfacePrimary);
                color: var(--BaseText);
                height: 2.2rem !important;
                border: 1px solid var(--InverseText);
                border-image: initial;
                margin: 0.18rem;
                border-left: 0px;
            }
        }
    }

    .disabled-element {
        pointer-events: none;
        opacity: 0.5;
        background-color: var(--surfaceSecondary);
    }
    .json-form,
    .notice-bold {
      display: inline-block;
      padding: 1px 6px;
      border: 0;
      border-left: 5px solid var(--bgInverseShade3);
      background: transparent;
      background: linear-gradient(90deg, var(--bgInverseShade3), transparent);
      margin: 5px 0;
      max-width: 45rem;
    
      &.info-light {
        border-left-color: var(--bgInverseShade3);
        background: linear-gradient(90deg, var(--bgShade2), transparent);
        h3{
          color: var(--BaseText);
          font-weight: 500;
        }
      }
    
      &.info {
        border-left-color: var(--primary);
        background: linear-gradient(90deg, var(--surfaceBlue), transparent);
        h3{
          color: var(--primary);
          font-weight: 500;
        }
      }
    
      &.warning {
        border-left-color: var(--warning);
        background: linear-gradient(90deg, var(--surfaceOrange), transparent);
        h3{
          color: var(--warningDark);
          font-weight: 500;
        }
      }
    
      &.danger {
        border-left-color: var(--danger);
        background: linear-gradient(90deg, var(--surfaceRed), transparent);
        h3{
          color: var(--danger);
          font-weight: 500;
        }
      }
    }
    
    app-sb-formly-renderer-dialog{
        app-sb-field-collection-grid{
            app-sb-formly-renderer-dialog{
                .p-dialog{
                    max-width: 35rem;
                }
            }
        }
    }

    .lookup-grid.ag-theme-material {
        .ag-row-selected {
            border: 1px solid var(--primaryActive);
        }
    }
    .ag-theme-material {
        app-lookup-selector {
            height: 2rem;
            margin-top: 0;
        }
    }

    .p-inputgroup-addon.lookup-addon{
        height: 2.5rem;
        border-left: 0;
    }

    .max-w-50rem{
        max-width: 50rem;
    }

    .form-errors{
        &.card{
            max-width: 40rem !important;
            min-height: 43rem;
            position: relative;
        }
        h2{
            position: absolute;
            top: 7rem;
            left: 4.35rem;
            color: var(--primary);
            font-weight: normal;
        }
        img, div{
            max-width: 32rem;
            width: 100%;
        }
    }

    .datastore-data-grid {
        .ag-display-size {
            border: 1px solid var(--bgShade3);
        }
        .p-panel .p-panel-content {
            padding: 0;
        }
    }

    .search-container, .search-container-no-icon {
        &.p-input-icon-left > i{
          top: 48%;
          left: 0.5rem;
          font-size: 1.25rem;
          &.pi{
            font-size: 0.875rem;
            top: 53%;
          }
        }
        .p-inputtext {
          background-color: var(--surfacePrimary);
          border: 1px solid var(--BorderColor);
          padding-top: 0.35rem;
          padding-bottom: 0.35rem;
          padding-left: 2rem;
          margin: 0.5rem 0;
      
          .close-me {
            display: inline-block;
            width: 2.5rem;
            margin-left: -2.5rem;
          }
      
          &:enabled:hover,
          &:not(.p-disabled):hover,
          &:enabled:focus,
          &:not(.p-disabled):focus {
            border: 1px solid var(--primary);
          }
          &:disabled {
            background-color: var(--surfaceSecondary);
            border-color: var(--BorderColor) !important;
          }
        }
      
        input::placeholder,
        input:focus::placeholder {
          opacity: 1;
        }
        &.search-container-no-icon {
          .p-inputtext {
            padding-left: 0.5rem;
          }
        }
        .p-inputgroup-addon{
          margin: 0.5rem 0;
          &:last-child .p-button{
            border-top-left-radius: 0;
            border-bottom-left-radius: 0;
          }
        }
      }
      .search-container-no-icon {
        .p-inputtext {
          padding-left: 0.5rem;
        }
      }

    /* Classes for event colors */
    .focused-event {
        box-shadow: 0 0 0 0.10rem var(--primary) !important;
    }
    
  .highlight-word {
    background-color: var(--warningLight);
    font-weight: bold;
  }

  .color-picker-input-group {
    .p-colorpicker{
      max-width: 2.25rem;
    }
  }
  .p-colorpicker-color-selector {
    background: linear-gradient(to top, black, transparent), linear-gradient(to right, white, transparent); // FIXES PrimeNG BUG https://github.com/primefaces/primeng/issues/16449
  }
  .p-colorpicker-hue {
    background: linear-gradient(
      to bottom,
      #ff0000 0%,   /* Red */
      #ff00ff 16.6%, /* Magenta */
      #0000ff 33.3%, /* Blue */
      #00ffff 50%,   /* Cyan */
      #00ff00 66.6%, /* Green */
      #ffff00 83.3%, /* Yellow */
      #ff0000 100%   /* Back to Red */
    ); // FIXES PrimeNG BUG https://github.com/primefaces/primeng/issues/16449
  }
} 

.grid-layouts-menu {
  width: auto !important;
  min-width: fit-content !important;
  
  .p-submenu-header {
    &:first-child {
      display: none;
    }
    
    &:not(:first-child) {
      font-weight: bold;
    }
  }

  .p-button-sm {
    width: 1.5em !important;
    height: 1.5em !important;
  }

  .p-splitter {
    .p-splitter-gutter-handle, .p-splitter-gutter-handle:focus {
      box-shadow: none;
    }
  }

  .p-scrollpanel {
    .p-scrollpanel-bar, .p-scrollpanel-bar:focus {
      box-shadow: none;
    }
  }

}

//Closing for body class wrapper. Custom CSS must be added above this line

@layer primeng {
  body.saltbox-data-app.sbdapp {
    
  .fake-field, .fake-field-readonly{
    .grid{
      background-color: var(--surfaceSecondary);
      border: none;
      border-bottom: 2px solid var(--BorderColor);
      width: 100%;
      padding: 1rem 0.5rem 0.25rem;
      border-radius: 0.25rem;
      max-width: 30rem;
    }
    &.p-float-label{
      label{
        top: 1rem !important;
        font-size: 0.7rem;
        left: 0.7rem;
      }
    }
    img{
      width: 1.5rem;
      margin-right: 0.375rem;
    }
  }

  .fake-field-readonly .grid {
    background-color: var(--surfacePrimaryAlt);
    border-bottom-color: var(--bgShade3);
  }

  .max-h-unset {
    max-height: unset !important;
  }

  .no-label{
    .p-inputtext{
      padding: 0.62rem 0.5rem !important;
    }
  }
  .p-inputtext.no-label{
    padding: 0.62rem 0.5rem !important;
  }

  .min-h-3rem{
    min-height: 3rem;
  }
  .min-h-4rem{
    min-height: 4rem;
  }
  .min-h-5rem{
    min-height: 5rem;
  }

  .p-modal-compact-header{
    .p-dialog-header{
      padding: 0.5rem !important;
    }
  }
  .p-modal-compact-content{
    .p-dialog-content{
      padding: 0.25rem 0.5rem !important;
    }
  }
  .p-modal-compact-footer{
    .p-dialog-footer{
      padding: 0.5rem !important;
    }
  }
  
  }

  
}
