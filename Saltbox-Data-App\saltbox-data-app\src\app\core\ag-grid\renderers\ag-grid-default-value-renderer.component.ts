import { Component } from '@angular/core';
import { ICellRendererAngularComp } from 'ag-grid-angular';
import { ICellRendererParams } from 'ag-grid-community';
import { ProfileProperty } from '../../models/profile-property';
import { UserService } from '../../services/user.service';
import { DefaultValue } from '../../models/default-value';
import { JsonDataTypes, JsonStringFormats } from 'src/app/shared/enums/json-schema.enum';
import { deepCopy } from 'src/app/shared/utilities/copy.functions';
import { ButtonModule } from 'primeng/button';
import { DropdownModule } from 'primeng/dropdown';
import { CheckboxModule } from 'primeng/checkbox';
import { SharedModule } from 'primeng/api';
import { DialogModule } from 'primeng/dialog';
import { InputTextModule } from 'primeng/inputtext';
import { FormsModule } from '@angular/forms';


@Component({
    selector: 'app-default-value-renderer',
    templateUrl: './ag-grid-default-value-renderer.component.html',
    standalone: true,
    imports: [FormsModule, InputTextModule, DialogModule, SharedModule, CheckboxModule, DropdownModule, ButtonModule]
})
export class AgGridDefaultValueRendererComponent implements ICellRendererAngularComp {

  dialogVisible = false;
  profileProperties: ProfileProperty[];
  defaultValue: DefaultValue;
  columnConfig: any;

  get displayValue() {
    return this.columnConfig?.defaultValue?.isUserProfileValue 
    ? 'User Profile Value'
    : this.columnConfig?.defaultValue?.value;
  }

  get defaultValueInputText(): string {
    return this.defaultValue.isUserProfileValue ? '' : this.defaultValue.value;
  }

  set defaultValueInputText(value: string) {
    this.defaultValue.value = value;
  }

  get showRenderer(): boolean {
    // The default value option for date fields is hidden for now.
    return this.columnConfig.type !== JsonDataTypes.Object
      && this.columnConfig.type !== JsonDataTypes.Collection
      && this.columnConfig.format !== JsonStringFormats.DateTime 
      && this.columnConfig.format !== JsonStringFormats.Date;
  }

  agInit(cellRendererParams: ICellRendererParams): void {
    this.columnConfig = cellRendererParams.data;
  }

  constructor(private userService: UserService) { }

  refresh(): boolean {
    return false;
  }

  getProfileProperties(): void {
    this.userService.getCompleteUserProfilePropertyList().subscribe({
      next: (userProfileProperties: ProfileProperty[]) => {
        this.profileProperties = userProfileProperties;
      }
    });
  }

  showDialog(visible: boolean): void {
    if (!this.profileProperties) {
      this.getProfileProperties();
    }
    this.dialogVisible = visible;
    this.defaultValue = visible ? deepCopy<DefaultValue>(this.columnConfig?.defaultValue) ?? new DefaultValue() : this.defaultValue;
  }

  reset(): void {
    this.columnConfig.defaultValue = new DefaultValue();
  }

  resetValue() {
    this.defaultValue.value = '';
  }

  save(): void {
    this.columnConfig.defaultValue = this.defaultValue;
    this.dialogVisible = false;
  }
}
