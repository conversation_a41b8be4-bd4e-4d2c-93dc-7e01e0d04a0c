{"ast": null, "code": "import { CustomCellRenderer } from './ag-grid-custom-cell-renderer';\nimport { getDateByISO8601Format } from 'src/app/shared/utilities/date.functions';\nimport { formatCurrency, formatDate } from 'src/app/shared/utilities/format.functions';\nimport { JsonStringFormats } from 'src/app/shared/enums/json-schema.enum';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/actionable-grid/services/change-history.service\";\nimport * as i2 from \"../../services/notification.service\";\nimport * as i3 from \"../../services/ag-grid.service\";\nfunction AgGridHyperlinkRendererComponent_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    let tmp_1_0;\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (tmp_1_0 = ctx_r0.cellRendererParams == null ? null : ctx_r0.cellRendererParams.valueFormatted) !== null && tmp_1_0 !== undefined ? tmp_1_0 : ctx_r0.cellRendererParams == null ? null : ctx_r0.cellRendererParams.value, \" \");\n  }\n}\nfunction AgGridHyperlinkRendererComponent_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\")(1, \"a\", 0);\n    i0.ɵɵelement(2, \"i\", 1);\n    i0.ɵɵtext(3);\n    i0.ɵɵelement(4, \"i\", 2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"href\", ctx_r0.urlString, i0.ɵɵsanitizeUrl)(\"target\", ctx_r0.cellRendererParams.link.openIn);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.value, \" \");\n  }\n}\nexport class AgGridHyperlinkRendererComponent extends CustomCellRenderer {\n  constructor(changeHistoryService, notificationService, aGGridService) {\n    super(changeHistoryService, notificationService, aGGridService);\n  }\n  refresh(cellRendererParams) {\n    this.setHyperlinkValues(cellRendererParams);\n    return true;\n  }\n  agInit(cellRendererParams) {\n    super.agInit(cellRendererParams);\n    this.setHyperlinkValues(cellRendererParams);\n  }\n  setHyperlinkValues(cellRendererParams) {\n    this.value = cellRendererParams.value;\n    if (cellRendererParams.format.baseFormat === JsonStringFormats.DateTime) {\n      this.value = formatDate(getDateByISO8601Format(cellRendererParams.value)?.toString());\n    } else if (cellRendererParams.format.currency && cellRendererParams.format.decimalPlaces) {\n      this.value = formatCurrency(cellRendererParams.value, cellRendererParams.format.currency, cellRendererParams.format.decimalPlaces);\n    }\n    this.setUrl(cellRendererParams);\n  }\n  setUrl(cellRendererParams) {\n    if (cellRendererParams.node.group) return;\n    this.urlString = cellRendererParams.link.urlAlias;\n    cellRendererParams.link.urlSubstitutions?.forEach((param, index) => {\n      const placeholder = `{${index}}`;\n      const value = param.isProjectVariable ? cellRendererParams.projectVariables?.find(v => v.variable === param.urlName)?.configValue : cellRendererParams.data[param.urlName];\n      this.urlString = this.urlString.replace(placeholder, `${value ?? ''}`);\n    });\n  }\n  static {\n    this.ɵfac = function AgGridHyperlinkRendererComponent_Factory(t) {\n      return new (t || AgGridHyperlinkRendererComponent)(i0.ɵɵdirectiveInject(i1.ChangeHistoryService), i0.ɵɵdirectiveInject(i2.NotificationService), i0.ɵɵdirectiveInject(i3.AGGridService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AgGridHyperlinkRendererComponent,\n      selectors: [[\"app-hyperlink-renderer\"]],\n      standalone: true,\n      features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n      decls: 2,\n      vars: 2,\n      consts: [[1, \"hyperlink-renderer\", 3, \"href\", \"target\"], [1, \"pi\", \"pi-link\"], [1, \"pi\", \"pi-external-link\", \"external-link-icon\"]],\n      template: function AgGridHyperlinkRendererComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, AgGridHyperlinkRendererComponent_Conditional_0_Template, 2, 1, \"div\")(1, AgGridHyperlinkRendererComponent_Conditional_1_Template, 5, 3, \"span\");\n        }\n        if (rf & 2) {\n          i0.ɵɵconditional(0, !ctx.showEditor && (ctx.cellRendererParams == null ? null : ctx.cellRendererParams.node == null ? null : ctx.cellRendererParams.node.group) ? 0 : -1);\n          i0.ɵɵadvance();\n          i0.ɵɵconditional(1, ctx.showEditor ? 1 : -1);\n        }\n      },\n      encapsulation: 2\n    });\n  }\n}", "map": {"version": 3, "names": ["CustomCellRenderer", "getDateByISO8601Format", "formatCurrency", "formatDate", "JsonStringFormats", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "tmp_1_0", "ctx_r0", "cellRendererParams", "valueFormatted", "undefined", "value", "ɵɵelement", "ɵɵproperty", "urlString", "ɵɵsanitizeUrl", "link", "openIn", "AgGridHyperlinkRendererComponent", "constructor", "changeHistoryService", "notificationService", "aGGridService", "refresh", "setHyperlinkValues", "agInit", "format", "baseFormat", "DateTime", "toString", "currency", "decimalPlaces", "setUrl", "node", "group", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "urlSubstitutions", "for<PERSON>ach", "param", "index", "placeholder", "isProjectVariable", "projectVariables", "find", "v", "variable", "urlName", "config<PERSON><PERSON><PERSON>", "data", "replace", "ɵɵdirectiveInject", "i1", "ChangeHistoryService", "i2", "NotificationService", "i3", "AGGridService", "selectors", "standalone", "features", "ɵɵInheritDefinitionFeature", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "AgGridHyperlinkRendererComponent_Template", "rf", "ctx", "ɵɵtemplate", "AgGridHyperlinkRendererComponent_Conditional_0_Template", "AgGridHyperlinkRendererComponent_Conditional_1_Template", "ɵɵconditional", "showEditor"], "sources": ["C:\\Projects\\SaltboxActionableGrid\\Saltbox-Data-App\\saltbox-data-app\\src\\app\\core\\ag-grid\\renderers\\ag-grid-hyperlink-renderer.component.ts", "C:\\Projects\\SaltboxActionableGrid\\Saltbox-Data-App\\saltbox-data-app\\src\\app\\core\\ag-grid\\renderers\\ag-grid-hyperlink-renderer.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\r\nimport { CustomCellRenderer } from './ag-grid-custom-cell-renderer';\r\nimport { ICustomCellRendererParams } from './ag-grid-custom-cell-renderer-params';\r\nimport { NotificationService } from '../../services/notification.service';\r\nimport { getDateByISO8601Format } from 'src/app/shared/utilities/date.functions';\r\nimport { formatCurrency, formatDate } from 'src/app/shared/utilities/format.functions';\r\nimport { JsonStringFormats } from 'src/app/shared/enums/json-schema.enum';\r\nimport { ChangeHistoryService } from 'src/app/actionable-grid/services/change-history.service';\r\nimport { AGGridService } from '../../services/ag-grid.service';\r\n\r\n@Component({\r\n    selector: 'app-hyperlink-renderer',\r\n    templateUrl: './ag-grid-hyperlink-renderer.component.html',\r\n    standalone: true,\r\n})\r\n\r\nexport class AgGridHyperlinkRendererComponent extends CustomCellRenderer {\r\n    public urlString: string;\r\n    public value: string;\r\n\r\n    constructor(\r\n        changeHistoryService: ChangeHistoryService,\r\n        notificationService: NotificationService,\r\n        aGGridService: AGGridService) {\r\n        super(changeHistoryService, notificationService, aGGridService);\r\n    }\r\n\r\n    refresh(cellRendererParams: ICustomCellRendererParams): boolean {\r\n        this.setHyperlinkValues(cellRendererParams);\r\n        return true;\r\n    }\r\n\r\n    agInit(cellRendererParams: ICustomCellRendererParams): void {\r\n        super.agInit(cellRendererParams);\r\n        this.setHyperlinkValues(cellRendererParams);\r\n    }\r\n\r\n    private setHyperlinkValues(cellRendererParams: ICustomCellRendererParams) {\r\n        this.value = cellRendererParams.value;\r\n        if (cellRendererParams.format.baseFormat === JsonStringFormats.DateTime) {\r\n            this.value = formatDate(getDateByISO8601Format(cellRendererParams.value)?.toString());\r\n        }\r\n        else if (cellRendererParams.format.currency && cellRendererParams.format.decimalPlaces) {\r\n            this.value = formatCurrency(\r\n                cellRendererParams.value, cellRendererParams.format.currency, cellRendererParams.format.decimalPlaces);\r\n        }\r\n\r\n        this.setUrl(cellRendererParams);\r\n    }\r\n\r\n    private setUrl(cellRendererParams: ICustomCellRendererParams) {\r\n        if (cellRendererParams.node.group)\r\n            return;\r\n\r\n        this.urlString = cellRendererParams.link.urlAlias;\r\n        cellRendererParams.link.urlSubstitutions?.forEach((param, index) => {\r\n            const placeholder = `{${index}}`;\r\n            const value = param.isProjectVariable\r\n                ? cellRendererParams.projectVariables?.find(v => v.variable === param.urlName)?.configValue\r\n                : cellRendererParams.data[param.urlName];\r\n            this.urlString = this.urlString.replace(placeholder, `${value ?? ''}`);\r\n        });\r\n    }\r\n}\r\n", "@if (!showEditor && cellRendererParams?.node?.group) {\r\n  <div>\r\n  {{ cellRendererParams?.valueFormatted ?? cellRendererParams?.value }}\r\n  </div>\r\n}\r\n@if (showEditor) {\r\n    <span>\r\n        <a [href]=\"urlString\" [target]=\"cellRendererParams.link.openIn\" class=\"hyperlink-renderer\">\r\n            <i class=\"pi pi-link\"></i>\r\n            {{value}}\r\n            <i class=\"pi pi-external-link external-link-icon\"></i>\r\n        </a>\r\n    </span>\r\n}"], "mappings": "AACA,SAASA,kBAAkB,QAAQ,gCAAgC;AAGnE,SAASC,sBAAsB,QAAQ,yCAAyC;AAChF,SAASC,cAAc,EAAEC,UAAU,QAAQ,2CAA2C;AACtF,SAASC,iBAAiB,QAAQ,uCAAuC;;;;;;;ICLvEC,EAAA,CAAAC,cAAA,UAAK;IACLD,EAAA,CAAAE,MAAA,GACA;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IADNH,EAAA,CAAAI,SAAA,EACA;IADAJ,EAAA,CAAAK,kBAAA,OAAAC,OAAA,GAAAC,MAAA,CAAAC,kBAAA,kBAAAD,MAAA,CAAAC,kBAAA,CAAAC,cAAA,cAAAH,OAAA,KAAAI,SAAA,GAAAJ,OAAA,GAAAC,MAAA,CAAAC,kBAAA,kBAAAD,MAAA,CAAAC,kBAAA,CAAAG,KAAA,MACA;;;;;IAIMX,EADJ,CAAAC,cAAA,WAAM,WACyF;IACvFD,EAAA,CAAAY,SAAA,WAA0B;IAC1BZ,EAAA,CAAAE,MAAA,GACA;IAAAF,EAAA,CAAAY,SAAA,WAAsD;IAE9DZ,EADI,CAAAG,YAAA,EAAI,EACD;;;;IALAH,EAAA,CAAAI,SAAA,EAAkB;IAACJ,EAAnB,CAAAa,UAAA,SAAAN,MAAA,CAAAO,SAAA,EAAAd,EAAA,CAAAe,aAAA,CAAkB,WAAAR,MAAA,CAAAC,kBAAA,CAAAQ,IAAA,CAAAC,MAAA,CAA0C;IAE3DjB,EAAA,CAAAI,SAAA,GACA;IADAJ,EAAA,CAAAK,kBAAA,MAAAE,MAAA,CAAAI,KAAA,MACA;;;ADMZ,OAAM,MAAOO,gCAAiC,SAAQvB,kBAAkB;EAIpEwB,YACIC,oBAA0C,EAC1CC,mBAAwC,EACxCC,aAA4B;IAC5B,KAAK,CAACF,oBAAoB,EAAEC,mBAAmB,EAAEC,aAAa,CAAC;EACnE;EAEAC,OAAOA,CAACf,kBAA6C;IACjD,IAAI,CAACgB,kBAAkB,CAAChB,kBAAkB,CAAC;IAC3C,OAAO,IAAI;EACf;EAEAiB,MAAMA,CAACjB,kBAA6C;IAChD,KAAK,CAACiB,MAAM,CAACjB,kBAAkB,CAAC;IAChC,IAAI,CAACgB,kBAAkB,CAAChB,kBAAkB,CAAC;EAC/C;EAEQgB,kBAAkBA,CAAChB,kBAA6C;IACpE,IAAI,CAACG,KAAK,GAAGH,kBAAkB,CAACG,KAAK;IACrC,IAAIH,kBAAkB,CAACkB,MAAM,CAACC,UAAU,KAAK5B,iBAAiB,CAAC6B,QAAQ,EAAE;MACrE,IAAI,CAACjB,KAAK,GAAGb,UAAU,CAACF,sBAAsB,CAACY,kBAAkB,CAACG,KAAK,CAAC,EAAEkB,QAAQ,EAAE,CAAC;IACzF,CAAC,MACI,IAAIrB,kBAAkB,CAACkB,MAAM,CAACI,QAAQ,IAAItB,kBAAkB,CAACkB,MAAM,CAACK,aAAa,EAAE;MACpF,IAAI,CAACpB,KAAK,GAAGd,cAAc,CACvBW,kBAAkB,CAACG,KAAK,EAAEH,kBAAkB,CAACkB,MAAM,CAACI,QAAQ,EAAEtB,kBAAkB,CAACkB,MAAM,CAACK,aAAa,CAAC;IAC9G;IAEA,IAAI,CAACC,MAAM,CAACxB,kBAAkB,CAAC;EACnC;EAEQwB,MAAMA,CAACxB,kBAA6C;IACxD,IAAIA,kBAAkB,CAACyB,IAAI,CAACC,KAAK,EAC7B;IAEJ,IAAI,CAACpB,SAAS,GAAGN,kBAAkB,CAACQ,IAAI,CAACmB,QAAQ;IACjD3B,kBAAkB,CAACQ,IAAI,CAACoB,gBAAgB,EAAEC,OAAO,CAAC,CAACC,KAAK,EAAEC,KAAK,KAAI;MAC/D,MAAMC,WAAW,GAAG,IAAID,KAAK,GAAG;MAChC,MAAM5B,KAAK,GAAG2B,KAAK,CAACG,iBAAiB,GAC/BjC,kBAAkB,CAACkC,gBAAgB,EAAEC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,QAAQ,KAAKP,KAAK,CAACQ,OAAO,CAAC,EAAEC,WAAW,GACzFvC,kBAAkB,CAACwC,IAAI,CAACV,KAAK,CAACQ,OAAO,CAAC;MAC5C,IAAI,CAAChC,SAAS,GAAG,IAAI,CAACA,SAAS,CAACmC,OAAO,CAACT,WAAW,EAAE,GAAG7B,KAAK,IAAI,EAAE,EAAE,CAAC;IAC1E,CAAC,CAAC;EACN;;;uBA9CSO,gCAAgC,EAAAlB,EAAA,CAAAkD,iBAAA,CAAAC,EAAA,CAAAC,oBAAA,GAAApD,EAAA,CAAAkD,iBAAA,CAAAG,EAAA,CAAAC,mBAAA,GAAAtD,EAAA,CAAAkD,iBAAA,CAAAK,EAAA,CAAAC,aAAA;IAAA;EAAA;;;YAAhCtC,gCAAgC;MAAAuC,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAA3D,EAAA,CAAA4D,0BAAA,EAAA5D,EAAA,CAAA6D,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,0CAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCX7CnE,EALA,CAAAqE,UAAA,IAAAC,uDAAA,cAAsD,IAAAC,uDAAA,eAKpC;;;UALlBvE,EAAA,CAAAwE,aAAA,KAAAJ,GAAA,CAAAK,UAAA,KAAAL,GAAA,CAAA5D,kBAAA,kBAAA4D,GAAA,CAAA5D,kBAAA,CAAAyB,IAAA,kBAAAmC,GAAA,CAAA5D,kBAAA,CAAAyB,IAAA,CAAAC,KAAA,WAIC;UACDlC,EAAA,CAAAI,SAAA,EAQC;UARDJ,EAAA,CAAAwE,aAAA,IAAAJ,GAAA,CAAAK,UAAA,UAQC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}