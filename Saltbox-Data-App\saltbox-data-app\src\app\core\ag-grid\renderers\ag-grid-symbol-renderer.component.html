@if (!showEditor && cellRendererParams?.node?.group) {
  <div>
  {{ cellRendererParams?.valueFormatted ?? cellRendererParams?.value }}
  </div>
}
@if (showEditor) {
  <div [ngClass]="{'hide-dropdown-arrow' : !cellRendererParams.allowUserUpdate}">
    <p-dropdown #targetElement [options]="symbolArray" appendTo="body" [(ngModel)]="icon"
      [disabled]="!cellRendererParams.allowUserUpdate" [required]="required" (onChange)=onChangeData()>
      <ng-template let-item pTemplate="selectedItem"> <i class="{{item.label}}"></i> </ng-template>
      <ng-template let-object pTemplate="item"> <i class="{{object.label}}"></i> </ng-template> </p-dropdown>
  </div>
}