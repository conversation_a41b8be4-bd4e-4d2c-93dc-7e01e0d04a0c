@if (!showEditor && cellRendererParams?.node?.group) {
  <div>
  {{ cellRendererParams?.valueFormatted ?? cellRendererParams?.value }}
  </div>
}
@if (showEditor) {
    <div class="actionable-grid-config-dropdown">
        <p-dropdown #targetElement panelStyleClass="actionable-grid-config-dropdown-panel" styleClass="alphine-p-highlight"
            [options]="dropdownArray" [(ngModel)]="cellRendererParams.value" appendTo="body" placeholder=" "
            optionLabel="value" optionValue="value" [required]="required" (onChange)=onChangeData()>
        </p-dropdown>
    </div>
}