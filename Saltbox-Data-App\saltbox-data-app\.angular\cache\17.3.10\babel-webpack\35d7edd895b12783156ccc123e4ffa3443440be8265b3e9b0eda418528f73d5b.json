{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { ValidationLevels, ValidationRuleTypes } from '../core/enums/business-validations.enum';\nimport { DB_PRIMARY_KEY } from '../shared/constants/record.const';\nimport { getEnumKeyByValue } from '../shared/utilities/helper.functions';\nimport { JsonDataTypes } from '../shared/enums/json-schema.enum';\nimport { AgGridIconRendererComponent } from '../core/ag-grid/renderers/ag-grid-icon.renderer.component';\nimport { ButtonModule } from 'primeng/button';\nimport { AgGridModule } from 'ag-grid-angular';\nimport { NgClass, NgStyle, NgIf } from '@angular/common';\nimport { SharedModule } from 'primeng/api';\nimport { SidebarModule } from 'primeng/sidebar';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"primeng/sidebar\";\nimport * as i2 from \"primeng/api\";\nimport * as i3 from \"ag-grid-angular\";\nimport * as i4 from \"primeng/button\";\nconst _c0 = a0 => ({\n  \"color\": a0\n});\nfunction ValidationResultsDialogComponent_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 7);\n    i0.ɵɵelement(1, \"i\", 8);\n    i0.ɵɵelementStart(2, \"h5\", 9);\n    i0.ɵɵtext(3, \" Issue Log \");\n    i0.ɵɵelementStart(4, \"span\", 10);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", ctx_r0.saveRecordsResponse.hasFailures ? \"pi-exclamation-circle\" : \"pi-exclamation-triangle\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(3, _c0, ctx_r0.saveRecordsResponse.hasFailures ? \"#de3e35\" : \"#AE5900\"));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.saveRecordsResponse.hasFailures ? \"Save Failed With Validation Errors\" : \"Save Completed with Validation Warnings\", \" \");\n  }\n}\nfunction ValidationResultsDialogComponent_div_4_p_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"The update could not be completed due to (\", ctx_r0.failureErrorCount, \") record(s) that failed during save. The following exceptions business rule validations were found:\");\n  }\n}\nfunction ValidationResultsDialogComponent_div_4_p_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\");\n    i0.ɵɵtext(1, \"The update could not be completed due to the following exceptions business rule validations:\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ValidationResultsDialogComponent_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 11);\n    i0.ɵɵtemplate(1, ValidationResultsDialogComponent_div_4_p_1_Template, 2, 1, \"p\", 12)(2, ValidationResultsDialogComponent_div_4_p_2_Template, 2, 0, \"p\", 12);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.singleRecordMode);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.singleRecordMode);\n  }\n}\nfunction ValidationResultsDialogComponent_div_5_p_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"The update was completed successfully. However there were (\", ctx_r0.saveRecordsResponse == null ? null : ctx_r0.saveRecordsResponse.failedValidations == null ? null : ctx_r0.saveRecordsResponse.failedValidations.length, \") record(s) that had at least one validation warning. The following exceptions business rule validations were found:\");\n  }\n}\nfunction ValidationResultsDialogComponent_div_5_p_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\");\n    i0.ɵɵtext(1, \"The update was completed successfully. However there were some business rule validation warnings. The following issues were found:\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ValidationResultsDialogComponent_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 11);\n    i0.ɵɵtemplate(1, ValidationResultsDialogComponent_div_5_p_1_Template, 2, 1, \"p\", 12)(2, ValidationResultsDialogComponent_div_5_p_2_Template, 2, 0, \"p\", 12);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.singleRecordMode);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.singleRecordMode);\n  }\n}\nfunction ValidationResultsDialogComponent_ng_template_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 13)(1, \"p-button\", 14);\n    i0.ɵɵlistener(\"onClick\", function ValidationResultsDialogComponent_ng_template_8_Template_p_button_onClick_1_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.closeDialog(false));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"outlined\", true);\n  }\n}\nexport class ValidationResultsDialogComponent {\n  constructor() {\n    this.visible = false;\n    this.singleRecordMode = false;\n    this.hideDialog = new EventEmitter();\n    this.visibleChange = new EventEmitter();\n    this.validationResult = [];\n    this.gridOptions = {\n      onGridReady: this.onGridReady.bind(this),\n      defaultColDef: {\n        resizable: true,\n        sortable: true,\n        suppressHeaderMenuButton: true\n      }\n    };\n    this.getRowStyle = params => {\n      if (params?.data?.level === ValidationLevels.Failure) {\n        return {\n          backgroundColor: '#fcebea'\n        };\n      }\n    };\n  }\n  ngOnInit() {\n    this.columnDefs = [{\n      field: 'recordId',\n      headerName: 'Record Id',\n      hide: true\n    }, {\n      field: 'masterPK',\n      headerName: 'Master PK',\n      rowGroup: !this.singleRecordMode,\n      hide: true\n    }, {\n      field: 'keyColValue',\n      headerName: 'Key',\n      hide: !this.singleRecordMode\n    }, {\n      field: 'isValid',\n      headerName: 'Is Valid',\n      hide: true\n    }, {\n      field: 'hasFailures',\n      headerName: 'Has Failures',\n      hide: true\n    }, {\n      field: 'hasWarnings',\n      headerName: 'Has Warnings',\n      hide: true\n    },\n    // Fields from ScriptRuleResult\n    {\n      field: 'ruleId',\n      headerName: 'Rule ID',\n      hide: true\n    }, {\n      field: 'ruleName',\n      headerName: 'Rule Name'\n    }, {\n      field: 'level',\n      headerName: 'Severity'\n    }, {\n      field: 'message',\n      headerName: 'Message'\n    }, {\n      field: 'fieldLevel',\n      headerName: 'Level'\n    }, {\n      field: 'fieldName',\n      headerName: 'Field Name'\n    }, {\n      field: 'fieldJsonPath',\n      headerName: 'Field JSON Path',\n      hide: true\n    }, {\n      field: 'log',\n      headerName: 'Log',\n      cellRenderer: AgGridIconRendererComponent,\n      cellRendererParams: params => {\n        return {\n          iconClass: params.value ? 'pi-info-circle' : '',\n          tooltip: params.value\n        };\n      }\n    }, {\n      field: 'scriptFailed',\n      headerName: 'Script Failed'\n    }, {\n      field: 'errorMessage',\n      headerName: 'Script Error Message'\n    }];\n    // if there is no key column defined we skip the first column as it is _id (datastore PK) and take the second column as the primary key\n    if (!this.masterKeyColumn) this.masterKeyColumn = this.data?.length ? this.getSecondPropertyOfObject(this.data[0])?.column : undefined;\n    const pkDisplayName = this.datastore?.baseSchema?.properties[this.masterKeyColumn]?.presentationProperties?.displayName;\n    this.masterKeyColumnName = pkDisplayName != null && pkDisplayName?.trim() != '' ? pkDisplayName : this.masterKeyColumn;\n    if (!this.singleRecordMode) {\n      this.gridOptions.autoGroupColumnDef = {\n        headerName: this.masterKeyColumnName,\n        field: 'keyColValue'\n      };\n    }\n    this.validationResult = this.getMergedValidationResults(this.saveRecordsResponse.failedValidations);\n  }\n  onGridReady(params) {\n    this.gridApi = params.api;\n    // Expand all rows if rowData is already set\n    this.gridApi?.expandAll();\n    this.gridApi?.autoSizeAllColumns();\n  }\n  getMergedValidationResults(results) {\n    return results.reduce((acc, result) => {\n      // transform each validation failure and concatenate the result\n      const failures = result.validationFailures.map(failure => ({\n        ...result,\n        ...failure,\n        fieldName: this.getFieldDisplayNameByRecordPath(failure.fieldJsonPath, failure.fieldName),\n        masterPK: this.getMasterKeyColValue(result.recordId),\n        keyColValue: failure.ruleType === ValidationRuleTypes.Field && failure.fieldJsonPath ? this.getNestedObjectKeys(failure.fieldJsonPath, result.recordId, failure.rowId) : this.getMasterKeyColValue(result.recordId, true),\n        fieldLevel: getEnumKeyByValue(ValidationRuleTypes, failure.ruleType)\n      }));\n      return acc.concat(failures);\n    }, []);\n  }\n  getFieldDisplayNameByRecordPath(fieldJsonPath, fieldName) {\n    if (!fieldName) return undefined;\n    // Split the path into parts (field names and indexes)\n    const parts = fieldJsonPath.split(/\\.|\\[|\\]/).filter(Boolean);\n    let currentProperty = this.datastore?.baseSchema;\n    for (let i = 0; i < parts.length; i = i + 2) {\n      // Even parts are field names\n      if (currentProperty?.type === JsonDataTypes.Object) currentProperty = currentProperty.properties[parts[i]];else if (currentProperty?.type === JsonDataTypes.Collection) currentProperty = currentProperty.items.properties[parts[i]];\n    }\n    const displayName = currentProperty?.type === JsonDataTypes.Collection ? currentProperty?.items?.properties[fieldName]?.presentationProperties?.displayName : currentProperty?.properties[fieldName]?.presentationProperties?.displayName;\n    return displayName && displayName.trim() !== '' ? displayName : fieldName;\n  }\n  getMasterKeyColValue(recordId, withColumnName = false) {\n    const record = this.singleRecordMode ? this.data[0] : this.data?.find(x => x[DB_PRIMARY_KEY] === recordId);\n    if (!record) return undefined;\n    const masterKeyColumnValue = record[this.masterKeyColumn];\n    return withColumnName ? `${this.masterKeyColumnName}: ${masterKeyColumnValue}` : masterKeyColumnValue;\n  }\n  getNestedObjectKeys(path, recordId, rowId) {\n    let record = this.data.find(x => x[DB_PRIMARY_KEY] === recordId);\n    if (!record) return undefined;\n    // Split the path into parts (field names and indexes)\n    const parts = path.split(/\\.|\\[|\\]/).filter(Boolean);\n    let currentProperty = this.datastore?.baseSchema;\n    const objectKeys = [];\n    for (let i = 0; i < parts.length; i++) {\n      if (record === undefined) {\n        return undefined;\n      }\n      if (i % 2 === 0) {\n        // Even parts are field names\n        record = record[parts[i]];\n        if (currentProperty?.type === JsonDataTypes.Object) currentProperty = currentProperty.properties[parts[i]];else if (currentProperty?.type === JsonDataTypes.Collection) currentProperty = currentProperty.items.properties[parts[i]];\n      } else {\n        // Odd parts are array indexes\n        const index = Number(parts[i]);\n        record = Array.isArray(record) ? record[index] : undefined;\n        const secondCol = this.getSecondPropertyOfObject(record);\n        const displayName = currentProperty?.type === JsonDataTypes.Collection ? currentProperty?.items?.properties[secondCol.column]?.presentationProperties?.displayName : currentProperty?.properties[secondCol.column]?.presentationProperties?.displayName;\n        objectKeys.push(`${displayName ?? secondCol?.column}: ${secondCol.value}`);\n      }\n    }\n    if (!record || record[DB_PRIMARY_KEY] !== rowId) {\n      return undefined;\n    }\n    return objectKeys.join(', ');\n  }\n  getSecondPropertyOfObject(obj) {\n    if (!obj) return undefined;\n    const columns = Object.keys(obj);\n    if (columns?.length <= 1) return undefined;\n    return {\n      column: columns[1],\n      value: obj[columns[1]]\n    };\n  }\n  closeDialog(byXButton) {\n    this.visible = false;\n    this.visibleChange.emit(false);\n    if (byXButton) this.hideDialog.emit();\n  }\n  get failureErrorCount() {\n    return this.saveRecordsResponse?.failedValidations?.filter(x => x.hasFailures)?.length;\n  }\n  static {\n    this.ɵfac = function ValidationResultsDialogComponent_Factory(t) {\n      return new (t || ValidationResultsDialogComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ValidationResultsDialogComponent,\n      selectors: [[\"app-validation-results-dialog\"]],\n      inputs: {\n        visible: \"visible\",\n        saveRecordsResponse: \"saveRecordsResponse\",\n        data: \"data\",\n        datastore: \"datastore\",\n        singleRecordMode: \"singleRecordMode\",\n        masterKeyColumn: \"masterKeyColumn\"\n      },\n      outputs: {\n        hideDialog: \"hideDialog\",\n        visibleChange: \"visibleChange\"\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 9,\n      vars: 9,\n      consts: [[\"position\", \"right\", \"styleClass\", \"sidebar-md business-validation-sidebar\", \"appendTo\", \"body\", 3, \"visibleChange\", \"onHide\", \"visible\"], [\"pTemplate\", \"header\"], [1, \"grid\"], [1, \"col-12\"], [\"class\", \"ml-2 section-description\", 4, \"ngIf\"], [1, \"ag-theme-material\", \"w-full\", \"h-30rem\", 3, \"animateRows\", \"rowHeight\", \"rowData\", \"columnDefs\", \"getRowStyle\", \"gridOptions\"], [\"pTemplate\", \"footer\"], [1, \"flex\", \"w-full\", \"justify-content-start\", \"align-items-center\"], [1, \"pi\", \"pr-2\", \"text-4xl\", 3, \"ngClass\"], [1, \"font-normal\", \"text-sm\"], [1, \"block\", 3, \"ngStyle\"], [1, \"ml-2\", \"section-description\"], [4, \"ngIf\"], [1, \"flex\", \"justify-content-end\", \"w-full\"], [\"id\", \"ok\", \"name\", \"ok\", \"label\", \"Close\", 3, \"onClick\", \"outlined\"]],\n      template: function ValidationResultsDialogComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"p-sidebar\", 0);\n          i0.ɵɵtwoWayListener(\"visibleChange\", function ValidationResultsDialogComponent_Template_p_sidebar_visibleChange_0_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.visible, $event) || (ctx.visible = $event);\n            return $event;\n          });\n          i0.ɵɵlistener(\"onHide\", function ValidationResultsDialogComponent_Template_p_sidebar_onHide_0_listener() {\n            return ctx.closeDialog(true);\n          });\n          i0.ɵɵtemplate(1, ValidationResultsDialogComponent_ng_template_1_Template, 6, 5, \"ng-template\", 1);\n          i0.ɵɵelementStart(2, \"div\", 2)(3, \"div\", 3);\n          i0.ɵɵtemplate(4, ValidationResultsDialogComponent_div_4_Template, 3, 2, \"div\", 4)(5, ValidationResultsDialogComponent_div_5_Template, 3, 2, \"div\", 4);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"div\", 3);\n          i0.ɵɵelement(7, \"ag-grid-angular\", 5);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(8, ValidationResultsDialogComponent_ng_template_8_Template, 2, 1, \"ng-template\", 6);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵtwoWayProperty(\"visible\", ctx.visible);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.saveRecordsResponse.hasFailures);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.saveRecordsResponse.hasFailures);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"animateRows\", true)(\"rowHeight\", 40)(\"rowData\", ctx.validationResult)(\"columnDefs\", ctx.columnDefs)(\"getRowStyle\", ctx.getRowStyle)(\"gridOptions\", ctx.gridOptions);\n        }\n      },\n      dependencies: [SidebarModule, i1.Sidebar, i2.PrimeTemplate, SharedModule, NgClass, NgStyle, NgIf, AgGridModule, i3.AgGridAngular, ButtonModule, i4.Button],\n      encapsulation: 2\n    });\n  }\n}", "map": {"version": 3, "names": ["EventEmitter", "ValidationLevels", "ValidationRuleTypes", "DB_PRIMARY_KEY", "getEnumKeyByValue", "JsonDataTypes", "AgGridIconRendererComponent", "ButtonModule", "AgGridModule", "Ng<PERSON><PERSON>", "NgStyle", "NgIf", "SharedModule", "SidebarModule", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵproperty", "ctx_r0", "saveRecordsResponse", "hasFailures", "ɵɵpureFunction1", "_c0", "ɵɵtextInterpolate1", "failureErrorCount", "ɵɵtemplate", "ValidationResultsDialogComponent_div_4_p_1_Template", "ValidationResultsDialogComponent_div_4_p_2_Template", "singleRecordMode", "failedValidations", "length", "ValidationResultsDialogComponent_div_5_p_1_Template", "ValidationResultsDialogComponent_div_5_p_2_Template", "ɵɵlistener", "ValidationResultsDialogComponent_ng_template_8_Template_p_button_onClick_1_listener", "ɵɵrestoreView", "_r2", "ɵɵnextContext", "ɵɵresetView", "closeDialog", "ValidationResultsDialogComponent", "constructor", "visible", "hideDialog", "visibleChange", "validationResult", "gridOptions", "onGridReady", "bind", "defaultColDef", "resizable", "sortable", "suppressHeaderMenuButton", "getRowStyle", "params", "data", "level", "Failure", "backgroundColor", "ngOnInit", "columnDefs", "field", "headerName", "hide", "rowGroup", "cell<PERSON><PERSON><PERSON>", "cellRendererParams", "iconClass", "value", "tooltip", "masterKeyColumn", "getSecondPropertyOfObject", "column", "undefined", "pkDisplayName", "datastore", "baseSchema", "properties", "presentationProperties", "displayName", "masterKeyColumnName", "trim", "autoGroupColumnDef", "getMergedValidationResults", "gridApi", "api", "expandAll", "autoSizeAllColumns", "results", "reduce", "acc", "result", "failures", "validationFailures", "map", "failure", "fieldName", "getFieldDisplayNameByRecordPath", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "masterPK", "getMasterKeyColValue", "recordId", "keyColValue", "ruleType", "Field", "getNestedObjectKeys", "rowId", "fieldLevel", "concat", "parts", "split", "filter", "Boolean", "currentProperty", "i", "type", "Object", "Collection", "items", "withColumnName", "record", "find", "x", "masterKeyColumnValue", "path", "objectKeys", "index", "Number", "Array", "isArray", "secondCol", "push", "join", "obj", "columns", "keys", "byXButton", "emit", "selectors", "inputs", "outputs", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "ValidationResultsDialogComponent_Template", "rf", "ctx", "ɵɵtwoWayListener", "ValidationResultsDialogComponent_Template_p_sidebar_visibleChange_0_listener", "$event", "ɵɵtwoWayBindingSet", "ValidationResultsDialogComponent_Template_p_sidebar_onHide_0_listener", "ValidationResultsDialogComponent_ng_template_1_Template", "ValidationResultsDialogComponent_div_4_Template", "ValidationResultsDialogComponent_div_5_Template", "ValidationResultsDialogComponent_ng_template_8_Template", "ɵɵtwoWayProperty", "i1", "Sidebar", "i2", "PrimeTemplate", "i3", "AgGridAngular", "i4", "<PERSON><PERSON>", "encapsulation"], "sources": ["C:\\Projects\\SaltboxActionableGrid\\Saltbox-Data-App\\saltbox-data-app\\src\\app\\business-validation-results\\validation-results-dialog.component.ts", "C:\\Projects\\SaltboxActionableGrid\\Saltbox-Data-App\\saltbox-data-app\\src\\app\\business-validation-results\\validation-results-dialog.component.html"], "sourcesContent": ["import { Component, Input, EventEmitter, Output, OnInit } from '@angular/core';\r\nimport { ColDef, GridApi, GridOptions } from 'ag-grid-community';\r\nimport { SaveRecordsResponse } from '../core/models/save-records-response';\r\nimport { ValidationLevels, ValidationRuleTypes } from '../core/enums/business-validations.enum';\r\nimport { ScriptValidationResult } from '../core/models/script-validation-result';\r\nimport { DB_PRIMARY_KEY } from '../shared/constants/record.const';\r\nimport { getEnumKeyByValue } from '../shared/utilities/helper.functions';\r\nimport { Datastore } from '../shared/models/datastore';\r\nimport { ExtendedJsonSchema } from '../shared/models/extended-json-schema';\r\nimport { JsonDataTypes } from '../shared/enums/json-schema.enum';\r\nimport { AgGridIconRendererComponent } from '../core/ag-grid/renderers/ag-grid-icon.renderer.component';\r\nimport { MergedValidationResult } from '../core/models/merged-validation-result';\r\nimport { ButtonModule } from 'primeng/button';\r\nimport { AgGridModule } from 'ag-grid-angular';\r\nimport { NgClass, NgStyle, NgIf } from '@angular/common';\r\nimport { SharedModule } from 'primeng/api';\r\nimport { SidebarModule } from 'primeng/sidebar';\r\n\r\n@Component({\r\n    selector: 'app-validation-results-dialog',\r\n    templateUrl: './validation-results-dialog.component.html',\r\n    standalone: true,\r\n    imports: [SidebarModule, SharedModule, NgClass, NgStyle, NgIf, AgGridModule, ButtonModule]\r\n})\r\nexport class ValidationResultsDialogComponent implements OnInit {\r\n    @Input() visible = false;\r\n    @Input() saveRecordsResponse: SaveRecordsResponse;\r\n    @Input() data: any[];\r\n    @Input() datastore: Datastore;\r\n    @Input() singleRecordMode = false;\r\n    @Input() masterKeyColumn: string;\r\n\r\n    @Output() hideDialog = new EventEmitter<void>();\r\n    @Output() visibleChange = new EventEmitter<boolean>();\r\n\r\n    validationResult: MergedValidationResult[] = [];\r\n    protected masterKeyColumnName: string;\r\n    private gridApi: GridApi;\r\n\r\n    columnDefs: ColDef[];\r\n\r\n    gridOptions: GridOptions = {\r\n        onGridReady: this.onGridReady.bind(this),\r\n        defaultColDef: {\r\n            resizable: true,\r\n            sortable: true,\r\n            suppressHeaderMenuButton: true,\r\n        }\r\n    };\r\n\r\n    ngOnInit(): void {\r\n        this.columnDefs = [\r\n            { field: 'recordId', headerName: 'Record Id', hide: true },\r\n            { field: 'masterPK', headerName: 'Master PK', rowGroup: !this.singleRecordMode, hide: true },\r\n            { field: 'keyColValue', headerName: 'Key', hide: !this.singleRecordMode },\r\n            { field: 'isValid', headerName: 'Is Valid', hide: true },\r\n            { field: 'hasFailures', headerName: 'Has Failures', hide: true },\r\n            { field: 'hasWarnings', headerName: 'Has Warnings', hide: true },\r\n            // Fields from ScriptRuleResult\r\n            { field: 'ruleId', headerName: 'Rule ID', hide: true },\r\n            { field: 'ruleName', headerName: 'Rule Name' },\r\n            { field: 'level', headerName: 'Severity' },\r\n            { field: 'message', headerName: 'Message' },\r\n            { field: 'fieldLevel', headerName: 'Level' },\r\n            { field: 'fieldName', headerName: 'Field Name' },\r\n            { field: 'fieldJsonPath', headerName: 'Field JSON Path', hide: true },\r\n            {\r\n                field: 'log', headerName: 'Log',\r\n                cellRenderer: AgGridIconRendererComponent,\r\n                cellRendererParams: (params) => {\r\n                    return {\r\n                        iconClass: params.value ? 'pi-info-circle' : '',\r\n                        tooltip: params.value\r\n                    };\r\n                }\r\n            },\r\n            { field: 'scriptFailed', headerName: 'Script Failed' },\r\n            { field: 'errorMessage', headerName: 'Script Error Message' },\r\n        ];\r\n\r\n        // if there is no key column defined we skip the first column as it is _id (datastore PK) and take the second column as the primary key\r\n        if (!this.masterKeyColumn)\r\n            this.masterKeyColumn = this.data?.length ? this.getSecondPropertyOfObject(this.data[0])?.column : undefined;\r\n\r\n        const pkDisplayName = this.datastore?.baseSchema?.properties[this.masterKeyColumn]?.presentationProperties?.displayName;\r\n        this.masterKeyColumnName = pkDisplayName != null && pkDisplayName?.trim() != '' ? pkDisplayName : this.masterKeyColumn;\r\n\r\n        if (!this.singleRecordMode) {\r\n            this.gridOptions.autoGroupColumnDef = {\r\n                headerName: this.masterKeyColumnName,\r\n                field: 'keyColValue'\r\n            };\r\n        }\r\n\r\n        this.validationResult = this.getMergedValidationResults(this.saveRecordsResponse.failedValidations);\r\n    }\r\n\r\n    onGridReady(params) {\r\n        this.gridApi = params.api;\r\n\r\n        // Expand all rows if rowData is already set\r\n        this.gridApi?.expandAll();\r\n        this.gridApi?.autoSizeAllColumns();\r\n    }\r\n\r\n    getMergedValidationResults(results: ScriptValidationResult[]): MergedValidationResult[] {\r\n        return results.reduce((acc, result) => {\r\n            // transform each validation failure and concatenate the result\r\n            const failures = result.validationFailures.map(failure => ({\r\n                ...result,\r\n                ...failure,\r\n                fieldName: this.getFieldDisplayNameByRecordPath(failure.fieldJsonPath, failure.fieldName),\r\n                masterPK: this.getMasterKeyColValue(result.recordId),\r\n                keyColValue: failure.ruleType === ValidationRuleTypes.Field && failure.fieldJsonPath\r\n                    ? this.getNestedObjectKeys(failure.fieldJsonPath, result.recordId, failure.rowId)\r\n                    : this.getMasterKeyColValue(result.recordId, true),\r\n                fieldLevel: getEnumKeyByValue(ValidationRuleTypes, failure.ruleType),\r\n            }));\r\n            return acc.concat(failures);\r\n        }, []);\r\n    }\r\n\r\n    getFieldDisplayNameByRecordPath(fieldJsonPath: string, fieldName): any {\r\n        if (!fieldName)\r\n            return undefined;\r\n\r\n        // Split the path into parts (field names and indexes)\r\n        const parts = fieldJsonPath.split(/\\.|\\[|\\]/).filter(Boolean);\r\n        let currentProperty = this.datastore?.baseSchema;\r\n\r\n        for (let i = 0; i < parts.length; i = i + 2) {\r\n            // Even parts are field names\r\n            if (currentProperty?.type === JsonDataTypes.Object)\r\n                currentProperty = currentProperty.properties[parts[i]];\r\n            else if (currentProperty?.type === JsonDataTypes.Collection)\r\n                currentProperty = currentProperty.items.properties[parts[i]];\r\n        }\r\n\r\n        const displayName = currentProperty?.type === JsonDataTypes.Collection\r\n            ? currentProperty?.items?.properties[fieldName]?.presentationProperties?.displayName\r\n            : currentProperty?.properties[fieldName]?.presentationProperties?.displayName;\r\n\r\n        return displayName && displayName.trim() !== '' ? displayName : fieldName;\r\n    }\r\n\r\n    getMasterKeyColValue(recordId: string, withColumnName = false): any {\r\n        const record = this.singleRecordMode ? this.data[0] : this.data?.find(x => x[DB_PRIMARY_KEY] === recordId);\r\n        if (!record)\r\n            return undefined;\r\n\r\n        const masterKeyColumnValue = record[this.masterKeyColumn];\r\n        return withColumnName ? `${this.masterKeyColumnName}: ${masterKeyColumnValue}` : masterKeyColumnValue;\r\n    }\r\n\r\n    getNestedObjectKeys(path: string, recordId, rowId): string {\r\n        let record = this.data.find(x => x[DB_PRIMARY_KEY] === recordId);\r\n        if (!record)\r\n            return undefined;\r\n\r\n        // Split the path into parts (field names and indexes)\r\n        const parts = path.split(/\\.|\\[|\\]/).filter(Boolean);\r\n\r\n        let currentProperty: ExtendedJsonSchema = this.datastore?.baseSchema;\r\n        const objectKeys: string[] = [];\r\n\r\n        for (let i = 0; i < parts.length; i++) {\r\n            if (record === undefined) {\r\n                return undefined;\r\n            }\r\n\r\n            if (i % 2 === 0) {\r\n                // Even parts are field names\r\n                record = record[parts[i]];\r\n                if (currentProperty?.type === JsonDataTypes.Object)\r\n                    currentProperty = currentProperty.properties[parts[i]];\r\n                else if (currentProperty?.type === JsonDataTypes.Collection)\r\n                    currentProperty = currentProperty.items.properties[parts[i]];\r\n            } else {\r\n                // Odd parts are array indexes\r\n                const index = Number(parts[i]);\r\n                record = Array.isArray(record) ? record[index] : undefined;\r\n\r\n                const secondCol = this.getSecondPropertyOfObject(record);\r\n\r\n                const displayName = currentProperty?.type === JsonDataTypes.Collection\r\n                    ? currentProperty?.items?.properties[secondCol.column]?.presentationProperties?.displayName\r\n                    : currentProperty?.properties[secondCol.column]?.presentationProperties?.displayName;\r\n\r\n                objectKeys.push(`${displayName ?? secondCol?.column}: ${secondCol.value}`);\r\n            }\r\n        }\r\n\r\n        if (!record || record[DB_PRIMARY_KEY] !== rowId) {\r\n            return undefined;\r\n        }\r\n\r\n        return objectKeys.join(', ');\r\n    }\r\n\r\n    getSecondPropertyOfObject(obj): { column: string, value: any } {\r\n        if (!obj)\r\n            return undefined;\r\n\r\n        const columns = Object.keys(obj);\r\n        if (columns?.length <= 1)\r\n            return undefined;\r\n\r\n        return { column: columns[1], value: obj[columns[1]] };\r\n    }\r\n\r\n    getRowStyle = (params) => {\r\n        if (params?.data?.level === ValidationLevels.Failure) {\r\n            return { backgroundColor: '#fcebea' };\r\n        }\r\n    }\r\n\r\n    closeDialog(byXButton: boolean) {\r\n        this.visible = false;\r\n        this.visibleChange.emit(false);\r\n\r\n        if (byXButton)\r\n            this.hideDialog.emit();\r\n    }\r\n\r\n    get failureErrorCount() {\r\n        return this.saveRecordsResponse?.failedValidations?.filter(x => x.hasFailures)?.length;\r\n    }\r\n}\r\n", "<p-sidebar [(visible)]=\"visible\" position=\"right\" styleClass=\"sidebar-md business-validation-sidebar\"\r\n    (onHide)=\"closeDialog(true)\" appendTo=\"body\">\r\n    <ng-template pTemplate=\"header\">\r\n        <div class=\"flex w-full justify-content-start align-items-center\">\r\n            <i class=\"pi pr-2 text-4xl\"\r\n                [ngClass]=\"saveRecordsResponse.hasFailures ? 'pi-exclamation-circle' : 'pi-exclamation-triangle'\"></i>\r\n            <h5 class=\"font-normal text-sm\">\r\n                Issue Log\r\n                <span class=\"block\" [ngStyle]=\"{'color': saveRecordsResponse.hasFailures ? '#de3e35' : '#AE5900'}\">\r\n                    {{saveRecordsResponse.hasFailures ? 'Save Failed With Validation Errors' : 'Save Completed with\r\n                    Validation Warnings'}}\r\n                </span>\r\n            </h5>\r\n        </div>\r\n    </ng-template>\r\n    <div class=\"grid\">\r\n        <div class=\"col-12\">\r\n            <div *ngIf=\"saveRecordsResponse.hasFailures\" class=\"ml-2 section-description\">\r\n                <p *ngIf=\"!singleRecordMode\">The update could not be completed due to ({{failureErrorCount}}) record(s)\r\n                    that failed during save. The following exceptions business rule validations were found:</p>\r\n                <p *ngIf=\"singleRecordMode\">The update could not be completed due to the following exceptions business\r\n                    rule validations:</p>\r\n            </div>\r\n            <div *ngIf=\"!saveRecordsResponse.hasFailures\" class=\"ml-2 section-description\">\r\n                <p *ngIf=\"!singleRecordMode\">The update was completed successfully. However there were\r\n                    ({{saveRecordsResponse?.failedValidations?.length}}) record(s) that had at least one validation\r\n                    warning. The following exceptions business rule validations were found:</p>\r\n                <p *ngIf=\"singleRecordMode\">The update was completed successfully. However there were\r\n                    some business rule validation warnings. The following issues were found:</p>\r\n            </div>\r\n        </div>\r\n        <div class=\"col-12\">\r\n            <ag-grid-angular class=\"ag-theme-material w-full h-30rem\" [animateRows]=\"true\" [rowHeight]=\"40\"\r\n                [rowData]=\"validationResult\" [columnDefs]=\"columnDefs\" [getRowStyle]=\"getRowStyle\"\r\n                [gridOptions]=\"gridOptions\">\r\n            </ag-grid-angular>\r\n        </div>\r\n    </div>\r\n    <ng-template pTemplate=\"footer\">\r\n        <div class=\"flex justify-content-end w-full\">\r\n            <p-button id=\"ok\" name=\"ok\" label=\"Close\" [outlined]=\"true\" \r\n                (onClick)=\"closeDialog(false)\"></p-button>\r\n        </div>\r\n    </ng-template>\r\n</p-sidebar>"], "mappings": "AAAA,SAA2BA,YAAY,QAAwB,eAAe;AAG9E,SAASC,gBAAgB,EAAEC,mBAAmB,QAAQ,yCAAyC;AAE/F,SAASC,cAAc,QAAQ,kCAAkC;AACjE,SAASC,iBAAiB,QAAQ,sCAAsC;AAGxE,SAASC,aAAa,QAAQ,kCAAkC;AAChE,SAASC,2BAA2B,QAAQ,2DAA2D;AAEvG,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,OAAO,EAAEC,OAAO,EAAEC,IAAI,QAAQ,iBAAiB;AACxD,SAASC,YAAY,QAAQ,aAAa;AAC1C,SAASC,aAAa,QAAQ,iBAAiB;;;;;;;;;;;ICbvCC,EAAA,CAAAC,cAAA,aAAkE;IAC9DD,EAAA,CAAAE,SAAA,WAC0G;IAC1GF,EAAA,CAAAC,cAAA,YAAgC;IAC5BD,EAAA,CAAAG,MAAA,kBACA;IAAAH,EAAA,CAAAC,cAAA,eAAmG;IAC/FD,EAAA,CAAAG,MAAA,GAEJ;IAERH,EAFQ,CAAAI,YAAA,EAAO,EACN,EACH;;;;IAREJ,EAAA,CAAAK,SAAA,EAAiG;IAAjGL,EAAA,CAAAM,UAAA,YAAAC,MAAA,CAAAC,mBAAA,CAAAC,WAAA,uDAAiG;IAG7ET,EAAA,CAAAK,SAAA,GAA8E;IAA9EL,EAAA,CAAAM,UAAA,YAAAN,EAAA,CAAAU,eAAA,IAAAC,GAAA,EAAAJ,MAAA,CAAAC,mBAAA,CAAAC,WAAA,0BAA8E;IAC9FT,EAAA,CAAAK,SAAA,EAEJ;IAFIL,EAAA,CAAAY,kBAAA,MAAAL,MAAA,CAAAC,mBAAA,CAAAC,WAAA,yFAEJ;;;;;IAOAT,EAAA,CAAAC,cAAA,QAA6B;IAAAD,EAAA,CAAAG,MAAA,GAC8D;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;IADlEJ,EAAA,CAAAK,SAAA,EAC8D;IAD9DL,EAAA,CAAAY,kBAAA,+CAAAL,MAAA,CAAAM,iBAAA,wGAC8D;;;;;IAC3Fb,EAAA,CAAAC,cAAA,QAA4B;IAAAD,EAAA,CAAAG,MAAA,mGACP;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;;IAJ7BJ,EAAA,CAAAC,cAAA,cAA8E;IAG1ED,EAFA,CAAAc,UAAA,IAAAC,mDAAA,gBAA6B,IAAAC,mDAAA,gBAED;IAEhChB,EAAA,CAAAI,YAAA,EAAM;;;;IAJEJ,EAAA,CAAAK,SAAA,EAAuB;IAAvBL,EAAA,CAAAM,UAAA,UAAAC,MAAA,CAAAU,gBAAA,CAAuB;IAEvBjB,EAAA,CAAAK,SAAA,EAAsB;IAAtBL,EAAA,CAAAM,UAAA,SAAAC,MAAA,CAAAU,gBAAA,CAAsB;;;;;IAI1BjB,EAAA,CAAAC,cAAA,QAA6B;IAAAD,EAAA,CAAAG,MAAA,GAE8C;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;IAFlDJ,EAAA,CAAAK,SAAA,EAE8C;IAF9CL,EAAA,CAAAY,kBAAA,gEAAAL,MAAA,CAAAC,mBAAA,kBAAAD,MAAA,CAAAC,mBAAA,CAAAU,iBAAA,kBAAAX,MAAA,CAAAC,mBAAA,CAAAU,iBAAA,CAAAC,MAAA,yHAE8C;;;;;IAC3EnB,EAAA,CAAAC,cAAA,QAA4B;IAAAD,EAAA,CAAAG,MAAA,yIACgD;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;;IALpFJ,EAAA,CAAAC,cAAA,cAA+E;IAI3ED,EAHA,CAAAc,UAAA,IAAAM,mDAAA,gBAA6B,IAAAC,mDAAA,gBAGD;IAEhCrB,EAAA,CAAAI,YAAA,EAAM;;;;IALEJ,EAAA,CAAAK,SAAA,EAAuB;IAAvBL,EAAA,CAAAM,UAAA,UAAAC,MAAA,CAAAU,gBAAA,CAAuB;IAGvBjB,EAAA,CAAAK,SAAA,EAAsB;IAAtBL,EAAA,CAAAM,UAAA,SAAAC,MAAA,CAAAU,gBAAA,CAAsB;;;;;;IAa9BjB,EADJ,CAAAC,cAAA,cAA6C,mBAEN;IAA/BD,EAAA,CAAAsB,UAAA,qBAAAC,oFAAA;MAAAvB,EAAA,CAAAwB,aAAA,CAAAC,GAAA;MAAA,MAAAlB,MAAA,GAAAP,EAAA,CAAA0B,aAAA;MAAA,OAAA1B,EAAA,CAAA2B,WAAA,CAAWpB,MAAA,CAAAqB,WAAA,CAAY,KAAK,CAAC;IAAA,EAAC;IACtC5B,EADuC,CAAAI,YAAA,EAAW,EAC5C;;;IAFwCJ,EAAA,CAAAK,SAAA,EAAiB;IAAjBL,EAAA,CAAAM,UAAA,kBAAiB;;;ADhBvE,OAAM,MAAOuB,gCAAgC;EAN7CC,YAAA;IAOa,KAAAC,OAAO,GAAG,KAAK;IAIf,KAAAd,gBAAgB,GAAG,KAAK;IAGvB,KAAAe,UAAU,GAAG,IAAI9C,YAAY,EAAQ;IACrC,KAAA+C,aAAa,GAAG,IAAI/C,YAAY,EAAW;IAErD,KAAAgD,gBAAgB,GAA6B,EAAE;IAM/C,KAAAC,WAAW,GAAgB;MACvBC,WAAW,EAAE,IAAI,CAACA,WAAW,CAACC,IAAI,CAAC,IAAI,CAAC;MACxCC,aAAa,EAAE;QACXC,SAAS,EAAE,IAAI;QACfC,QAAQ,EAAE,IAAI;QACdC,wBAAwB,EAAE;;KAEjC;IAkKD,KAAAC,WAAW,GAAIC,MAAM,IAAI;MACrB,IAAIA,MAAM,EAAEC,IAAI,EAAEC,KAAK,KAAK1D,gBAAgB,CAAC2D,OAAO,EAAE;QAClD,OAAO;UAAEC,eAAe,EAAE;QAAS,CAAE;MACzC;IACJ,CAAC;;EApKDC,QAAQA,CAAA;IACJ,IAAI,CAACC,UAAU,GAAG,CACd;MAAEC,KAAK,EAAE,UAAU;MAAEC,UAAU,EAAE,WAAW;MAAEC,IAAI,EAAE;IAAI,CAAE,EAC1D;MAAEF,KAAK,EAAE,UAAU;MAAEC,UAAU,EAAE,WAAW;MAAEE,QAAQ,EAAE,CAAC,IAAI,CAACpC,gBAAgB;MAAEmC,IAAI,EAAE;IAAI,CAAE,EAC5F;MAAEF,KAAK,EAAE,aAAa;MAAEC,UAAU,EAAE,KAAK;MAAEC,IAAI,EAAE,CAAC,IAAI,CAACnC;IAAgB,CAAE,EACzE;MAAEiC,KAAK,EAAE,SAAS;MAAEC,UAAU,EAAE,UAAU;MAAEC,IAAI,EAAE;IAAI,CAAE,EACxD;MAAEF,KAAK,EAAE,aAAa;MAAEC,UAAU,EAAE,cAAc;MAAEC,IAAI,EAAE;IAAI,CAAE,EAChE;MAAEF,KAAK,EAAE,aAAa;MAAEC,UAAU,EAAE,cAAc;MAAEC,IAAI,EAAE;IAAI,CAAE;IAChE;IACA;MAAEF,KAAK,EAAE,QAAQ;MAAEC,UAAU,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAI,CAAE,EACtD;MAAEF,KAAK,EAAE,UAAU;MAAEC,UAAU,EAAE;IAAW,CAAE,EAC9C;MAAED,KAAK,EAAE,OAAO;MAAEC,UAAU,EAAE;IAAU,CAAE,EAC1C;MAAED,KAAK,EAAE,SAAS;MAAEC,UAAU,EAAE;IAAS,CAAE,EAC3C;MAAED,KAAK,EAAE,YAAY;MAAEC,UAAU,EAAE;IAAO,CAAE,EAC5C;MAAED,KAAK,EAAE,WAAW;MAAEC,UAAU,EAAE;IAAY,CAAE,EAChD;MAAED,KAAK,EAAE,eAAe;MAAEC,UAAU,EAAE,iBAAiB;MAAEC,IAAI,EAAE;IAAI,CAAE,EACrE;MACIF,KAAK,EAAE,KAAK;MAAEC,UAAU,EAAE,KAAK;MAC/BG,YAAY,EAAE9D,2BAA2B;MACzC+D,kBAAkB,EAAGZ,MAAM,IAAI;QAC3B,OAAO;UACHa,SAAS,EAAEb,MAAM,CAACc,KAAK,GAAG,gBAAgB,GAAG,EAAE;UAC/CC,OAAO,EAAEf,MAAM,CAACc;SACnB;MACL;KACH,EACD;MAAEP,KAAK,EAAE,cAAc;MAAEC,UAAU,EAAE;IAAe,CAAE,EACtD;MAAED,KAAK,EAAE,cAAc;MAAEC,UAAU,EAAE;IAAsB,CAAE,CAChE;IAED;IACA,IAAI,CAAC,IAAI,CAACQ,eAAe,EACrB,IAAI,CAACA,eAAe,GAAG,IAAI,CAACf,IAAI,EAAEzB,MAAM,GAAG,IAAI,CAACyC,yBAAyB,CAAC,IAAI,CAAChB,IAAI,CAAC,CAAC,CAAC,CAAC,EAAEiB,MAAM,GAAGC,SAAS;IAE/G,MAAMC,aAAa,GAAG,IAAI,CAACC,SAAS,EAAEC,UAAU,EAAEC,UAAU,CAAC,IAAI,CAACP,eAAe,CAAC,EAAEQ,sBAAsB,EAAEC,WAAW;IACvH,IAAI,CAACC,mBAAmB,GAAGN,aAAa,IAAI,IAAI,IAAIA,aAAa,EAAEO,IAAI,EAAE,IAAI,EAAE,GAAGP,aAAa,GAAG,IAAI,CAACJ,eAAe;IAEtH,IAAI,CAAC,IAAI,CAAC1C,gBAAgB,EAAE;MACxB,IAAI,CAACkB,WAAW,CAACoC,kBAAkB,GAAG;QAClCpB,UAAU,EAAE,IAAI,CAACkB,mBAAmB;QACpCnB,KAAK,EAAE;OACV;IACL;IAEA,IAAI,CAAChB,gBAAgB,GAAG,IAAI,CAACsC,0BAA0B,CAAC,IAAI,CAAChE,mBAAmB,CAACU,iBAAiB,CAAC;EACvG;EAEAkB,WAAWA,CAACO,MAAM;IACd,IAAI,CAAC8B,OAAO,GAAG9B,MAAM,CAAC+B,GAAG;IAEzB;IACA,IAAI,CAACD,OAAO,EAAEE,SAAS,EAAE;IACzB,IAAI,CAACF,OAAO,EAAEG,kBAAkB,EAAE;EACtC;EAEAJ,0BAA0BA,CAACK,OAAiC;IACxD,OAAOA,OAAO,CAACC,MAAM,CAAC,CAACC,GAAG,EAAEC,MAAM,KAAI;MAClC;MACA,MAAMC,QAAQ,GAAGD,MAAM,CAACE,kBAAkB,CAACC,GAAG,CAACC,OAAO,KAAK;QACvD,GAAGJ,MAAM;QACT,GAAGI,OAAO;QACVC,SAAS,EAAE,IAAI,CAACC,+BAA+B,CAACF,OAAO,CAACG,aAAa,EAAEH,OAAO,CAACC,SAAS,CAAC;QACzFG,QAAQ,EAAE,IAAI,CAACC,oBAAoB,CAACT,MAAM,CAACU,QAAQ,CAAC;QACpDC,WAAW,EAAEP,OAAO,CAACQ,QAAQ,KAAKxG,mBAAmB,CAACyG,KAAK,IAAIT,OAAO,CAACG,aAAa,GAC9E,IAAI,CAACO,mBAAmB,CAACV,OAAO,CAACG,aAAa,EAAEP,MAAM,CAACU,QAAQ,EAAEN,OAAO,CAACW,KAAK,CAAC,GAC/E,IAAI,CAACN,oBAAoB,CAACT,MAAM,CAACU,QAAQ,EAAE,IAAI,CAAC;QACtDM,UAAU,EAAE1G,iBAAiB,CAACF,mBAAmB,EAAEgG,OAAO,CAACQ,QAAQ;OACtE,CAAC,CAAC;MACH,OAAOb,GAAG,CAACkB,MAAM,CAAChB,QAAQ,CAAC;IAC/B,CAAC,EAAE,EAAE,CAAC;EACV;EAEAK,+BAA+BA,CAACC,aAAqB,EAAEF,SAAS;IAC5D,IAAI,CAACA,SAAS,EACV,OAAOvB,SAAS;IAEpB;IACA,MAAMoC,KAAK,GAAGX,aAAa,CAACY,KAAK,CAAC,UAAU,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC;IAC7D,IAAIC,eAAe,GAAG,IAAI,CAACtC,SAAS,EAAEC,UAAU;IAEhD,KAAK,IAAIsC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,KAAK,CAAC/E,MAAM,EAAEoF,CAAC,GAAGA,CAAC,GAAG,CAAC,EAAE;MACzC;MACA,IAAID,eAAe,EAAEE,IAAI,KAAKjH,aAAa,CAACkH,MAAM,EAC9CH,eAAe,GAAGA,eAAe,CAACpC,UAAU,CAACgC,KAAK,CAACK,CAAC,CAAC,CAAC,CAAC,KACtD,IAAID,eAAe,EAAEE,IAAI,KAAKjH,aAAa,CAACmH,UAAU,EACvDJ,eAAe,GAAGA,eAAe,CAACK,KAAK,CAACzC,UAAU,CAACgC,KAAK,CAACK,CAAC,CAAC,CAAC;IACpE;IAEA,MAAMnC,WAAW,GAAGkC,eAAe,EAAEE,IAAI,KAAKjH,aAAa,CAACmH,UAAU,GAChEJ,eAAe,EAAEK,KAAK,EAAEzC,UAAU,CAACmB,SAAS,CAAC,EAAElB,sBAAsB,EAAEC,WAAW,GAClFkC,eAAe,EAAEpC,UAAU,CAACmB,SAAS,CAAC,EAAElB,sBAAsB,EAAEC,WAAW;IAEjF,OAAOA,WAAW,IAAIA,WAAW,CAACE,IAAI,EAAE,KAAK,EAAE,GAAGF,WAAW,GAAGiB,SAAS;EAC7E;EAEAI,oBAAoBA,CAACC,QAAgB,EAAEkB,cAAc,GAAG,KAAK;IACzD,MAAMC,MAAM,GAAG,IAAI,CAAC5F,gBAAgB,GAAG,IAAI,CAAC2B,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAACA,IAAI,EAAEkE,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC1H,cAAc,CAAC,KAAKqG,QAAQ,CAAC;IAC1G,IAAI,CAACmB,MAAM,EACP,OAAO/C,SAAS;IAEpB,MAAMkD,oBAAoB,GAAGH,MAAM,CAAC,IAAI,CAAClD,eAAe,CAAC;IACzD,OAAOiD,cAAc,GAAG,GAAG,IAAI,CAACvC,mBAAmB,KAAK2C,oBAAoB,EAAE,GAAGA,oBAAoB;EACzG;EAEAlB,mBAAmBA,CAACmB,IAAY,EAAEvB,QAAQ,EAAEK,KAAK;IAC7C,IAAIc,MAAM,GAAG,IAAI,CAACjE,IAAI,CAACkE,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC1H,cAAc,CAAC,KAAKqG,QAAQ,CAAC;IAChE,IAAI,CAACmB,MAAM,EACP,OAAO/C,SAAS;IAEpB;IACA,MAAMoC,KAAK,GAAGe,IAAI,CAACd,KAAK,CAAC,UAAU,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC;IAEpD,IAAIC,eAAe,GAAuB,IAAI,CAACtC,SAAS,EAAEC,UAAU;IACpE,MAAMiD,UAAU,GAAa,EAAE;IAE/B,KAAK,IAAIX,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,KAAK,CAAC/E,MAAM,EAAEoF,CAAC,EAAE,EAAE;MACnC,IAAIM,MAAM,KAAK/C,SAAS,EAAE;QACtB,OAAOA,SAAS;MACpB;MAEA,IAAIyC,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;QACb;QACAM,MAAM,GAAGA,MAAM,CAACX,KAAK,CAACK,CAAC,CAAC,CAAC;QACzB,IAAID,eAAe,EAAEE,IAAI,KAAKjH,aAAa,CAACkH,MAAM,EAC9CH,eAAe,GAAGA,eAAe,CAACpC,UAAU,CAACgC,KAAK,CAACK,CAAC,CAAC,CAAC,CAAC,KACtD,IAAID,eAAe,EAAEE,IAAI,KAAKjH,aAAa,CAACmH,UAAU,EACvDJ,eAAe,GAAGA,eAAe,CAACK,KAAK,CAACzC,UAAU,CAACgC,KAAK,CAACK,CAAC,CAAC,CAAC;MACpE,CAAC,MAAM;QACH;QACA,MAAMY,KAAK,GAAGC,MAAM,CAAClB,KAAK,CAACK,CAAC,CAAC,CAAC;QAC9BM,MAAM,GAAGQ,KAAK,CAACC,OAAO,CAACT,MAAM,CAAC,GAAGA,MAAM,CAACM,KAAK,CAAC,GAAGrD,SAAS;QAE1D,MAAMyD,SAAS,GAAG,IAAI,CAAC3D,yBAAyB,CAACiD,MAAM,CAAC;QAExD,MAAMzC,WAAW,GAAGkC,eAAe,EAAEE,IAAI,KAAKjH,aAAa,CAACmH,UAAU,GAChEJ,eAAe,EAAEK,KAAK,EAAEzC,UAAU,CAACqD,SAAS,CAAC1D,MAAM,CAAC,EAAEM,sBAAsB,EAAEC,WAAW,GACzFkC,eAAe,EAAEpC,UAAU,CAACqD,SAAS,CAAC1D,MAAM,CAAC,EAAEM,sBAAsB,EAAEC,WAAW;QAExF8C,UAAU,CAACM,IAAI,CAAC,GAAGpD,WAAW,IAAImD,SAAS,EAAE1D,MAAM,KAAK0D,SAAS,CAAC9D,KAAK,EAAE,CAAC;MAC9E;IACJ;IAEA,IAAI,CAACoD,MAAM,IAAIA,MAAM,CAACxH,cAAc,CAAC,KAAK0G,KAAK,EAAE;MAC7C,OAAOjC,SAAS;IACpB;IAEA,OAAOoD,UAAU,CAACO,IAAI,CAAC,IAAI,CAAC;EAChC;EAEA7D,yBAAyBA,CAAC8D,GAAG;IACzB,IAAI,CAACA,GAAG,EACJ,OAAO5D,SAAS;IAEpB,MAAM6D,OAAO,GAAGlB,MAAM,CAACmB,IAAI,CAACF,GAAG,CAAC;IAChC,IAAIC,OAAO,EAAExG,MAAM,IAAI,CAAC,EACpB,OAAO2C,SAAS;IAEpB,OAAO;MAAED,MAAM,EAAE8D,OAAO,CAAC,CAAC,CAAC;MAAElE,KAAK,EAAEiE,GAAG,CAACC,OAAO,CAAC,CAAC,CAAC;IAAC,CAAE;EACzD;EAQA/F,WAAWA,CAACiG,SAAkB;IAC1B,IAAI,CAAC9F,OAAO,GAAG,KAAK;IACpB,IAAI,CAACE,aAAa,CAAC6F,IAAI,CAAC,KAAK,CAAC;IAE9B,IAAID,SAAS,EACT,IAAI,CAAC7F,UAAU,CAAC8F,IAAI,EAAE;EAC9B;EAEA,IAAIjH,iBAAiBA,CAAA;IACjB,OAAO,IAAI,CAACL,mBAAmB,EAAEU,iBAAiB,EAAEkF,MAAM,CAACW,CAAC,IAAIA,CAAC,CAACtG,WAAW,CAAC,EAAEU,MAAM;EAC1F;;;uBA1MSU,gCAAgC;IAAA;EAAA;;;YAAhCA,gCAAgC;MAAAkG,SAAA;MAAAC,MAAA;QAAAjG,OAAA;QAAAvB,mBAAA;QAAAoC,IAAA;QAAAoB,SAAA;QAAA/C,gBAAA;QAAA0C,eAAA;MAAA;MAAAsE,OAAA;QAAAjG,UAAA;QAAAC,aAAA;MAAA;MAAAiG,UAAA;MAAAC,QAAA,GAAAnI,EAAA,CAAAoI,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,0CAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCxB7C1I,EAAA,CAAAC,cAAA,mBACiD;UADtCD,EAAA,CAAA4I,gBAAA,2BAAAC,6EAAAC,MAAA;YAAA9I,EAAA,CAAA+I,kBAAA,CAAAJ,GAAA,CAAA5G,OAAA,EAAA+G,MAAA,MAAAH,GAAA,CAAA5G,OAAA,GAAA+G,MAAA;YAAA,OAAAA,MAAA;UAAA,EAAqB;UAC5B9I,EAAA,CAAAsB,UAAA,oBAAA0H,sEAAA;YAAA,OAAUL,GAAA,CAAA/G,WAAA,CAAY,IAAI,CAAC;UAAA,EAAC;UAC5B5B,EAAA,CAAAc,UAAA,IAAAmI,uDAAA,yBAAgC;UAc5BjJ,EADJ,CAAAC,cAAA,aAAkB,aACM;UAOhBD,EANA,CAAAc,UAAA,IAAAoI,+CAAA,iBAA8E,IAAAC,+CAAA,iBAMC;UAOnFnJ,EAAA,CAAAI,YAAA,EAAM;UACNJ,EAAA,CAAAC,cAAA,aAAoB;UAChBD,EAAA,CAAAE,SAAA,yBAGkB;UAE1BF,EADI,CAAAI,YAAA,EAAM,EACJ;UACNJ,EAAA,CAAAc,UAAA,IAAAsI,uDAAA,yBAAgC;UAMpCpJ,EAAA,CAAAI,YAAA,EAAY;;;UA5CDJ,EAAA,CAAAqJ,gBAAA,YAAAV,GAAA,CAAA5G,OAAA,CAAqB;UAiBd/B,EAAA,CAAAK,SAAA,GAAqC;UAArCL,EAAA,CAAAM,UAAA,SAAAqI,GAAA,CAAAnI,mBAAA,CAAAC,WAAA,CAAqC;UAMrCT,EAAA,CAAAK,SAAA,EAAsC;UAAtCL,EAAA,CAAAM,UAAA,UAAAqI,GAAA,CAAAnI,mBAAA,CAAAC,WAAA,CAAsC;UAScT,EAAA,CAAAK,SAAA,GAAoB;UAE1EL,EAFsD,CAAAM,UAAA,qBAAoB,iBAAiB,YAAAqI,GAAA,CAAAzG,gBAAA,CAC/D,eAAAyG,GAAA,CAAA1F,UAAA,CAA0B,gBAAA0F,GAAA,CAAAjG,WAAA,CAA4B,gBAAAiG,GAAA,CAAAxG,WAAA,CACvD;;;qBDZ7BpC,aAAa,EAAAuJ,EAAA,CAAAC,OAAA,EAAAC,EAAA,CAAAC,aAAA,EAAE3J,YAAY,EAAEH,OAAO,EAAEC,OAAO,EAAEC,IAAI,EAAEH,YAAY,EAAAgK,EAAA,CAAAC,aAAA,EAAElK,YAAY,EAAAmK,EAAA,CAAAC,MAAA;MAAAC,aAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}