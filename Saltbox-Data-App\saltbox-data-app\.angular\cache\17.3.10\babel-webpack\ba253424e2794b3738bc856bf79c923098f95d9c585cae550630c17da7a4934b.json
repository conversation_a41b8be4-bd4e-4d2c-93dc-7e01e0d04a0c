{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { formatCurrency } from 'src/app/shared/utilities/format.functions';\nimport { EditorColumnType } from 'src/app/shared/enums/editor-column-type.enum';\nimport { MenuModule } from 'primeng/menu';\nimport { ButtonModule } from 'primeng/button';\nimport { BadgeModule } from 'primeng/badge';\nimport { NgClass } from '@angular/common';\nimport { TooltipModule } from 'primeng/tooltip';\nimport { ToolbarModule } from 'primeng/toolbar';\nimport { SplitButtonModule } from 'primeng/splitbutton';\nimport { ActionableGridViewModeLabels, ActionableGridViewModes } from 'src/app/core/enums/actionable-grid';\nimport { GridLayoutManagerComponent } from './grid-layout-manager/grid-layout-manager.component';\nimport { ToggleButtonModule } from 'primeng/togglebutton';\nimport { FormsModule } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/core/services/communication.service\";\nimport * as i2 from \"src/app/core/services/user-activity.service\";\nimport * as i3 from \"@angular/forms\";\nimport * as i4 from \"primeng/toolbar\";\nimport * as i5 from \"primeng/tooltip\";\nimport * as i6 from \"primeng/api\";\nimport * as i7 from \"primeng/button\";\nimport * as i8 from \"primeng/menu\";\nimport * as i9 from \"primeng/splitbutton\";\nimport * as i10 from \"primeng/togglebutton\";\nconst _c0 = () => ({\n  width: \"auto\"\n});\nconst _c1 = a0 => ({\n  \"bg-blue-100\": a0\n});\nconst _c2 = a0 => ({\n  \"vertical-align-top\": a0\n});\nconst _c3 = a0 => ({\n  \"font-bold\": a0\n});\nfunction ActionsMenuComponent_Conditional_1_Conditional_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-button\", 29);\n    i0.ɵɵlistener(\"onClick\", function ActionsMenuComponent_Conditional_1_Conditional_7_Template_p_button_onClick_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.onClickShowValidationResults());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"badge\", ctx_r3.validationResultsCount);\n  }\n}\nfunction ActionsMenuComponent_Conditional_1_Conditional_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-toggleButton\", 30);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function ActionsMenuComponent_Conditional_1_Conditional_8_Template_p_toggleButton_ngModelChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r3.columnsOptions, $event) || (ctx_r3.columnsOptions = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"onChange\", function ActionsMenuComponent_Conditional_1_Conditional_8_Template_p_toggleButton_onChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.onColumnsOptionsChange($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r3.columnsOptions);\n    i0.ɵɵproperty(\"onLabel\", \"Columns Options On\")(\"offLabel\", \"Columns Options Off\");\n  }\n}\nfunction ActionsMenuComponent_Conditional_1_Conditional_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-button\", 31);\n    i0.ɵɵlistener(\"onClick\", function ActionsMenuComponent_Conditional_1_Conditional_9_Template_p_button_onClick_0_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.onUpdateUserFavorite());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"icon\", ctx_r3.isFavorite ? \"pi pi-star-fill\" : \"pi pi-star\")(\"pTooltip\", ctx_r3.isFavorite ? \"Remove Favorite\" : \"Add Favorite\");\n  }\n}\nfunction ActionsMenuComponent_Conditional_1_Conditional_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-button\", 32);\n    i0.ɵɵlistener(\"onClick\", function ActionsMenuComponent_Conditional_1_Conditional_11_Template_p_button_onClick_0_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.onClickShowFormEditor(false));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"disabled\", !(ctx_r3.reportInfo == null ? null : ctx_r3.reportInfo.allowAddNewRow) || ctx_r3.disabled);\n  }\n}\nfunction ActionsMenuComponent_Conditional_1_Conditional_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-button\", 33);\n    i0.ɵɵlistener(\"onClick\", function ActionsMenuComponent_Conditional_1_Conditional_12_Template_p_button_onClick_0_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.onClickShowFormEditor(false));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"disabled\", !(ctx_r3.reportInfo == null ? null : ctx_r3.reportInfo.allowAddNewRow) || ctx_r3.disabled);\n  }\n}\nfunction ActionsMenuComponent_Conditional_1_Conditional_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-button\", 34);\n    i0.ɵɵlistener(\"onClick\", function ActionsMenuComponent_Conditional_1_Conditional_14_Template_p_button_onClick_0_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.onClickShowAgent());\n    });\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ActionsMenuComponent_Conditional_1_Conditional_16_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 40)(1, \"span\", 41);\n    i0.ɵɵelement(2, \"i\", 42);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 43);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r11 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(6, _c1, item_r11.value === ctx_r3.selectedView));\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassMap(item_r11.icon);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(8, _c2, item_r11.icon === \"sb sb-icon-slice\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(10, _c3, item_r11.value === ctx_r3.selectedView));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r11.label);\n  }\n}\nfunction ActionsMenuComponent_Conditional_1_Conditional_16_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-menu\", 26, 2);\n    i0.ɵɵtemplate(2, ActionsMenuComponent_Conditional_1_Conditional_16_ng_template_2_Template, 5, 12, \"ng-template\", 35);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p-button\", 36);\n    i0.ɵɵlistener(\"click\", function ActionsMenuComponent_Conditional_1_Conditional_16_Template_p_button_click_3_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const calendar_view_r12 = i0.ɵɵreference(1);\n      return i0.ɵɵresetView(calendar_view_r12.toggle($event));\n    });\n    i0.ɵɵelementStart(4, \"span\", 37);\n    i0.ɵɵelement(5, \"i\");\n    i0.ɵɵelementStart(6, \"span\", 38);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(8, \"i\", 39);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"popup\", true)(\"model\", ctx_r3.calendarViews);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"rounded\", true)(\"disabled\", ctx_r3.disabled);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassMapInterpolate1(\"\", ctx_r3.selectedViewIcon, \" vertical-align-middle\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r3.selectedView);\n  }\n}\nfunction ActionsMenuComponent_Conditional_1_Conditional_17_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-button\", 44);\n    i0.ɵɵlistener(\"onClick\", function ActionsMenuComponent_Conditional_1_Conditional_17_Template_p_button_onClick_0_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.onClickShowRefiner());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r3.disabled);\n  }\n}\nfunction ActionsMenuComponent_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-toolbar\", 10)(1, \"div\", 11)(2, \"h3\");\n    i0.ɵɵelement(3, \"i\", 7);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 12)(6, \"span\", 13);\n    i0.ɵɵtemplate(7, ActionsMenuComponent_Conditional_1_Conditional_7_Template, 1, 1, \"p-button\", 14)(8, ActionsMenuComponent_Conditional_1_Conditional_8_Template, 1, 3, \"p-toggleButton\", 15)(9, ActionsMenuComponent_Conditional_1_Conditional_9_Template, 1, 2, \"p-button\", 16);\n    i0.ɵɵelementStart(10, \"span\", 17);\n    i0.ɵɵtemplate(11, ActionsMenuComponent_Conditional_1_Conditional_11_Template, 1, 1, \"p-button\", 18)(12, ActionsMenuComponent_Conditional_1_Conditional_12_Template, 1, 1);\n    i0.ɵɵelementStart(13, \"p-splitButton\", 19);\n    i0.ɵɵlistener(\"onClick\", function ActionsMenuComponent_Conditional_1_Template_p_splitButton_onClick_13_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.undoActions[0] == null ? null : ctx_r3.undoActions[0].command());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(14, ActionsMenuComponent_Conditional_1_Conditional_14_Template, 1, 0, \"p-button\", 20);\n    i0.ɵɵelementStart(15, \"app-grid-layout-manager\", 21);\n    i0.ɵɵlistener(\"selectedLayoutChange\", function ActionsMenuComponent_Conditional_1_Template_app_grid_layout_manager_selectedLayoutChange_15_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.onSelectedLayoutChange($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(16, ActionsMenuComponent_Conditional_1_Conditional_16_Template, 9, 8)(17, ActionsMenuComponent_Conditional_1_Conditional_17_Template, 1, 1, \"p-button\", 22);\n    i0.ɵɵelementStart(18, \"p-button\", 23);\n    i0.ɵɵlistener(\"onClick\", function ActionsMenuComponent_Conditional_1_Template_p_button_onClick_18_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const menu_download_r14 = i0.ɵɵreference(24);\n      return i0.ɵɵresetView(menu_download_r14.toggle($event));\n    });\n    i0.ɵɵelementStart(19, \"span\");\n    i0.ɵɵelement(20, \"i\", 24);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"span\");\n    i0.ɵɵelement(22, \"i\", 25);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(23, \"p-menu\", 26, 1);\n    i0.ɵɵelementStart(25, \"p-button\", 27);\n    i0.ɵɵlistener(\"onClick\", function ActionsMenuComponent_Conditional_1_Template_p_button_onClick_25_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.onClickRefreshGridData());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"p-button\", 28);\n    i0.ɵɵlistener(\"onClick\", function ActionsMenuComponent_Conditional_1_Template_p_button_onClick_26_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.onClickSaveChanges());\n    });\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵstyleMap(i0.ɵɵpureFunction0(24, _c0));\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.reportInfo == null ? null : ctx_r3.reportInfo.description, \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵconditional(7, ctx_r3.validationResultsCount ? 7 : -1);\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(8, ctx_r3.showColumnsOptions ? 8 : -1);\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(9, !ctx_r3.previewMode ? 9 : -1);\n    i0.ɵɵadvance();\n    i0.ɵɵpropertyInterpolate(\"pTooltip\", ctx_r3.disabled ? \"Project is locked\" : \"\");\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(11, !(ctx_r3.reportInfo == null ? null : ctx_r3.reportInfo.allowAddNewRow) || ctx_r3.disabled ? 11 : 12);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"icon\", ctx_r3.undoActions[0] == null ? null : ctx_r3.undoActions[0].icon)(\"disabled\", !ctx_r3.checkHasChanges || ctx_r3.disabled)(\"model\", ctx_r3.undoActions);\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(14, ctx_r3.agentChatFlag ? 14 : -1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"reportInfo\", ctx_r3.reportInfo)(\"agGrid\", ctx_r3.agGrid)(\"disabled\", ctx_r3.disabled)(\"layouts\", ctx_r3.layouts);\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(16, ctx_r3.calendarViewFlag && (ctx_r3.reportInfo == null ? null : ctx_r3.reportInfo.enableCalendarView) ? 16 : -1);\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(17, ctx_r3.checkHasSlicers ? 17 : -1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"rounded\", true)(\"disabled\", ctx_r3.disabled);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"popup\", true)(\"model\", ctx_r3.exportItems);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r3.disabled);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", !ctx_r3.checkHasChanges || ctx_r3.disabled);\n  }\n}\nexport class ActionsMenuComponent {\n  constructor(communicationService, changeDetectorRef, userActivityService) {\n    this.communicationService = communicationService;\n    this.changeDetectorRef = changeDetectorRef;\n    this.userActivityService = userActivityService;\n    this.hasFailures = false;\n    this.disabled = false;\n    this.selectedView = ActionableGridViewModes.Month;\n    this.calendarViewFlag = false;\n    this.agentChatFlag = false;\n    this.showColumnsOptions = false;\n    this.showFormEditor = new EventEmitter();\n    this.undoLastChange = new EventEmitter();\n    this.undoAll = new EventEmitter();\n    this.saveChanges = new EventEmitter();\n    this.refreshGridData = new EventEmitter();\n    this.sendEmailClick = new EventEmitter();\n    this.showRefiner = new EventEmitter();\n    this.showValidationResults = new EventEmitter();\n    this.changeSelectedView = new EventEmitter();\n    this.showAgentChat = new EventEmitter();\n    this.columnsOptionsChange = new EventEmitter();\n    this.exportItems = [{\n      value: null,\n      styleClass: 'hidden'\n    }, {\n      label: 'Send Email',\n      icon: 'pi pi-envelope',\n      command: () => {\n        this.onClickSendEmail();\n      }\n    }, {\n      label: 'Export as Excel',\n      icon: 'pi pi-file-excel',\n      command: () => {\n        this.onExcelExportClick();\n      }\n    }, {\n      label: 'Export as CSV',\n      icon: 'pi pi-file',\n      command: () => {\n        this.onCsvExportClick();\n      }\n    }];\n    this.calendarViews = [];\n    this.undoActions = [];\n    this.selectedViewIcon = 'fa-regular fa-calendar';\n    this.columnsOptions = false;\n    this.allReportFavorites = [];\n  }\n  ngOnInit() {\n    this.userFavoriteSub = this.communicationService.userFavorite.subscribe(event => {\n      this.allReportFavorites = event.data;\n      this.setIsFavorite();\n    });\n    this.communicationService.refreshUserFavoriteList();\n    this.mobileActions = [{\n      label: 'Favorite',\n      icon: 'pi pi-star',\n      command: () => {\n        this.onUpdateUserFavorite();\n      }\n    }, {\n      label: 'Add',\n      icon: 'pi pi-plus',\n      command: () => {\n        this.onClickShowFormEditor(false);\n      }\n    }, {\n      label: 'Undo',\n      icon: 'sb sb-icon-undo',\n      command: () => {\n        this.onClickUndoLastChange();\n      }\n    }, {\n      label: 'Undo All',\n      icon: 'pi pi-times',\n      command: () => {\n        this.onClickUndoAll();\n      }\n    }, {\n      label: 'Save',\n      icon: 'pi pi-save',\n      command: () => {\n        this.onClickSaveChanges();\n      }\n    }, {\n      label: 'Refresh',\n      icon: 'pi pi-sync',\n      command: () => {\n        this.onClickRefreshGridData();\n      }\n    }, {\n      label: 'Send Email',\n      icon: 'pi pi-envelope',\n      command: () => {\n        this.onClickSendEmail();\n      }\n    }, {\n      label: 'Export to Excel',\n      icon: 'pi pi-file-excel',\n      command: () => {\n        this.onExcelExportClick();\n      }\n    }, {\n      label: 'Export to CSV',\n      icon: 'pi pi-file',\n      command: () => {\n        this.onCsvExportClick();\n      }\n    }];\n    this.calendarViews = [{\n      value: null,\n      styleClass: 'hidden'\n    }, {\n      value: ActionableGridViewModes.Table,\n      label: ActionableGridViewModeLabels.Table,\n      icon: 'pi pi-list',\n      command: () => this.onChangeSelectedView(ActionableGridViewModes.Table, 'pi pi-list')\n    }, {\n      value: ActionableGridViewModes.Year,\n      label: ActionableGridViewModeLabels.Year,\n      icon: 'fa-solid fa-calendar',\n      command: () => this.onChangeSelectedView(ActionableGridViewModes.Year, 'fa-solid fa-calendar')\n    }, {\n      value: ActionableGridViewModes.Month,\n      label: ActionableGridViewModeLabels.Month,\n      icon: 'fa-regular fa-calendar',\n      command: () => this.onChangeSelectedView(ActionableGridViewModes.Month, 'fa-regular fa-calendar')\n    }, {\n      value: ActionableGridViewModes.Week,\n      label: ActionableGridViewModeLabels.Week,\n      icon: 'fa-solid fa-calendar-week',\n      command: () => this.onChangeSelectedView(ActionableGridViewModes.Week, 'fa-solid fa-calendar-week')\n    }];\n    this.undoActions = [{\n      label: 'Undo',\n      icon: 'sb sb-icon-undo',\n      command: () => this.onClickUndoLastChange()\n    }, {\n      label: 'Undo All',\n      icon: 'pi pi-times',\n      command: () => this.onClickUndoAll()\n    }];\n  }\n  ngOnDestroy() {\n    if (this.userFavoriteSub) {\n      this.userFavoriteSub.unsubscribe();\n    }\n  }\n  onClickShowFormEditor(arg) {\n    if (this.disabled) return;\n    this.showFormEditor.emit(arg);\n  }\n  onClickUndoLastChange() {\n    if (this.disabled) return;\n    this.undoLastChange.emit();\n  }\n  onClickUndoAll() {\n    if (this.disabled) return;\n    this.undoAll.emit();\n  }\n  onClickSaveChanges() {\n    if (this.disabled) return;\n    this.saveChanges.emit();\n  }\n  onClickRefreshGridData() {\n    if (this.disabled) return;\n    this.refreshGridData.emit();\n  }\n  onClickShowRefiner() {\n    if (this.disabled) return;\n    this.showRefiner.emit();\n  }\n  onClickShowValidationResults() {\n    this.showValidationResults.emit();\n  }\n  onClickShowAgent() {\n    this.showAgentChat.emit();\n  }\n  setIsFavorite() {\n    this.isFavorite = this.allReportFavorites.some(f => f.reportId === this.reportInfo?.reportId && f.isApp);\n    // for some weird reason angular doesn't detect the changes\n    this.changeDetectorRef.detectChanges();\n  }\n  onUpdateUserFavorite() {\n    let userFavorite = this.allReportFavorites?.find(favorite => favorite.projectId.toString() === this.reportInfo.projectId && favorite.reportId === this.reportInfo.reportId);\n    if (userFavorite) {\n      userFavorite.active = false;\n      userFavorite.isApp = window.location.pathname.includes('app-view') ? true : false;\n    } else {\n      userFavorite = this.addNewUserFavorite();\n    }\n    this.userActivityService.upsertUserFavorite(userFavorite);\n    // because of the delay we set isFavorite temporarily, after updating is done it will update again.\n    this.isFavorite = userFavorite.active;\n  }\n  addNewUserFavorite() {\n    const userFavorite = {\n      projectId: +this.reportInfo.projectId,\n      url: window.location.pathname,\n      reportId: this.reportInfo.reportId,\n      reportName: this.reportInfo.reportName,\n      projectVersionId: +this.reportInfo.projectVersionId,\n      active: true,\n      isApp: window.location.pathname.includes('app-view') ? true : false\n    };\n    return userFavorite;\n  }\n  onClickSendEmail() {\n    if (this.disabled) return;\n    this.sendEmailClick.emit();\n  }\n  onExcelExportClick() {\n    if (this.disabled) return;\n    // this.agGrid.api.exportDataAsExcel();\n    // return;\n    this.agGrid.api.exportDataAsExcel({\n      processCellCallback: params => {\n        return this.processCellsForExport(params);\n      },\n      // processHeaderCallback: (params) => {\n      //   return params.column.getColDef().headerName || params.column.getColId();\n      // },\n      // columnKeys: this.columnsToExport, \n      fileName: this.reportInfo.reportName,\n      sheetName: this.reportInfo.reportName\n    });\n  }\n  onCsvExportClick() {\n    if (this.disabled) return;\n    this.agGrid.api.exportDataAsCsv({\n      processCellCallback: params => {\n        return this.processCellsForExport(params);\n      },\n      columnKeys: this.columnsToExport,\n      fileName: this.reportInfo.reportName\n    });\n  }\n  processCellsForExport(params) {\n    // console.log(params);\n    // return params.value;\n    const exportParamsColDef = params.column.getColDef();\n    const actionableGridColDef = this.reportInfo.formatConfig?.actionableGridColumnsConfig.find(x => x.column === exportParamsColDef.field);\n    if (actionableGridColDef?.format.type === EditorColumnType.Currency) {\n      exportParamsColDef.valueFormatter = formatCurrency(params.value, actionableGridColDef.format.currency, actionableGridColDef.format.decimalPlaces);\n      return exportParamsColDef.valueFormatter;\n    }\n    return params.value;\n  }\n  onChangeSelectedView(view, icon) {\n    this.changeSelectedView.emit(view);\n    this.selectedViewIcon = icon;\n    this.selectedView = view;\n    // Resize columns if no layout is selected\n    setTimeout(() => {\n      if (this.slectedLayout === undefined) {\n        this.agGrid.api?.autoSizeAllColumns();\n      }\n    }, 0);\n  }\n  onSelectedLayoutChange(layout) {\n    this.columnsOptions = layout?.agGridSettings?.sideBar === 'columns';\n    this.slectedLayout = layout;\n  }\n  onColumnsOptionsChange(event) {\n    this.columnsOptionsChange.emit(event.checked);\n  }\n  static {\n    this.ɵfac = function ActionsMenuComponent_Factory(t) {\n      return new (t || ActionsMenuComponent)(i0.ɵɵdirectiveInject(i1.CommunicationService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i2.UserActivityService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ActionsMenuComponent,\n      selectors: [[\"app-actions-menu\"]],\n      inputs: {\n        previewMode: \"previewMode\",\n        reportInfo: \"reportInfo\",\n        checkHasChanges: \"checkHasChanges\",\n        checkHasSlicers: \"checkHasSlicers\",\n        checkHasPendingChanges: \"checkHasPendingChanges\",\n        agGrid: \"agGrid\",\n        columnsToExport: \"columnsToExport\",\n        validationResultsCount: \"validationResultsCount\",\n        hasFailures: \"hasFailures\",\n        disabled: \"disabled\",\n        selectedView: \"selectedView\",\n        calendarViewFlag: \"calendarViewFlag\",\n        agentChatFlag: \"agentChatFlag\",\n        showColumnsOptions: \"showColumnsOptions\",\n        layouts: \"layouts\"\n      },\n      outputs: {\n        showFormEditor: \"showFormEditor\",\n        undoLastChange: \"undoLastChange\",\n        undoAll: \"undoAll\",\n        saveChanges: \"saveChanges\",\n        refreshGridData: \"refreshGridData\",\n        sendEmailClick: \"sendEmailClick\",\n        showRefiner: \"showRefiner\",\n        showValidationResults: \"showValidationResults\",\n        changeSelectedView: \"changeSelectedView\",\n        showAgentChat: \"showAgentChat\",\n        columnsOptionsChange: \"columnsOptionsChange\"\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 9,\n      vars: 4,\n      consts: [[\"menu\", \"\"], [\"menu_download\", \"\"], [\"calendar_view\", \"\"], [1, \"grid-toolbar\"], [1, \"desktop-grid-tb\", 3, \"style\"], [1, \"mobile-app-menu\"], [1, \"report-title\"], [1, \"pi\", \"pi-chart-line\"], [\"appendTo\", \"body\", 3, \"model\", \"popup\"], [\"type\", \"button\", \"icon\", \"pi pi-ellipsis-h\", 1, \"mobile-menu-tb\", 3, \"onClick\"], [1, \"desktop-grid-tb\"], [1, \"p-toolbar-group-start\"], [1, \"p-toolbar-group-end\"], [1, \"tb-menu-desktop\", \"inline-flex\", \"align-items-center\"], [\"icon\", \"fa-solid fa-exclamation\", \"severity\", \"warning\", \"pTooltip\", \"Show Errors/Warnings\", \"tooltipPosition\", \"top\", \"styleClass\", \"p-overlay-badge mr-3\", \"badgeClass\", \"p-badge-warning\", 3, \"badge\"], [3, \"ngModel\", \"onLabel\", \"offLabel\"], [\"tooltipPosition\", \"top\", 3, \"icon\", \"pTooltip\"], [\"tooltipPosition\", \"top\", 1, \"inline-flex\", \"align-items-center\", 3, \"pTooltip\"], [\"pTooltip\", \"Add Row (permission required)\", \"tooltipPosition\", \"top\", \"icon\", \"pi pi-plus\", 3, \"disabled\"], [\"appendTo\", \"body\", 3, \"onClick\", \"icon\", \"disabled\", \"model\"], [\"icon\", \"fa-regular fa-comment\", \"pTooltip\", \"Launch Agent Chat\"], [3, \"selectedLayoutChange\", \"reportInfo\", \"agGrid\", \"disabled\", \"layouts\"], [\"icon\", \"pi pi-filter\", \"pTooltip\", \"Filter Your Data\", \"tooltipPosition\", \"top\", 3, \"disabled\"], [\"pTooltip\", \"Export\", \"tooltipPosition\", \"top\", \"styleClass\", \"min-w-0\", 3, \"onClick\", \"rounded\", \"disabled\"], [1, \"pi\", \"pi-file-export\", \"vertical-align-bottom\"], [1, \"pi\", \"pi-angle-down\", \"vertical-align-middle\", \"ml-1\"], [\"appendTo\", \"body\", 3, \"popup\", \"model\"], [\"icon\", \"pi pi-refresh\", \"pTooltip\", \"Refresh\", \"tooltipPosition\", \"top\", 3, \"onClick\", \"disabled\"], [\"icon\", \"pi pi-save\", \"pTooltip\", \"Save\", \"tooltipPosition\", \"top\", 3, \"onClick\", \"disabled\"], [\"icon\", \"fa-solid fa-exclamation\", \"severity\", \"warning\", \"pTooltip\", \"Show Errors/Warnings\", \"tooltipPosition\", \"top\", \"styleClass\", \"p-overlay-badge mr-3\", \"badgeClass\", \"p-badge-warning\", 3, \"onClick\", \"badge\"], [3, \"ngModelChange\", \"onChange\", \"ngModel\", \"onLabel\", \"offLabel\"], [\"tooltipPosition\", \"top\", 3, \"onClick\", \"icon\", \"pTooltip\"], [\"pTooltip\", \"Add Row (permission required)\", \"tooltipPosition\", \"top\", \"icon\", \"pi pi-plus\", 3, \"onClick\", \"disabled\"], [\"icon\", \"pi pi-plus\", 3, \"onClick\", \"disabled\"], [\"icon\", \"fa-regular fa-comment\", \"pTooltip\", \"Launch Agent Chat\", 3, \"onClick\"], [\"pTemplate\", \"item\"], [\"pTooltip\", \"Switch View\", \"tooltipPosition\", \"top\", \"styleClass\", \"min-w-0\", 3, \"click\", \"rounded\", \"disabled\"], [1, \"inline-flex\", \"align-items-center\"], [1, \"ml-2\"], [1, \"pi\", \"pi-angle-down\", \"vertical-align-middle\", \"ml-2\"], [\"pRipple\", \"\", 1, \"p-ripple\", \"cursor-pointer\", \"p-element\", \"flex\", \"items-center\", \"p-menu-item-link\", \"p-3\", 3, \"ngClass\"], [1, \"text-base\", \"text-base-text\"], [1, \"p-menuitem-icon\", 3, \"ngClass\"], [1, \"ml-2\", \"p-menuitem-text\", \"text-sm\", \"text-base-text\", \"vertical-align-middle\", \"line-height-3\", 3, \"ngClass\"], [\"icon\", \"pi pi-filter\", \"pTooltip\", \"Filter Your Data\", \"tooltipPosition\", \"top\", 3, \"onClick\", \"disabled\"]],\n      template: function ActionsMenuComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"div\", 3);\n          i0.ɵɵtemplate(1, ActionsMenuComponent_Conditional_1_Template, 27, 25, \"p-toolbar\", 4);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(2, \"div\", 5)(3, \"span\", 6);\n          i0.ɵɵelement(4, \"i\", 7);\n          i0.ɵɵtext(5);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(6, \"p-menu\", 8, 0);\n          i0.ɵɵelementStart(8, \"p-button\", 9);\n          i0.ɵɵlistener(\"onClick\", function ActionsMenuComponent_Template_p_button_onClick_8_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            const menu_r15 = i0.ɵɵreference(7);\n            return i0.ɵɵresetView(menu_r15.toggle($event));\n          });\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵconditional(1, (ctx.calendarViews == null ? null : ctx.calendarViews.length) > 0 && (ctx.undoActions == null ? null : ctx.undoActions.length) > 0 ? 1 : -1);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate1(\" \", ctx.reportInfo == null ? null : ctx.reportInfo.description, \"\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"model\", ctx.mobileActions)(\"popup\", true);\n        }\n      },\n      dependencies: [FormsModule, i3.NgControlStatus, i3.NgModel, ToolbarModule, i4.Toolbar, TooltipModule, i5.Tooltip, BadgeModule, i6.PrimeTemplate, NgClass, ButtonModule, i7.Button, MenuModule, i8.Menu, SplitButtonModule, i9.SplitButton, GridLayoutManagerComponent, ToggleButtonModule, i10.ToggleButton],\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["EventEmitter", "formatCurrency", "EditorColumnType", "MenuModule", "ButtonModule", "BadgeModule", "Ng<PERSON><PERSON>", "TooltipModule", "ToolbarModule", "SplitButtonModule", "ActionableGridViewModeLabels", "ActionableGridViewModes", "GridLayoutManagerComponent", "ToggleButtonModule", "FormsModule", "i0", "ɵɵelementStart", "ɵɵlistener", "ActionsMenuComponent_Conditional_1_Conditional_7_Template_p_button_onClick_0_listener", "ɵɵrestoreView", "_r3", "ctx_r3", "ɵɵnextContext", "ɵɵresetView", "onClickShowValidationResults", "ɵɵelementEnd", "ɵɵproperty", "validationResultsCount", "ɵɵtwoWayListener", "ActionsMenuComponent_Conditional_1_Conditional_8_Template_p_toggleButton_ngModelChange_0_listener", "$event", "_r5", "ɵɵtwoWayBindingSet", "columnsOptions", "ActionsMenuComponent_Conditional_1_Conditional_8_Template_p_toggleButton_onChange_0_listener", "onColumnsOptionsChange", "ɵɵtwoWayProperty", "ActionsMenuComponent_Conditional_1_Conditional_9_Template_p_button_onClick_0_listener", "_r6", "onUpdateUserFavorite", "isFavorite", "ActionsMenuComponent_Conditional_1_Conditional_11_Template_p_button_onClick_0_listener", "_r7", "onClickShowFormEditor", "reportInfo", "allowAddNewRow", "disabled", "ActionsMenuComponent_Conditional_1_Conditional_12_Template_p_button_onClick_0_listener", "_r8", "ActionsMenuComponent_Conditional_1_Conditional_14_Template_p_button_onClick_0_listener", "_r9", "onClickShowAgent", "ɵɵelement", "ɵɵtext", "ɵɵpureFunction1", "_c1", "item_r11", "value", "<PERSON><PERSON><PERSON><PERSON>", "ɵɵadvance", "ɵɵclassMap", "icon", "_c2", "_c3", "ɵɵtextInterpolate", "label", "ɵɵtemplate", "ActionsMenuComponent_Conditional_1_Conditional_16_ng_template_2_Template", "ActionsMenuComponent_Conditional_1_Conditional_16_Template_p_button_click_3_listener", "_r10", "calendar_view_r12", "ɵɵreference", "toggle", "calendarViews", "ɵɵclassMapInterpolate1", "selectedViewIcon", "ActionsMenuComponent_Conditional_1_Conditional_17_Template_p_button_onClick_0_listener", "_r13", "onClickShowRefiner", "ActionsMenuComponent_Conditional_1_Conditional_7_Template", "ActionsMenuComponent_Conditional_1_Conditional_8_Template", "ActionsMenuComponent_Conditional_1_Conditional_9_Template", "ActionsMenuComponent_Conditional_1_Conditional_11_Template", "ActionsMenuComponent_Conditional_1_Conditional_12_Template", "ActionsMenuComponent_Conditional_1_Template_p_splitButton_onClick_13_listener", "_r2", "undoActions", "command", "ActionsMenuComponent_Conditional_1_Conditional_14_Template", "ActionsMenuComponent_Conditional_1_Template_app_grid_layout_manager_selectedLayoutChange_15_listener", "onSelectedLayoutChange", "ActionsMenuComponent_Conditional_1_Conditional_16_Template", "ActionsMenuComponent_Conditional_1_Conditional_17_Template", "ActionsMenuComponent_Conditional_1_Template_p_button_onClick_18_listener", "menu_download_r14", "ActionsMenuComponent_Conditional_1_Template_p_button_onClick_25_listener", "onClickRefreshGridData", "ActionsMenuComponent_Conditional_1_Template_p_button_onClick_26_listener", "onClickSaveChanges", "ɵɵstyleMap", "ɵɵpureFunction0", "_c0", "ɵɵtextInterpolate1", "description", "ɵɵconditional", "showColumnsOptions", "previewMode", "ɵɵpropertyInterpolate", "checkHasChanges", "agentChatFlag", "agGrid", "layouts", "calendarViewFlag", "enableCalendarView", "checkHasSlicers", "exportItems", "ActionsMenuComponent", "constructor", "communicationService", "changeDetectorRef", "userActivityService", "hasFailures", "Month", "showFormEditor", "undoLastChange", "undoAll", "saveChanges", "refreshGridData", "sendEmailClick", "showRefiner", "showValidationResults", "changeSelectedView", "showAgentChat", "columnsOptionsChange", "styleClass", "onClickSendEmail", "onExcelExportClick", "onCsvExportClick", "allReportFavorites", "ngOnInit", "userFavoriteSub", "userFavorite", "subscribe", "event", "data", "setIsFavorite", "refreshUserFavoriteList", "mobileActions", "onClickUndoLastChange", "onClickUndoAll", "Table", "onChangeSelectedView", "Year", "Week", "ngOnDestroy", "unsubscribe", "arg", "emit", "some", "f", "reportId", "isApp", "detectChanges", "find", "favorite", "projectId", "toString", "active", "window", "location", "pathname", "includes", "addNewUserFavorite", "upsertUserFavorite", "url", "reportName", "projectVersionId", "api", "exportDataAsExcel", "processCellCallback", "params", "processCellsForExport", "fileName", "sheetName", "exportDataAsCsv", "columnKeys", "columnsToExport", "exportParamsColDef", "column", "getColDef", "actionableGridColDef", "formatConfig", "actionableGridColumnsConfig", "x", "field", "format", "type", "<PERSON><PERSON><PERSON><PERSON>", "valueFormatter", "currency", "decimalPlaces", "view", "setTimeout", "s<PERSON><PERSON><PERSON><PERSON>", "undefined", "autoSizeAllColumns", "layout", "agGridSettings", "sideBar", "checked", "ɵɵdirectiveInject", "i1", "CommunicationService", "ChangeDetectorRef", "i2", "UserActivityService", "selectors", "inputs", "checkHasPendingChanges", "outputs", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "ActionsMenuComponent_Template", "rf", "ctx", "ActionsMenuComponent_Conditional_1_Template", "ActionsMenuComponent_Template_p_button_onClick_8_listener", "_r1", "menu_r15", "length", "i3", "NgControlStatus", "NgModel", "i4", "<PERSON><PERSON><PERSON>", "i5", "<PERSON><PERSON><PERSON>", "i6", "PrimeTemplate", "i7", "<PERSON><PERSON>", "i8", "<PERSON><PERSON>", "i9", "SplitButton", "i10", "ToggleButton", "styles"], "sources": ["C:\\Projects\\SaltboxActionableGrid\\Saltbox-Data-App\\saltbox-data-app\\src\\app\\actionable-grid\\actions-menu\\actions-menu.component.ts", "C:\\Projects\\SaltboxActionableGrid\\Saltbox-Data-App\\saltbox-data-app\\src\\app\\actionable-grid\\actions-menu\\actions-menu.component.html"], "sourcesContent": ["import { ChangeDetectorRef, Component, EventEmitter, Input, OnDestroy, OnInit, Output } from '@angular/core';\r\nimport { AgGridAngular } from 'ag-grid-angular';\r\nimport { ProcessCellForExportParams } from 'ag-grid-community';\r\nimport { Subscription } from 'rxjs';\r\nimport { UserFavorite } from 'src/app/core/models/user-favorite';\r\nimport { CommunicationService } from 'src/app/core/services/communication.service';\r\nimport { UserActivityService } from 'src/app/core/services/user-activity.service';\r\nimport { ReportInfo } from 'src/app/core/models/report-info';\r\nimport { formatCurrency } from 'src/app/shared/utilities/format.functions';\r\nimport { MenuItem } from 'primeng/api';\r\nimport { EditorColumnType } from 'src/app/shared/enums/editor-column-type.enum';\r\nimport { MenuModule } from 'primeng/menu';\r\nimport { ButtonModule } from 'primeng/button';\r\nimport { BadgeModule } from 'primeng/badge';\r\nimport { Ng<PERSON><PERSON>, NgIf } from '@angular/common';\r\nimport { TooltipModule } from 'primeng/tooltip';\r\nimport { ToolbarModule } from 'primeng/toolbar';\r\nimport { SplitButtonModule } from 'primeng/splitbutton';\r\nimport { ActionableGridViewModeLabels, ActionableGridViewModes } from 'src/app/core/enums/actionable-grid';\r\nimport { GridLayoutManagerComponent } from './grid-layout-manager/grid-layout-manager.component';\r\nimport { GridLayout } from '../model/grid-layout';\r\nimport { ToggleButtonModule } from 'primeng/togglebutton';\r\nimport { FormsModule } from '@angular/forms';\r\n\r\n@Component({\r\n  selector: 'app-actions-menu',\r\n  templateUrl: './actions-menu.component.html',\r\n  styleUrls: ['./actions-menu.component.scss'],\r\n  standalone: true,\r\n  imports: [FormsModule, ToolbarModule, TooltipModule, BadgeModule, NgClass, ButtonModule, MenuModule, SplitButtonModule, NgIf, GridLayoutManagerComponent, ToggleButtonModule]\r\n})\r\nexport class ActionsMenuComponent implements OnInit, OnDestroy {\r\n\r\n  @Input() previewMode: boolean;\r\n  @Input() reportInfo: ReportInfo;\r\n  @Input() checkHasChanges: boolean;\r\n  @Input() checkHasSlicers: boolean;\r\n  @Input() checkHasPendingChanges: boolean;\r\n  @Input() agGrid: AgGridAngular;\r\n  @Input() columnsToExport: string[];\r\n  @Input() validationResultsCount: any;\r\n  @Input() hasFailures = false;\r\n  @Input() disabled = false;\r\n  @Input() selectedView = ActionableGridViewModes.Month;\r\n  @Input() calendarViewFlag = false;\r\n  @Input() agentChatFlag = false;\r\n  @Input() showColumnsOptions = false;\r\n  @Input() layouts: GridLayout[];\r\n\r\n  @Output() showFormEditor: EventEmitter<any> = new EventEmitter();\r\n  @Output() undoLastChange: EventEmitter<any> = new EventEmitter();\r\n  @Output() undoAll: EventEmitter<any> = new EventEmitter();\r\n  @Output() saveChanges: EventEmitter<any> = new EventEmitter();\r\n  @Output() refreshGridData: EventEmitter<any> = new EventEmitter();\r\n  @Output() sendEmailClick: EventEmitter<any> = new EventEmitter();\r\n  @Output() showRefiner: EventEmitter<any> = new EventEmitter();\r\n  @Output() showValidationResults: EventEmitter<any> = new EventEmitter();\r\n  @Output() changeSelectedView: EventEmitter<string> = new EventEmitter();\r\n  @Output() showAgentChat: EventEmitter<string> = new EventEmitter();\r\n  @Output() columnsOptionsChange: EventEmitter<boolean> = new EventEmitter();\r\n\r\n  mobileActions: MenuItem[];\r\n\r\n  exportItems = [\r\n    {\r\n      value: null,\r\n      styleClass: 'hidden'\r\n    },\r\n    {\r\n      label: 'Send Email', icon: 'pi pi-envelope', command: () => {\r\n        this.onClickSendEmail();\r\n      }\r\n    },\r\n    {\r\n      label: 'Export as Excel', icon: 'pi pi-file-excel', command: () => {\r\n        this.onExcelExportClick();\r\n      },\r\n    },\r\n    {\r\n      label: 'Export as CSV', icon: 'pi pi-file', command: () => {\r\n        this.onCsvExportClick();\r\n      }\r\n    }\r\n  ];\r\n\r\n  calendarViews = [];\r\n  undoActions = [];\r\n  isFavorite: boolean;\r\n  selectedViewIcon: string = 'fa-regular fa-calendar';\r\n  slectedLayout: GridLayout | undefined;\r\n  columnsOptions = false;\r\n\r\n  private userFavoriteSub: Subscription;\r\n  private allReportFavorites: UserFavorite[] = [];\r\n\r\n  constructor(\r\n    private communicationService: CommunicationService,\r\n    private changeDetectorRef: ChangeDetectorRef,\r\n    private userActivityService: UserActivityService\r\n  ) { }\r\n\r\n  ngOnInit(): void {\r\n    this.userFavoriteSub = this.communicationService.userFavorite\r\n      .subscribe(event => {\r\n        this.allReportFavorites = event.data;\r\n        this.setIsFavorite();\r\n      });\r\n    this.communicationService.refreshUserFavoriteList();\r\n\r\n    this.mobileActions = [\r\n      {\r\n        label: 'Favorite',\r\n        icon: 'pi pi-star',\r\n        command: () => {\r\n          this.onUpdateUserFavorite();\r\n        }\r\n      },\r\n      {\r\n        label: 'Add',\r\n        icon: 'pi pi-plus',\r\n        command: () => {\r\n          this.onClickShowFormEditor(false);\r\n        }\r\n      },\r\n      {\r\n        label: 'Undo',\r\n        icon: 'sb sb-icon-undo',\r\n        command: () => {\r\n          this.onClickUndoLastChange();\r\n        }\r\n      },\r\n      {\r\n        label: 'Undo All',\r\n        icon: 'pi pi-times',\r\n        command: () => {\r\n          this.onClickUndoAll();\r\n        }\r\n      },\r\n      {\r\n        label: 'Save',\r\n        icon: 'pi pi-save',\r\n        command: () => {\r\n          this.onClickSaveChanges();\r\n        }\r\n      },\r\n      {\r\n        label: 'Refresh',\r\n        icon: 'pi pi-sync',\r\n        command: () => {\r\n          this.onClickRefreshGridData();\r\n        }\r\n      },\r\n      {\r\n        label: 'Send Email',\r\n        icon: 'pi pi-envelope',\r\n        command: () => {\r\n          this.onClickSendEmail();\r\n        }\r\n      },\r\n      {\r\n        label: 'Export to Excel',\r\n        icon: 'pi pi-file-excel',\r\n        command: () => {\r\n          this.onExcelExportClick();\r\n        }\r\n      },\r\n      {\r\n        label: 'Export to CSV',\r\n        icon: 'pi pi-file',\r\n        command: () => {\r\n          this.onCsvExportClick();\r\n        }\r\n      },\r\n    ];\r\n\r\n    this.calendarViews = [\r\n      {\r\n        value: null,\r\n        styleClass: 'hidden'\r\n      },\r\n      {\r\n        value: ActionableGridViewModes.Table, label: ActionableGridViewModeLabels.Table, icon: 'pi pi-list',\r\n        command: () => this.onChangeSelectedView(ActionableGridViewModes.Table, 'pi pi-list')\r\n      },\r\n      {\r\n        value: ActionableGridViewModes.Year, label: ActionableGridViewModeLabels.Year, icon: 'fa-solid fa-calendar',\r\n        command: () => this.onChangeSelectedView(ActionableGridViewModes.Year, 'fa-solid fa-calendar')\r\n      },\r\n      {\r\n        value: ActionableGridViewModes.Month, label: ActionableGridViewModeLabels.Month, icon: 'fa-regular fa-calendar',\r\n        command: () => this.onChangeSelectedView(ActionableGridViewModes.Month, 'fa-regular fa-calendar')\r\n      },\r\n      {\r\n        value: ActionableGridViewModes.Week, label: ActionableGridViewModeLabels.Week, icon: 'fa-solid fa-calendar-week',\r\n        command: () => this.onChangeSelectedView(ActionableGridViewModes.Week, 'fa-solid fa-calendar-week')\r\n      },\r\n    ];\r\n\r\n    this.undoActions = [\r\n      {\r\n        label: 'Undo', icon: 'sb sb-icon-undo', command: () => this.onClickUndoLastChange()\r\n      },\r\n      {\r\n        label: 'Undo All', icon: 'pi pi-times', command: () => this.onClickUndoAll()\r\n      },\r\n    ];\r\n  }\r\n\r\n  ngOnDestroy() {\r\n    if (this.userFavoriteSub) {\r\n      this.userFavoriteSub.unsubscribe();\r\n    }\r\n  }\r\n\r\n  onClickShowFormEditor(arg: boolean): void {\r\n    if (this.disabled) return;\r\n\r\n    this.showFormEditor.emit(arg);\r\n  }\r\n\r\n  onClickUndoLastChange(): void {\r\n    if (this.disabled) return;\r\n\r\n    this.undoLastChange.emit();\r\n  }\r\n\r\n  onClickUndoAll(): void {\r\n    if (this.disabled) return;\r\n\r\n    this.undoAll.emit();\r\n  }\r\n\r\n  onClickSaveChanges(): void {\r\n    if (this.disabled) return;\r\n\r\n    this.saveChanges.emit();\r\n  }\r\n\r\n  onClickRefreshGridData(): void {\r\n    if (this.disabled) return;\r\n\r\n    this.refreshGridData.emit();\r\n  }\r\n\r\n  onClickShowRefiner(): void {\r\n    if (this.disabled) return;\r\n\r\n    this.showRefiner.emit();\r\n  }\r\n\r\n  onClickShowValidationResults(): void {\r\n    this.showValidationResults.emit();\r\n  }\r\n\r\n  onClickShowAgent(): void {\r\n    this.showAgentChat.emit();\r\n  }\r\n\r\n  setIsFavorite() {\r\n    this.isFavorite = this.allReportFavorites.some(f => f.reportId === this.reportInfo?.reportId && f.isApp);\r\n    // for some weird reason angular doesn't detect the changes\r\n    this.changeDetectorRef.detectChanges();\r\n  }\r\n\r\n  onUpdateUserFavorite(): void {\r\n    let userFavorite: UserFavorite = this.allReportFavorites?.find(favorite =>\r\n      favorite.projectId.toString() === this.reportInfo.projectId && favorite.reportId === this.reportInfo.reportId);\r\n\r\n    if (userFavorite) {\r\n      userFavorite.active = false;\r\n      userFavorite.isApp = window.location.pathname.includes('app-view') ? true : false;\r\n    }\r\n    else {\r\n      userFavorite = this.addNewUserFavorite();\r\n    }\r\n    this.userActivityService.upsertUserFavorite(userFavorite);\r\n    // because of the delay we set isFavorite temporarily, after updating is done it will update again.\r\n    this.isFavorite = userFavorite.active;\r\n  }\r\n\r\n  addNewUserFavorite() {\r\n    const userFavorite: UserFavorite = {\r\n      projectId: +this.reportInfo.projectId,\r\n      url: window.location.pathname,\r\n      reportId: this.reportInfo.reportId,\r\n      reportName: this.reportInfo.reportName,\r\n      projectVersionId: +this.reportInfo.projectVersionId,\r\n      active: true,\r\n      isApp: window.location.pathname.includes('app-view') ? true : false\r\n    };\r\n    return userFavorite;\r\n  }\r\n\r\n  onClickSendEmail(): void {\r\n    if (this.disabled) return;\r\n\r\n    this.sendEmailClick.emit();\r\n  }\r\n\r\n  onExcelExportClick() {\r\n    if (this.disabled) return;\r\n\r\n    // this.agGrid.api.exportDataAsExcel();\r\n    // return;\r\n    this.agGrid.api.exportDataAsExcel({\r\n      processCellCallback: (params) => {\r\n        return this.processCellsForExport(params);\r\n      },\r\n      // processHeaderCallback: (params) => {\r\n      //   return params.column.getColDef().headerName || params.column.getColId();\r\n      // },\r\n      // columnKeys: this.columnsToExport, \r\n      fileName: this.reportInfo.reportName, sheetName: this.reportInfo.reportName\r\n    });\r\n  }\r\n\r\n  onCsvExportClick() {\r\n    if (this.disabled) return;\r\n\r\n    this.agGrid.api.exportDataAsCsv({\r\n      processCellCallback: (params) => {\r\n        return this.processCellsForExport(params);\r\n      },\r\n      columnKeys: this.columnsToExport, fileName: this.reportInfo.reportName\r\n    });\r\n  }\r\n\r\n  processCellsForExport(params: ProcessCellForExportParams) {\r\n    // console.log(params);\r\n    // return params.value;\r\n    const exportParamsColDef = params.column.getColDef();\r\n    const actionableGridColDef = this.reportInfo.formatConfig?.actionableGridColumnsConfig.find(x => x.column === exportParamsColDef.field);\r\n    if (actionableGridColDef?.format.type === EditorColumnType.Currency) {\r\n      exportParamsColDef.valueFormatter = formatCurrency(\r\n        params.value,\r\n        actionableGridColDef.format.currency,\r\n        actionableGridColDef.format.decimalPlaces);\r\n      return exportParamsColDef.valueFormatter;\r\n    }\r\n\r\n    return params.value;\r\n  }\r\n\r\n  onChangeSelectedView(view: ActionableGridViewModes, icon: string) {\r\n    this.changeSelectedView.emit(view);\r\n    this.selectedViewIcon = icon;\r\n    this.selectedView = view;\r\n\r\n\r\n    // Resize columns if no layout is selected\r\n    setTimeout(() => {\r\n      if (this.slectedLayout === undefined) {\r\n        this.agGrid.api?.autoSizeAllColumns();\r\n      }\r\n    }, 0);\r\n  }\r\n\r\n  onSelectedLayoutChange(layout: GridLayout) {\r\n    this.columnsOptions = layout?.agGridSettings?.sideBar === 'columns';\r\n    this.slectedLayout = layout;\r\n  }\r\n\r\n  onColumnsOptionsChange(event: any) {\r\n    this.columnsOptionsChange.emit(event.checked);\r\n  }\r\n}\r\n", "<div class=\"grid-toolbar\">\r\n  @if(this.calendarViews?.length > 0 && this.undoActions?.length > 0) {\r\n  <p-toolbar [style]=\"{width: 'auto'}\" class=\"desktop-grid-tb\">\r\n    <div class=\"p-toolbar-group-start\">\r\n      <h3><i class=\"pi pi-chart-line\"></i> {{reportInfo?.description}}</h3>\r\n    </div>\r\n    <div class=\"p-toolbar-group-end\">\r\n      <span class=\"tb-menu-desktop inline-flex align-items-center\">\r\n        @if (this.validationResultsCount) {\r\n        <p-button icon=\"fa-solid fa-exclamation\" severity=\"warning\" pTooltip=\"Show Errors/Warnings\"\r\n          tooltipPosition=\"top\" (onClick)=\"onClickShowValidationResults()\" styleClass=\"p-overlay-badge mr-3\"\r\n          [badge]=\"validationResultsCount\" badgeClass=\"p-badge-warning\"></p-button>\r\n        }\r\n\r\n        @if (this.showColumnsOptions) {\r\n        <p-toggleButton [(ngModel)]=\"columnsOptions\" [onLabel]=\"'Columns Options On'\" [offLabel]=\"'Columns Options Off'\"\r\n          (onChange)=\"onColumnsOptionsChange($event)\" />\r\n        }\r\n\r\n        @if (!this.previewMode) {\r\n        <p-button [icon]=\"isFavorite ? 'pi pi-star-fill' : 'pi pi-star'\"\r\n          [pTooltip]=\"isFavorite ? 'Remove Favorite' : 'Add Favorite'\" tooltipPosition=\"top\"\r\n          (onClick)=\"onUpdateUserFavorite()\"></p-button>\r\n        }\r\n\r\n        <span pTooltip=\"{{disabled? 'Project is locked' : ''}}\" tooltipPosition=\"top\"\r\n          class=\"inline-flex align-items-center\">\r\n\r\n          @if (!this.reportInfo?.allowAddNewRow || disabled) {\r\n          <p-button pTooltip=\"Add Row (permission required)\" tooltipPosition=\"top\" icon=\"pi pi-plus\"\r\n            (onClick)=\"onClickShowFormEditor(false)\"\r\n            [disabled]=\"!this.reportInfo?.allowAddNewRow || disabled\"></p-button>\r\n          } @else {\r\n          <p-button icon=\"pi pi-plus\" (onClick)=\"onClickShowFormEditor(false)\"\r\n            [disabled]=\"!this.reportInfo?.allowAddNewRow || disabled\"></p-button>\r\n          }\r\n\r\n          <p-splitButton [icon]=\"undoActions[0]?.icon\" (onClick)=\"undoActions[0]?.command()\"\r\n            [disabled]=\"!checkHasChanges || disabled\" appendTo=\"body\" [model]=\"undoActions\"></p-splitButton>\r\n\r\n          @if (this.agentChatFlag) {\r\n          <p-button icon=\"fa-regular fa-comment\" pTooltip=\"Launch Agent Chat\" (onClick)=\"onClickShowAgent()\"></p-button>\r\n          }\r\n\r\n          <app-grid-layout-manager [reportInfo]=\"reportInfo\" [agGrid]=\"agGrid\" [disabled]=\"disabled\" [layouts]=\"layouts\"\r\n            (selectedLayoutChange)=\"onSelectedLayoutChange($event)\">\r\n          </app-grid-layout-manager>\r\n\r\n          @if (calendarViewFlag && reportInfo?.enableCalendarView) {\r\n          <p-menu #calendar_view [popup]=\"true\" [model]=\"this.calendarViews\" appendTo=\"body\">\r\n            <ng-template pTemplate=\"item\" let-item>\r\n              <div pRipple class=\"p-ripple cursor-pointer p-element flex items-center p-menu-item-link p-3\"\r\n                [ngClass]=\"{'bg-blue-100': (item.value === selectedView)}\">\r\n                <span class=\"text-base text-base-text\">\r\n                  <i class=\"p-menuitem-icon\" [class]=\"item.icon\"\r\n                    [ngClass]=\"{'vertical-align-top': (item.icon === 'sb sb-icon-slice')}\"></i>\r\n                </span>\r\n                <span class=\"ml-2 p-menuitem-text text-sm text-base-text vertical-align-middle line-height-3\"\r\n                  [ngClass]=\"{'font-bold': (item.value === selectedView)}\">{{ item.label }}</span>\r\n              </div>\r\n            </ng-template>\r\n          </p-menu>\r\n          <p-button [rounded]=\"true\" (click)=\"calendar_view.toggle($event)\" [disabled]=\"disabled\" pTooltip=\"Switch View\"\r\n            tooltipPosition=\"top\" styleClass=\"min-w-0\">\r\n            <span class=\"inline-flex align-items-center\">\r\n              <i class=\"{{selectedViewIcon}} vertical-align-middle\"></i>\r\n              <span class=\"ml-2\">{{selectedView}}</span>\r\n              <i class=\"pi pi-angle-down vertical-align-middle ml-2\"></i>\r\n            </span>\r\n          </p-button>\r\n          }\r\n\r\n          @if (checkHasSlicers) {\r\n          <p-button icon=\"pi pi-filter\" (onClick)=\"onClickShowRefiner()\" [disabled]=\"disabled\"\r\n            pTooltip=\"Filter Your Data\" tooltipPosition=\"top\"></p-button>\r\n          }\r\n\r\n          <p-button [rounded]=\"true\" (onClick)=\"menu_download.toggle($event)\" [disabled]=\"disabled\" pTooltip=\"Export\"\r\n            tooltipPosition=\"top\" styleClass=\"min-w-0\">\r\n            <span><i class=\"pi pi-file-export vertical-align-bottom\"></i></span>\r\n            <span><i class=\"pi pi-angle-down vertical-align-middle ml-1\"></i></span>\r\n          </p-button>\r\n          <p-menu #menu_download [popup]=\"true\" [model]=\"this.exportItems\" appendTo=\"body\"></p-menu>\r\n\r\n          <p-button icon=\"pi pi-refresh\" (onClick)=\"onClickRefreshGridData()\" [disabled]=\"disabled\" pTooltip=\"Refresh\"\r\n            tooltipPosition=\"top\"></p-button>\r\n\r\n          <p-button icon=\"pi pi-save\" (onClick)=\"onClickSaveChanges()\" [disabled]=\"!checkHasChanges || disabled\"\r\n            pTooltip=\"Save\" tooltipPosition=\"top\"></p-button>\r\n        </span>\r\n      </span>\r\n    </div>\r\n  </p-toolbar>\r\n  }\r\n</div>\r\n<div class=\"mobile-app-menu\">\r\n  <span class=\"report-title\"><i class=\"pi pi-chart-line\"></i> {{reportInfo?.description}}</span>\r\n  <p-menu #menu [model]=\"mobileActions\" [popup]=\"true\" appendTo=\"body\"></p-menu>\r\n  <p-button class=\"mobile-menu-tb\" type=\"button\" (onClick)=\"menu.toggle($event)\" icon=\"pi pi-ellipsis-h\"></p-button>\r\n</div>"], "mappings": "AAAA,SAAuCA,YAAY,QAA0C,eAAe;AAQ5G,SAASC,cAAc,QAAQ,2CAA2C;AAE1E,SAASC,gBAAgB,QAAQ,8CAA8C;AAC/E,SAASC,UAAU,QAAQ,cAAc;AACzC,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,WAAW,QAAQ,eAAe;AAC3C,SAASC,OAAO,QAAc,iBAAiB;AAC/C,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,iBAAiB,QAAQ,qBAAqB;AACvD,SAASC,4BAA4B,EAAEC,uBAAuB,QAAQ,oCAAoC;AAC1G,SAASC,0BAA0B,QAAQ,qDAAqD;AAEhG,SAASC,kBAAkB,QAAQ,sBAAsB;AACzD,SAASC,WAAW,QAAQ,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;ICbpCC,EAAA,CAAAC,cAAA,mBAEgE;IADxCD,EAAA,CAAAE,UAAA,qBAAAC,sFAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAWF,MAAA,CAAAG,4BAAA,EAA8B;IAAA,EAAC;IACFT,EAAA,CAAAU,YAAA,EAAW;;;;IAAzEV,EAAA,CAAAW,UAAA,UAAAL,MAAA,CAAAM,sBAAA,CAAgC;;;;;;IAIlCZ,EAAA,CAAAC,cAAA,yBACgD;IADhCD,EAAA,CAAAa,gBAAA,2BAAAC,kGAAAC,MAAA;MAAAf,EAAA,CAAAI,aAAA,CAAAY,GAAA;MAAA,MAAAV,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAAiB,kBAAA,CAAAX,MAAA,CAAAY,cAAA,EAAAH,MAAA,MAAAT,MAAA,CAAAY,cAAA,GAAAH,MAAA;MAAA,OAAAf,EAAA,CAAAQ,WAAA,CAAAO,MAAA;IAAA,EAA4B;IAC1Cf,EAAA,CAAAE,UAAA,sBAAAiB,6FAAAJ,MAAA;MAAAf,EAAA,CAAAI,aAAA,CAAAY,GAAA;MAAA,MAAAV,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAYF,MAAA,CAAAc,sBAAA,CAAAL,MAAA,CAA8B;IAAA,EAAC;IAD7Cf,EAAA,CAAAU,YAAA,EACgD;;;;IADhCV,EAAA,CAAAqB,gBAAA,YAAAf,MAAA,CAAAY,cAAA,CAA4B;IAAkClB,EAAjC,CAAAW,UAAA,iCAAgC,mCAAmC;;;;;;IAKhHX,EAAA,CAAAC,cAAA,mBAEqC;IAAnCD,EAAA,CAAAE,UAAA,qBAAAoB,sFAAA;MAAAtB,EAAA,CAAAI,aAAA,CAAAmB,GAAA;MAAA,MAAAjB,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAWF,MAAA,CAAAkB,oBAAA,EAAsB;IAAA,EAAC;IAACxB,EAAA,CAAAU,YAAA,EAAW;;;;IAD9CV,EADQ,CAAAW,UAAA,SAAAL,MAAA,CAAAmB,UAAA,oCAAsD,aAAAnB,MAAA,CAAAmB,UAAA,sCACF;;;;;;IAQ5DzB,EAAA,CAAAC,cAAA,mBAE4D;IAD1DD,EAAA,CAAAE,UAAA,qBAAAwB,uFAAA;MAAA1B,EAAA,CAAAI,aAAA,CAAAuB,GAAA;MAAA,MAAArB,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAWF,MAAA,CAAAsB,qBAAA,CAAsB,KAAK,CAAC;IAAA,EAAC;IACkB5B,EAAA,CAAAU,YAAA,EAAW;;;;IAArEV,EAAA,CAAAW,UAAA,eAAAL,MAAA,CAAAuB,UAAA,kBAAAvB,MAAA,CAAAuB,UAAA,CAAAC,cAAA,KAAAxB,MAAA,CAAAyB,QAAA,CAAyD;;;;;;IAE3D/B,EAAA,CAAAC,cAAA,mBAC4D;IADhCD,EAAA,CAAAE,UAAA,qBAAA8B,uFAAA;MAAAhC,EAAA,CAAAI,aAAA,CAAA6B,GAAA;MAAA,MAAA3B,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAWF,MAAA,CAAAsB,qBAAA,CAAsB,KAAK,CAAC;IAAA,EAAC;IACR5B,EAAA,CAAAU,YAAA,EAAW;;;;IAArEV,EAAA,CAAAW,UAAA,eAAAL,MAAA,CAAAuB,UAAA,kBAAAvB,MAAA,CAAAuB,UAAA,CAAAC,cAAA,KAAAxB,MAAA,CAAAyB,QAAA,CAAyD;;;;;;IAO3D/B,EAAA,CAAAC,cAAA,mBAAmG;IAA/BD,EAAA,CAAAE,UAAA,qBAAAgC,uFAAA;MAAAlC,EAAA,CAAAI,aAAA,CAAA+B,GAAA;MAAA,MAAA7B,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAWF,MAAA,CAAA8B,gBAAA,EAAkB;IAAA,EAAC;IAACpC,EAAA,CAAAU,YAAA,EAAW;;;;;IAYxGV,EAFF,CAAAC,cAAA,cAC6D,eACpB;IACrCD,EAAA,CAAAqC,SAAA,YAC6E;IAC/ErC,EAAA,CAAAU,YAAA,EAAO;IACPV,EAAA,CAAAC,cAAA,eAC2D;IAAAD,EAAA,CAAAsC,MAAA,GAAgB;IAC7EtC,EAD6E,CAAAU,YAAA,EAAO,EAC9E;;;;;IAPJV,EAAA,CAAAW,UAAA,YAAAX,EAAA,CAAAuC,eAAA,IAAAC,GAAA,EAAAC,QAAA,CAAAC,KAAA,KAAApC,MAAA,CAAAqC,YAAA,EAA0D;IAE7B3C,EAAA,CAAA4C,SAAA,GAAmB;IAAnB5C,EAAA,CAAA6C,UAAA,CAAAJ,QAAA,CAAAK,IAAA,CAAmB;IAC5C9C,EAAA,CAAAW,UAAA,YAAAX,EAAA,CAAAuC,eAAA,IAAAQ,GAAA,EAAAN,QAAA,CAAAK,IAAA,yBAAsE;IAGxE9C,EAAA,CAAA4C,SAAA,EAAwD;IAAxD5C,EAAA,CAAAW,UAAA,YAAAX,EAAA,CAAAuC,eAAA,KAAAS,GAAA,EAAAP,QAAA,CAAAC,KAAA,KAAApC,MAAA,CAAAqC,YAAA,EAAwD;IAAC3C,EAAA,CAAA4C,SAAA,EAAgB;IAAhB5C,EAAA,CAAAiD,iBAAA,CAAAR,QAAA,CAAAS,KAAA,CAAgB;;;;;;IATjFlD,EAAA,CAAAC,cAAA,oBAAmF;IACjFD,EAAA,CAAAmD,UAAA,IAAAC,wEAAA,2BAAuC;IAWzCpD,EAAA,CAAAU,YAAA,EAAS;IACTV,EAAA,CAAAC,cAAA,mBAC6C;IADlBD,EAAA,CAAAE,UAAA,mBAAAmD,qFAAAtC,MAAA;MAAAf,EAAA,CAAAI,aAAA,CAAAkD,IAAA;MAAA,MAAAC,iBAAA,GAAAvD,EAAA,CAAAwD,WAAA;MAAA,OAAAxD,EAAA,CAAAQ,WAAA,CAAS+C,iBAAA,CAAAE,MAAA,CAAA1C,MAAA,CAA4B;IAAA,EAAC;IAE/Df,EAAA,CAAAC,cAAA,eAA6C;IAC3CD,EAAA,CAAAqC,SAAA,QAA0D;IAC1DrC,EAAA,CAAAC,cAAA,eAAmB;IAAAD,EAAA,CAAAsC,MAAA,GAAgB;IAAAtC,EAAA,CAAAU,YAAA,EAAO;IAC1CV,EAAA,CAAAqC,SAAA,YAA2D;IAE/DrC,EADE,CAAAU,YAAA,EAAO,EACE;;;;IApB2BV,EAAf,CAAAW,UAAA,eAAc,UAAAL,MAAA,CAAAoD,aAAA,CAA6B;IAaxD1D,EAAA,CAAA4C,SAAA,GAAgB;IAAwC5C,EAAxD,CAAAW,UAAA,iBAAgB,aAAAL,MAAA,CAAAyB,QAAA,CAA6D;IAGhF/B,EAAA,CAAA4C,SAAA,GAAkD;IAAlD5C,EAAA,CAAA2D,sBAAA,KAAArD,MAAA,CAAAsD,gBAAA,2BAAkD;IAClC5D,EAAA,CAAA4C,SAAA,GAAgB;IAAhB5C,EAAA,CAAAiD,iBAAA,CAAA3C,MAAA,CAAAqC,YAAA,CAAgB;;;;;;IAOvC3C,EAAA,CAAAC,cAAA,mBACoD;IADtBD,EAAA,CAAAE,UAAA,qBAAA2D,uFAAA;MAAA7D,EAAA,CAAAI,aAAA,CAAA0D,IAAA;MAAA,MAAAxD,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAWF,MAAA,CAAAyD,kBAAA,EAAoB;IAAA,EAAC;IACV/D,EAAA,CAAAU,YAAA,EAAW;;;;IADAV,EAAA,CAAAW,UAAA,aAAAL,MAAA,CAAAyB,QAAA,CAAqB;;;;;;IArExF/B,EAFJ,CAAAC,cAAA,oBAA6D,cACxB,SAC7B;IAAAD,EAAA,CAAAqC,SAAA,WAAgC;IAACrC,EAAA,CAAAsC,MAAA,GAA2B;IAClEtC,EADkE,CAAAU,YAAA,EAAK,EACjE;IAEJV,EADF,CAAAC,cAAA,cAAiC,eAC8B;IAY3DD,EAXA,CAAAmD,UAAA,IAAAa,yDAAA,uBAAmC,IAAAC,yDAAA,6BAMJ,IAAAC,yDAAA,uBAKN;IAMzBlE,EAAA,CAAAC,cAAA,gBACyC;IAMrCD,EAJF,CAAAmD,UAAA,KAAAgB,0DAAA,uBAAoD,KAAAC,0DAAA,OAI3C;IAKTpE,EAAA,CAAAC,cAAA,yBACkF;IADrCD,EAAA,CAAAE,UAAA,qBAAAmE,8EAAA;MAAArE,EAAA,CAAAI,aAAA,CAAAkE,GAAA;MAAA,MAAAhE,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAAF,MAAA,CAAAiE,WAAA,CAAuB,CAAC,mBAAAjE,MAAA,CAAAiE,WAAA,CAAD,CAAC,EAAAC,OAAA;IAAA,EAAa;IACAxE,EAAA,CAAAU,YAAA,EAAgB;IAElGV,EAAA,CAAAmD,UAAA,KAAAsB,0DAAA,uBAA0B;IAI1BzE,EAAA,CAAAC,cAAA,mCAC0D;IAAxDD,EAAA,CAAAE,UAAA,kCAAAwE,qGAAA3D,MAAA;MAAAf,EAAA,CAAAI,aAAA,CAAAkE,GAAA;MAAA,MAAAhE,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAwBF,MAAA,CAAAqE,sBAAA,CAAA5D,MAAA,CAA8B;IAAA,EAAC;IACzDf,EAAA,CAAAU,YAAA,EAA0B;IA0B1BV,EAxBA,CAAAmD,UAAA,KAAAyB,0DAAA,OAA0D,KAAAC,0DAAA,uBAwBnC;IAKvB7E,EAAA,CAAAC,cAAA,oBAC6C;IADlBD,EAAA,CAAAE,UAAA,qBAAA4E,yEAAA/D,MAAA;MAAAf,EAAA,CAAAI,aAAA,CAAAkE,GAAA;MAAA,MAAAS,iBAAA,GAAA/E,EAAA,CAAAwD,WAAA;MAAA,OAAAxD,EAAA,CAAAQ,WAAA,CAAWuE,iBAAA,CAAAtB,MAAA,CAAA1C,MAAA,CAA4B;IAAA,EAAC;IAEjEf,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAqC,SAAA,aAAuD;IAAArC,EAAA,CAAAU,YAAA,EAAO;IACpEV,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAqC,SAAA,aAA2D;IACnErC,EADmE,CAAAU,YAAA,EAAO,EAC/D;IACXV,EAAA,CAAAqC,SAAA,qBAA0F;IAE1FrC,EAAA,CAAAC,cAAA,oBACwB;IADOD,EAAA,CAAAE,UAAA,qBAAA8E,yEAAA;MAAAhF,EAAA,CAAAI,aAAA,CAAAkE,GAAA;MAAA,MAAAhE,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAWF,MAAA,CAAA2E,sBAAA,EAAwB;IAAA,EAAC;IAC3CjF,EAAA,CAAAU,YAAA,EAAW;IAEnCV,EAAA,CAAAC,cAAA,oBACwC;IADZD,EAAA,CAAAE,UAAA,qBAAAgF,yEAAA;MAAAlF,EAAA,CAAAI,aAAA,CAAAkE,GAAA;MAAA,MAAAhE,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAWF,MAAA,CAAA6E,kBAAA,EAAoB;IAAA,EAAC;IAKpEnF,EAJgD,CAAAU,YAAA,EAAW,EAC9C,EACF,EACH,EACI;;;;IA1FDV,EAAA,CAAAoF,UAAA,CAAApF,EAAA,CAAAqF,eAAA,KAAAC,GAAA,EAAyB;IAEKtF,EAAA,CAAA4C,SAAA,GAA2B;IAA3B5C,EAAA,CAAAuF,kBAAA,MAAAjF,MAAA,CAAAuB,UAAA,kBAAAvB,MAAA,CAAAuB,UAAA,CAAA2D,WAAA,KAA2B;IAI9DxF,EAAA,CAAA4C,SAAA,GAIC;IAJD5C,EAAA,CAAAyF,aAAA,IAAAnF,MAAA,CAAAM,sBAAA,UAIC;IAEDZ,EAAA,CAAA4C,SAAA,EAGC;IAHD5C,EAAA,CAAAyF,aAAA,IAAAnF,MAAA,CAAAoF,kBAAA,UAGC;IAED1F,EAAA,CAAA4C,SAAA,EAIC;IAJD5C,EAAA,CAAAyF,aAAA,KAAAnF,MAAA,CAAAqF,WAAA,UAIC;IAEK3F,EAAA,CAAA4C,SAAA,EAAiD;IAAjD5C,EAAA,CAAA4F,qBAAA,aAAAtF,MAAA,CAAAyB,QAAA,4BAAiD;IAGrD/B,EAAA,CAAA4C,SAAA,EAOC;IAPD5C,EAAA,CAAAyF,aAAA,OAAAnF,MAAA,CAAAuB,UAAA,kBAAAvB,MAAA,CAAAuB,UAAA,CAAAC,cAAA,KAAAxB,MAAA,CAAAyB,QAAA,WAOC;IAEc/B,EAAA,CAAA4C,SAAA,GAA6B;IACgB5C,EAD7C,CAAAW,UAAA,SAAAL,MAAA,CAAAiE,WAAA,qBAAAjE,MAAA,CAAAiE,WAAA,IAAAzB,IAAA,CAA6B,cAAAxC,MAAA,CAAAuF,eAAA,IAAAvF,MAAA,CAAAyB,QAAA,CACD,UAAAzB,MAAA,CAAAiE,WAAA,CAAsC;IAEjFvE,EAAA,CAAA4C,SAAA,EAEC;IAFD5C,EAAA,CAAAyF,aAAA,KAAAnF,MAAA,CAAAwF,aAAA,WAEC;IAEwB9F,EAAA,CAAA4C,SAAA,EAAyB;IAAyC5C,EAAlE,CAAAW,UAAA,eAAAL,MAAA,CAAAuB,UAAA,CAAyB,WAAAvB,MAAA,CAAAyF,MAAA,CAAkB,aAAAzF,MAAA,CAAAyB,QAAA,CAAsB,YAAAzB,MAAA,CAAA0F,OAAA,CAAoB;IAI9GhG,EAAA,CAAA4C,SAAA,EAsBC;IAtBD5C,EAAA,CAAAyF,aAAA,KAAAnF,MAAA,CAAA2F,gBAAA,KAAA3F,MAAA,CAAAuB,UAAA,kBAAAvB,MAAA,CAAAuB,UAAA,CAAAqE,kBAAA,YAsBC;IAEDlG,EAAA,CAAA4C,SAAA,EAGC;IAHD5C,EAAA,CAAAyF,aAAA,KAAAnF,MAAA,CAAA6F,eAAA,WAGC;IAESnG,EAAA,CAAA4C,SAAA,EAAgB;IAA0C5C,EAA1D,CAAAW,UAAA,iBAAgB,aAAAL,MAAA,CAAAyB,QAAA,CAA+D;IAKlE/B,EAAA,CAAA4C,SAAA,GAAc;IAAC5C,EAAf,CAAAW,UAAA,eAAc,UAAAL,MAAA,CAAA8F,WAAA,CAA2B;IAEIpG,EAAA,CAAA4C,SAAA,GAAqB;IAArB5C,EAAA,CAAAW,UAAA,aAAAL,MAAA,CAAAyB,QAAA,CAAqB;IAG5B/B,EAAA,CAAA4C,SAAA,EAAyC;IAAzC5C,EAAA,CAAAW,UAAA,cAAAL,MAAA,CAAAuF,eAAA,IAAAvF,MAAA,CAAAyB,QAAA,CAAyC;;;ADxDhH,OAAM,MAAOsE,oBAAoB;EAgE/BC,YACUC,oBAA0C,EAC1CC,iBAAoC,EACpCC,mBAAwC;IAFxC,KAAAF,oBAAoB,GAApBA,oBAAoB;IACpB,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,mBAAmB,GAAnBA,mBAAmB;IAzDpB,KAAAC,WAAW,GAAG,KAAK;IACnB,KAAA3E,QAAQ,GAAG,KAAK;IAChB,KAAAY,YAAY,GAAG/C,uBAAuB,CAAC+G,KAAK;IAC5C,KAAAV,gBAAgB,GAAG,KAAK;IACxB,KAAAH,aAAa,GAAG,KAAK;IACrB,KAAAJ,kBAAkB,GAAG,KAAK;IAGzB,KAAAkB,cAAc,GAAsB,IAAI3H,YAAY,EAAE;IACtD,KAAA4H,cAAc,GAAsB,IAAI5H,YAAY,EAAE;IACtD,KAAA6H,OAAO,GAAsB,IAAI7H,YAAY,EAAE;IAC/C,KAAA8H,WAAW,GAAsB,IAAI9H,YAAY,EAAE;IACnD,KAAA+H,eAAe,GAAsB,IAAI/H,YAAY,EAAE;IACvD,KAAAgI,cAAc,GAAsB,IAAIhI,YAAY,EAAE;IACtD,KAAAiI,WAAW,GAAsB,IAAIjI,YAAY,EAAE;IACnD,KAAAkI,qBAAqB,GAAsB,IAAIlI,YAAY,EAAE;IAC7D,KAAAmI,kBAAkB,GAAyB,IAAInI,YAAY,EAAE;IAC7D,KAAAoI,aAAa,GAAyB,IAAIpI,YAAY,EAAE;IACxD,KAAAqI,oBAAoB,GAA0B,IAAIrI,YAAY,EAAE;IAI1E,KAAAmH,WAAW,GAAG,CACZ;MACE1D,KAAK,EAAE,IAAI;MACX6E,UAAU,EAAE;KACb,EACD;MACErE,KAAK,EAAE,YAAY;MAAEJ,IAAI,EAAE,gBAAgB;MAAE0B,OAAO,EAAEA,CAAA,KAAK;QACzD,IAAI,CAACgD,gBAAgB,EAAE;MACzB;KACD,EACD;MACEtE,KAAK,EAAE,iBAAiB;MAAEJ,IAAI,EAAE,kBAAkB;MAAE0B,OAAO,EAAEA,CAAA,KAAK;QAChE,IAAI,CAACiD,kBAAkB,EAAE;MAC3B;KACD,EACD;MACEvE,KAAK,EAAE,eAAe;MAAEJ,IAAI,EAAE,YAAY;MAAE0B,OAAO,EAAEA,CAAA,KAAK;QACxD,IAAI,CAACkD,gBAAgB,EAAE;MACzB;KACD,CACF;IAED,KAAAhE,aAAa,GAAG,EAAE;IAClB,KAAAa,WAAW,GAAG,EAAE;IAEhB,KAAAX,gBAAgB,GAAW,wBAAwB;IAEnD,KAAA1C,cAAc,GAAG,KAAK;IAGd,KAAAyG,kBAAkB,GAAmB,EAAE;EAM3C;EAEJC,QAAQA,CAAA;IACN,IAAI,CAACC,eAAe,GAAG,IAAI,CAACtB,oBAAoB,CAACuB,YAAY,CAC1DC,SAAS,CAACC,KAAK,IAAG;MACjB,IAAI,CAACL,kBAAkB,GAAGK,KAAK,CAACC,IAAI;MACpC,IAAI,CAACC,aAAa,EAAE;IACtB,CAAC,CAAC;IACJ,IAAI,CAAC3B,oBAAoB,CAAC4B,uBAAuB,EAAE;IAEnD,IAAI,CAACC,aAAa,GAAG,CACnB;MACElF,KAAK,EAAE,UAAU;MACjBJ,IAAI,EAAE,YAAY;MAClB0B,OAAO,EAAEA,CAAA,KAAK;QACZ,IAAI,CAAChD,oBAAoB,EAAE;MAC7B;KACD,EACD;MACE0B,KAAK,EAAE,KAAK;MACZJ,IAAI,EAAE,YAAY;MAClB0B,OAAO,EAAEA,CAAA,KAAK;QACZ,IAAI,CAAC5C,qBAAqB,CAAC,KAAK,CAAC;MACnC;KACD,EACD;MACEsB,KAAK,EAAE,MAAM;MACbJ,IAAI,EAAE,iBAAiB;MACvB0B,OAAO,EAAEA,CAAA,KAAK;QACZ,IAAI,CAAC6D,qBAAqB,EAAE;MAC9B;KACD,EACD;MACEnF,KAAK,EAAE,UAAU;MACjBJ,IAAI,EAAE,aAAa;MACnB0B,OAAO,EAAEA,CAAA,KAAK;QACZ,IAAI,CAAC8D,cAAc,EAAE;MACvB;KACD,EACD;MACEpF,KAAK,EAAE,MAAM;MACbJ,IAAI,EAAE,YAAY;MAClB0B,OAAO,EAAEA,CAAA,KAAK;QACZ,IAAI,CAACW,kBAAkB,EAAE;MAC3B;KACD,EACD;MACEjC,KAAK,EAAE,SAAS;MAChBJ,IAAI,EAAE,YAAY;MAClB0B,OAAO,EAAEA,CAAA,KAAK;QACZ,IAAI,CAACS,sBAAsB,EAAE;MAC/B;KACD,EACD;MACE/B,KAAK,EAAE,YAAY;MACnBJ,IAAI,EAAE,gBAAgB;MACtB0B,OAAO,EAAEA,CAAA,KAAK;QACZ,IAAI,CAACgD,gBAAgB,EAAE;MACzB;KACD,EACD;MACEtE,KAAK,EAAE,iBAAiB;MACxBJ,IAAI,EAAE,kBAAkB;MACxB0B,OAAO,EAAEA,CAAA,KAAK;QACZ,IAAI,CAACiD,kBAAkB,EAAE;MAC3B;KACD,EACD;MACEvE,KAAK,EAAE,eAAe;MACtBJ,IAAI,EAAE,YAAY;MAClB0B,OAAO,EAAEA,CAAA,KAAK;QACZ,IAAI,CAACkD,gBAAgB,EAAE;MACzB;KACD,CACF;IAED,IAAI,CAAChE,aAAa,GAAG,CACnB;MACEhB,KAAK,EAAE,IAAI;MACX6E,UAAU,EAAE;KACb,EACD;MACE7E,KAAK,EAAE9C,uBAAuB,CAAC2I,KAAK;MAAErF,KAAK,EAAEvD,4BAA4B,CAAC4I,KAAK;MAAEzF,IAAI,EAAE,YAAY;MACnG0B,OAAO,EAAEA,CAAA,KAAM,IAAI,CAACgE,oBAAoB,CAAC5I,uBAAuB,CAAC2I,KAAK,EAAE,YAAY;KACrF,EACD;MACE7F,KAAK,EAAE9C,uBAAuB,CAAC6I,IAAI;MAAEvF,KAAK,EAAEvD,4BAA4B,CAAC8I,IAAI;MAAE3F,IAAI,EAAE,sBAAsB;MAC3G0B,OAAO,EAAEA,CAAA,KAAM,IAAI,CAACgE,oBAAoB,CAAC5I,uBAAuB,CAAC6I,IAAI,EAAE,sBAAsB;KAC9F,EACD;MACE/F,KAAK,EAAE9C,uBAAuB,CAAC+G,KAAK;MAAEzD,KAAK,EAAEvD,4BAA4B,CAACgH,KAAK;MAAE7D,IAAI,EAAE,wBAAwB;MAC/G0B,OAAO,EAAEA,CAAA,KAAM,IAAI,CAACgE,oBAAoB,CAAC5I,uBAAuB,CAAC+G,KAAK,EAAE,wBAAwB;KACjG,EACD;MACEjE,KAAK,EAAE9C,uBAAuB,CAAC8I,IAAI;MAAExF,KAAK,EAAEvD,4BAA4B,CAAC+I,IAAI;MAAE5F,IAAI,EAAE,2BAA2B;MAChH0B,OAAO,EAAEA,CAAA,KAAM,IAAI,CAACgE,oBAAoB,CAAC5I,uBAAuB,CAAC8I,IAAI,EAAE,2BAA2B;KACnG,CACF;IAED,IAAI,CAACnE,WAAW,GAAG,CACjB;MACErB,KAAK,EAAE,MAAM;MAAEJ,IAAI,EAAE,iBAAiB;MAAE0B,OAAO,EAAEA,CAAA,KAAM,IAAI,CAAC6D,qBAAqB;KAClF,EACD;MACEnF,KAAK,EAAE,UAAU;MAAEJ,IAAI,EAAE,aAAa;MAAE0B,OAAO,EAAEA,CAAA,KAAM,IAAI,CAAC8D,cAAc;KAC3E,CACF;EACH;EAEAK,WAAWA,CAAA;IACT,IAAI,IAAI,CAACd,eAAe,EAAE;MACxB,IAAI,CAACA,eAAe,CAACe,WAAW,EAAE;IACpC;EACF;EAEAhH,qBAAqBA,CAACiH,GAAY;IAChC,IAAI,IAAI,CAAC9G,QAAQ,EAAE;IAEnB,IAAI,CAAC6E,cAAc,CAACkC,IAAI,CAACD,GAAG,CAAC;EAC/B;EAEAR,qBAAqBA,CAAA;IACnB,IAAI,IAAI,CAACtG,QAAQ,EAAE;IAEnB,IAAI,CAAC8E,cAAc,CAACiC,IAAI,EAAE;EAC5B;EAEAR,cAAcA,CAAA;IACZ,IAAI,IAAI,CAACvG,QAAQ,EAAE;IAEnB,IAAI,CAAC+E,OAAO,CAACgC,IAAI,EAAE;EACrB;EAEA3D,kBAAkBA,CAAA;IAChB,IAAI,IAAI,CAACpD,QAAQ,EAAE;IAEnB,IAAI,CAACgF,WAAW,CAAC+B,IAAI,EAAE;EACzB;EAEA7D,sBAAsBA,CAAA;IACpB,IAAI,IAAI,CAAClD,QAAQ,EAAE;IAEnB,IAAI,CAACiF,eAAe,CAAC8B,IAAI,EAAE;EAC7B;EAEA/E,kBAAkBA,CAAA;IAChB,IAAI,IAAI,CAAChC,QAAQ,EAAE;IAEnB,IAAI,CAACmF,WAAW,CAAC4B,IAAI,EAAE;EACzB;EAEArI,4BAA4BA,CAAA;IAC1B,IAAI,CAAC0G,qBAAqB,CAAC2B,IAAI,EAAE;EACnC;EAEA1G,gBAAgBA,CAAA;IACd,IAAI,CAACiF,aAAa,CAACyB,IAAI,EAAE;EAC3B;EAEAZ,aAAaA,CAAA;IACX,IAAI,CAACzG,UAAU,GAAG,IAAI,CAACkG,kBAAkB,CAACoB,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,QAAQ,KAAK,IAAI,CAACpH,UAAU,EAAEoH,QAAQ,IAAID,CAAC,CAACE,KAAK,CAAC;IACxG;IACA,IAAI,CAAC1C,iBAAiB,CAAC2C,aAAa,EAAE;EACxC;EAEA3H,oBAAoBA,CAAA;IAClB,IAAIsG,YAAY,GAAiB,IAAI,CAACH,kBAAkB,EAAEyB,IAAI,CAACC,QAAQ,IACrEA,QAAQ,CAACC,SAAS,CAACC,QAAQ,EAAE,KAAK,IAAI,CAAC1H,UAAU,CAACyH,SAAS,IAAID,QAAQ,CAACJ,QAAQ,KAAK,IAAI,CAACpH,UAAU,CAACoH,QAAQ,CAAC;IAEhH,IAAInB,YAAY,EAAE;MAChBA,YAAY,CAAC0B,MAAM,GAAG,KAAK;MAC3B1B,YAAY,CAACoB,KAAK,GAAGO,MAAM,CAACC,QAAQ,CAACC,QAAQ,CAACC,QAAQ,CAAC,UAAU,CAAC,GAAG,IAAI,GAAG,KAAK;IACnF,CAAC,MACI;MACH9B,YAAY,GAAG,IAAI,CAAC+B,kBAAkB,EAAE;IAC1C;IACA,IAAI,CAACpD,mBAAmB,CAACqD,kBAAkB,CAAChC,YAAY,CAAC;IACzD;IACA,IAAI,CAACrG,UAAU,GAAGqG,YAAY,CAAC0B,MAAM;EACvC;EAEAK,kBAAkBA,CAAA;IAChB,MAAM/B,YAAY,GAAiB;MACjCwB,SAAS,EAAE,CAAC,IAAI,CAACzH,UAAU,CAACyH,SAAS;MACrCS,GAAG,EAAEN,MAAM,CAACC,QAAQ,CAACC,QAAQ;MAC7BV,QAAQ,EAAE,IAAI,CAACpH,UAAU,CAACoH,QAAQ;MAClCe,UAAU,EAAE,IAAI,CAACnI,UAAU,CAACmI,UAAU;MACtCC,gBAAgB,EAAE,CAAC,IAAI,CAACpI,UAAU,CAACoI,gBAAgB;MACnDT,MAAM,EAAE,IAAI;MACZN,KAAK,EAAEO,MAAM,CAACC,QAAQ,CAACC,QAAQ,CAACC,QAAQ,CAAC,UAAU,CAAC,GAAG,IAAI,GAAG;KAC/D;IACD,OAAO9B,YAAY;EACrB;EAEAN,gBAAgBA,CAAA;IACd,IAAI,IAAI,CAACzF,QAAQ,EAAE;IAEnB,IAAI,CAACkF,cAAc,CAAC6B,IAAI,EAAE;EAC5B;EAEArB,kBAAkBA,CAAA;IAChB,IAAI,IAAI,CAAC1F,QAAQ,EAAE;IAEnB;IACA;IACA,IAAI,CAACgE,MAAM,CAACmE,GAAG,CAACC,iBAAiB,CAAC;MAChCC,mBAAmB,EAAGC,MAAM,IAAI;QAC9B,OAAO,IAAI,CAACC,qBAAqB,CAACD,MAAM,CAAC;MAC3C,CAAC;MACD;MACA;MACA;MACA;MACAE,QAAQ,EAAE,IAAI,CAAC1I,UAAU,CAACmI,UAAU;MAAEQ,SAAS,EAAE,IAAI,CAAC3I,UAAU,CAACmI;KAClE,CAAC;EACJ;EAEAtC,gBAAgBA,CAAA;IACd,IAAI,IAAI,CAAC3F,QAAQ,EAAE;IAEnB,IAAI,CAACgE,MAAM,CAACmE,GAAG,CAACO,eAAe,CAAC;MAC9BL,mBAAmB,EAAGC,MAAM,IAAI;QAC9B,OAAO,IAAI,CAACC,qBAAqB,CAACD,MAAM,CAAC;MAC3C,CAAC;MACDK,UAAU,EAAE,IAAI,CAACC,eAAe;MAAEJ,QAAQ,EAAE,IAAI,CAAC1I,UAAU,CAACmI;KAC7D,CAAC;EACJ;EAEAM,qBAAqBA,CAACD,MAAkC;IACtD;IACA;IACA,MAAMO,kBAAkB,GAAGP,MAAM,CAACQ,MAAM,CAACC,SAAS,EAAE;IACpD,MAAMC,oBAAoB,GAAG,IAAI,CAAClJ,UAAU,CAACmJ,YAAY,EAAEC,2BAA2B,CAAC7B,IAAI,CAAC8B,CAAC,IAAIA,CAAC,CAACL,MAAM,KAAKD,kBAAkB,CAACO,KAAK,CAAC;IACvI,IAAIJ,oBAAoB,EAAEK,MAAM,CAACC,IAAI,KAAKlM,gBAAgB,CAACmM,QAAQ,EAAE;MACnEV,kBAAkB,CAACW,cAAc,GAAGrM,cAAc,CAChDmL,MAAM,CAAC3H,KAAK,EACZqI,oBAAoB,CAACK,MAAM,CAACI,QAAQ,EACpCT,oBAAoB,CAACK,MAAM,CAACK,aAAa,CAAC;MAC5C,OAAOb,kBAAkB,CAACW,cAAc;IAC1C;IAEA,OAAOlB,MAAM,CAAC3H,KAAK;EACrB;EAEA8F,oBAAoBA,CAACkD,IAA6B,EAAE5I,IAAY;IAC9D,IAAI,CAACsE,kBAAkB,CAAC0B,IAAI,CAAC4C,IAAI,CAAC;IAClC,IAAI,CAAC9H,gBAAgB,GAAGd,IAAI;IAC5B,IAAI,CAACH,YAAY,GAAG+I,IAAI;IAGxB;IACAC,UAAU,CAAC,MAAK;MACd,IAAI,IAAI,CAACC,aAAa,KAAKC,SAAS,EAAE;QACpC,IAAI,CAAC9F,MAAM,CAACmE,GAAG,EAAE4B,kBAAkB,EAAE;MACvC;IACF,CAAC,EAAE,CAAC,CAAC;EACP;EAEAnH,sBAAsBA,CAACoH,MAAkB;IACvC,IAAI,CAAC7K,cAAc,GAAG6K,MAAM,EAAEC,cAAc,EAAEC,OAAO,KAAK,SAAS;IACnE,IAAI,CAACL,aAAa,GAAGG,MAAM;EAC7B;EAEA3K,sBAAsBA,CAAC4G,KAAU;IAC/B,IAAI,CAACV,oBAAoB,CAACwB,IAAI,CAACd,KAAK,CAACkE,OAAO,CAAC;EAC/C;;;uBA7UW7F,oBAAoB,EAAArG,EAAA,CAAAmM,iBAAA,CAAAC,EAAA,CAAAC,oBAAA,GAAArM,EAAA,CAAAmM,iBAAA,CAAAnM,EAAA,CAAAsM,iBAAA,GAAAtM,EAAA,CAAAmM,iBAAA,CAAAI,EAAA,CAAAC,mBAAA;IAAA;EAAA;;;YAApBnG,oBAAoB;MAAAoG,SAAA;MAAAC,MAAA;QAAA/G,WAAA;QAAA9D,UAAA;QAAAgE,eAAA;QAAAM,eAAA;QAAAwG,sBAAA;QAAA5G,MAAA;QAAA4E,eAAA;QAAA/J,sBAAA;QAAA8F,WAAA;QAAA3E,QAAA;QAAAY,YAAA;QAAAsD,gBAAA;QAAAH,aAAA;QAAAJ,kBAAA;QAAAM,OAAA;MAAA;MAAA4G,OAAA;QAAAhG,cAAA;QAAAC,cAAA;QAAAC,OAAA;QAAAC,WAAA;QAAAC,eAAA;QAAAC,cAAA;QAAAC,WAAA;QAAAC,qBAAA;QAAAC,kBAAA;QAAAC,aAAA;QAAAC,oBAAA;MAAA;MAAAuF,UAAA;MAAAC,QAAA,GAAA9M,EAAA,CAAA+M,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;UC/BjCrN,EAAA,CAAAC,cAAA,aAA0B;UACxBD,EAAA,CAAAmD,UAAA,IAAAoK,2CAAA,yBAAqE;UA6FvEvN,EAAA,CAAAU,YAAA,EAAM;UAEJV,EADF,CAAAC,cAAA,aAA6B,cACA;UAAAD,EAAA,CAAAqC,SAAA,WAAgC;UAACrC,EAAA,CAAAsC,MAAA,GAA2B;UAAAtC,EAAA,CAAAU,YAAA,EAAO;UAC9FV,EAAA,CAAAqC,SAAA,mBAA8E;UAC9ErC,EAAA,CAAAC,cAAA,kBAAuG;UAAxDD,EAAA,CAAAE,UAAA,qBAAAsN,0DAAAzM,MAAA;YAAAf,EAAA,CAAAI,aAAA,CAAAqN,GAAA;YAAA,MAAAC,QAAA,GAAA1N,EAAA,CAAAwD,WAAA;YAAA,OAAAxD,EAAA,CAAAQ,WAAA,CAAWkN,QAAA,CAAAjK,MAAA,CAAA1C,MAAA,CAAmB;UAAA,EAAC;UAChFf,EADyG,CAAAU,YAAA,EAAW,EAC9G;;;UAlGJV,EAAA,CAAA4C,SAAA,EA4FC;UA5FD5C,EAAA,CAAAyF,aAAA,KAAA6H,GAAA,CAAA5J,aAAA,kBAAA4J,GAAA,CAAA5J,aAAA,CAAAiK,MAAA,UAAAL,GAAA,CAAA/I,WAAA,kBAAA+I,GAAA,CAAA/I,WAAA,CAAAoJ,MAAA,eA4FC;UAG2D3N,EAAA,CAAA4C,SAAA,GAA2B;UAA3B5C,EAAA,CAAAuF,kBAAA,MAAA+H,GAAA,CAAAzL,UAAA,kBAAAyL,GAAA,CAAAzL,UAAA,CAAA2D,WAAA,KAA2B;UACzExF,EAAA,CAAA4C,SAAA,EAAuB;UAAC5C,EAAxB,CAAAW,UAAA,UAAA2M,GAAA,CAAAlF,aAAA,CAAuB,eAAe;;;qBDpE1CrI,WAAW,EAAA6N,EAAA,CAAAC,eAAA,EAAAD,EAAA,CAAAE,OAAA,EAAErO,aAAa,EAAAsO,EAAA,CAAAC,OAAA,EAAExO,aAAa,EAAAyO,EAAA,CAAAC,OAAA,EAAE5O,WAAW,EAAA6O,EAAA,CAAAC,aAAA,EAAE7O,OAAO,EAAEF,YAAY,EAAAgP,EAAA,CAAAC,MAAA,EAAElP,UAAU,EAAAmP,EAAA,CAAAC,IAAA,EAAE9O,iBAAiB,EAAA+O,EAAA,CAAAC,WAAA,EAAQ7O,0BAA0B,EAAEC,kBAAkB,EAAA6O,GAAA,CAAAC,YAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}