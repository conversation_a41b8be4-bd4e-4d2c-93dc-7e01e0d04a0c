{"ast": null, "code": "import { deepCopy } from 'src/app/shared/utilities/copy.functions';\nimport { getDefaultValue, getValue } from 'src/app/shared/utilities/datatype.functions';\nimport { GroupUpdateInfo } from 'src/app/actionable-grid/model/group-update-info';\nimport { DB_PRIMARY_KEY, RECORD_PATH_FIELD_SEP } from 'src/app/shared/constants/record.const';\nimport { EditorColumnType } from 'src/app/shared/enums/editor-column-type.enum';\nexport class CustomCellRenderer {\n  constructor(changeHistoryService, notificationService, aGGridService) {\n    this.changeHistoryService = changeHistoryService;\n    this.notificationService = notificationService;\n    this.aGGridService = aGGridService;\n    this.basePath = '';\n    this.required = false;\n    this.cellRendererInstance = this;\n    this.pivotMode = false;\n    //this is not to show editors when the row is grouped\n    this.showEditor = false;\n  }\n  refresh(params) {\n    return false;\n  }\n  agInit(params) {\n    this.cellRendererParams = params;\n    this.pivotMode = this.cellRendererParams.pivotMode;\n    this.fieldBaseSchema = this.cellRendererParams.fieldBaseSchema;\n    this.required = this.cellRendererParams.required;\n    this.showEditor = this.cellRendererParams.colDef.showRowGroup != null || this.cellRendererParams?.node?.group == false;\n  }\n  updateGroupNode(node) {\n    this.groupUpdateInfo = [];\n    this.updateGroupChildren(node);\n    if (this.groupUpdateInfo.length > 0) {\n      this.changeHistoryService.trackGroupUpdate(this.groupUpdateInfo, this.basePath);\n      this.cellRendererParams.api.refreshClientSideRowModel('group');\n      const rowNode = this.cellRendererParams.api.getRowNode(this.groupUpdateInfo[0].id);\n      this.cellRendererParams.api.ensureIndexVisible(rowNode.rowIndex);\n    }\n  }\n  // This function is used to update all the values in group.\n  updateGroupChildren(node) {\n    if (node.field === this.cellRendererParams.colDef.showRowGroup) {\n      const value = getValue(this.cellRendererParams.value, this.cellRendererParams.format?.baseType, this.cellRendererParams.format?.baseFormat, this.cellRendererParams.format?.type == EditorColumnType.DateTime);\n      if (value === undefined) {\n        this.notificationService.showError('Error', 'Input value is not in the right format');\n        return;\n      }\n      const colId = this.cellRendererParams.colDef.showRowGroup;\n      node.allLeafChildren.forEach(rowNode => {\n        this.trackChange(rowNode, colId, value, false);\n        rowNode.setDataValue(colId, value);\n        // to update the formulas\n        this.triggerDataChanged(rowNode.data);\n      });\n    } else {\n      this.updateGroupChildren(node.parent);\n    }\n  }\n  setData() {\n    const value = getValue(this.cellRendererParams.value, this.cellRendererParams.format?.baseType, this.cellRendererParams.format.baseFormat, this.cellRendererParams.format?.type == EditorColumnType.DateTime);\n    if (value === undefined) {\n      this.notificationService.showError('Error', 'Input value is not in the right format!');\n    }\n    if (this.cellRendererParams.colDef.showRowGroup) {\n      this.updateGroupNode(this.cellRendererParams.node);\n    } else {\n      this.trackChange(this.cellRendererParams.node, this.cellRendererParams.column.getId(), value);\n      // node.setDatavalue replaced with apply transaction which is a safer method, also, setDatavalue doesn't work in some cases\n      const data = this.cellRendererParams.node.data;\n      // if the value is the same no update is needed!\n      if (data[this.cellRendererParams.column.getId()] !== value) {\n        data[this.cellRendererParams.column.getId()] = value;\n        this.cellRendererParams.api.applyTransaction({\n          update: [data]\n        });\n        // to update the formulas\n        this.triggerDataChanged(this.cellRendererParams.node.data);\n        // if focus is lost(for example on dates) we need to focus current cell again\n        this.focusToCurrentCell();\n      }\n    }\n  }\n  triggerDataChanged(rowData) {\n    const gridContext = this.cellRendererParams?.context;\n    // if this is detail grid\n    if (gridContext?.getRecordPath) {\n      const gridId = gridContext.getMainGridId ? gridContext.getMainGridId() : '';\n      const recordPath = gridContext.getRecordPath()?.split(RECORD_PATH_FIELD_SEP);\n      if (gridId && recordPath?.length) this.aGGridService.triggerGridDataChangeEvent(gridId, recordPath[0]);\n    } else {\n      this.aGGridService.triggerGridDataChangeEventWithDocData(this.cellRendererParams.api.getGridId(), rowData);\n    }\n  }\n  trackChange(rowNode, colId, newValue, addHistory = true) {\n    if (this.cellRendererParams.trackChange === false || rowNode.data[colId] === newValue) return;\n    const data = deepCopy(rowNode.data);\n    data[colId] = newValue;\n    const id = rowNode.data[DB_PRIMARY_KEY];\n    this.basePath = rowNode.id.replace(id, '');\n    if (addHistory) this.changeHistoryService.trackUpdate(id, colId, rowNode.data[colId], newValue, this.basePath, !this.fieldBaseSchema?.presentationProperties?.enableFormula);else this.groupUpdateInfo.push(new GroupUpdateInfo(id, {\n      [colId]: rowNode.data[colId]\n    }, {\n      [colId]: newValue\n    }));\n  }\n  getId() {\n    if (!this.cellRendererParams.data) {\n      return '';\n    }\n    return this.cellRendererParams.data[DB_PRIMARY_KEY];\n  }\n  focusToCurrentCell() {\n    const focusedCell = this.cellRendererParams.api.getFocusedCell();\n    if (focusedCell) {\n      const rowIndex = focusedCell.rowIndex;\n      const colId = focusedCell.column.getId();\n      const cellRendererInstances = this.cellRendererParams.api.getCellRendererInstances({\n        rowNodes: [this.cellRendererParams.api.getDisplayedRowAtIndex(rowIndex)],\n        columns: [colId]\n      });\n      if (cellRendererInstances?.length && cellRendererInstances[0].focusElement) cellRendererInstances[0].focusElement();\n    }\n  }\n  groupUpdate(data) {\n    if (!this.cellRendererParams.baseSchema || !data) return;\n    const schemaFields = Object.getOwnPropertyNames(this.cellRendererParams.baseSchema.properties);\n    const originalData = deepCopy(this.cellRendererParams.node.data);\n    const id = originalData[DB_PRIMARY_KEY];\n    schemaFields.forEach(field => {\n      // check to see if the data has this property\n      if (Object.prototype.hasOwnProperty.call(data, field)) {\n        let fieldValue = data[field];\n        if (fieldValue != originalData[field]) {\n          const fieldType = this.cellRendererParams.baseSchema.properties[field].type;\n          const fieldFormat = this.cellRendererParams.baseSchema.properties[field].format;\n          fieldValue = fieldValue == null ? getDefaultValue(fieldType, fieldFormat) : fieldValue;\n          this.cellRendererParams.node.setDataValue(field, fieldValue);\n        }\n      }\n    });\n    if (this.cellRendererParams.trackChange !== false) {\n      const recordPath = this.paramsContext?.getRecordPath ? this.paramsContext?.getRecordPath() : undefined;\n      this.changeHistoryService.trackGroupUpdate([new GroupUpdateInfo(id, originalData, this.cellRendererParams.node.data)], recordPath);\n    }\n    // to update the formulas\n    this.triggerDataChanged(this.cellRendererParams.node.data);\n    this.cellRendererParams.api.refreshClientSideRowModel('group');\n  }\n  get paramsContext() {\n    if (!this.cellRendererParams?.context) {\n      console.log('Please pass the context of the gridOptions!');\n      return undefined;\n    }\n    return this.cellRendererParams?.context;\n  }\n}", "map": {"version": 3, "names": ["deepCopy", "getDefaultValue", "getValue", "GroupUpdateInfo", "DB_PRIMARY_KEY", "RECORD_PATH_FIELD_SEP", "EditorColumnType", "CustomCellRenderer", "constructor", "changeHistoryService", "notificationService", "aGGridService", "basePath", "required", "cellRendererInstance", "pivotMode", "showEditor", "refresh", "params", "agInit", "cellRendererParams", "fieldBaseSchema", "colDef", "showRowGroup", "node", "group", "updateGroupNode", "groupUpdateInfo", "updateGroupChildren", "length", "trackGroupUpdate", "api", "refreshClientSideRowModel", "rowNode", "getRowNode", "id", "ensureIndexVisible", "rowIndex", "field", "value", "format", "baseType", "baseFormat", "type", "DateTime", "undefined", "showError", "colId", "allLeafChildren", "for<PERSON>ach", "trackChange", "setDataValue", "triggerDataChanged", "data", "parent", "setData", "column", "getId", "applyTransaction", "update", "focusToCurrentCell", "rowData", "gridContext", "context", "getRecordPath", "gridId", "getMainGridId", "recordPath", "split", "triggerGridDataChangeEvent", "triggerGridDataChangeEventWithDocData", "getGridId", "newValue", "addHistory", "replace", "trackUpdate", "presentationProperties", "enableFormula", "push", "focusedCell", "getFocusedCell", "cellRendererInstances", "getCellRendererInstances", "rowNodes", "getDisplayedRowAtIndex", "columns", "focusElement", "groupUpdate", "baseSchema", "schemaFields", "Object", "getOwnPropertyNames", "properties", "originalData", "prototype", "hasOwnProperty", "call", "fieldValue", "fieldType", "fieldFormat", "paramsContext", "console", "log"], "sources": ["C:\\Projects\\SaltboxActionableGrid\\Saltbox-Data-App\\saltbox-data-app\\src\\app\\core\\ag-grid\\renderers\\ag-grid-custom-cell-renderer.ts"], "sourcesContent": ["import { ICellRendererAngularComp } from 'ag-grid-angular';\r\nimport { IRowNode } from 'ag-grid-community';\r\nimport { deepCopy } from 'src/app/shared/utilities/copy.functions';\r\nimport { ICustomCellRendererParams } from './ag-grid-custom-cell-renderer-params';\r\nimport { NotificationService } from '../../services/notification.service';\r\nimport { getDefaultValue, getValue } from 'src/app/shared/utilities/datatype.functions';\r\nimport { ChangeHistoryService } from 'src/app/actionable-grid/services/change-history.service';\r\nimport { GroupUpdateInfo } from 'src/app/actionable-grid/model/group-update-info';\r\nimport { DB_PRIMARY_KEY, RECORD_PATH_FIELD_SEP } from 'src/app/shared/constants/record.const';\r\nimport { EditorColumnType } from 'src/app/shared/enums/editor-column-type.enum';\r\nimport { IRowActionsParamsContext } from '../../models/row-actions-params-context';\r\nimport { AGGridService } from '../../services/ag-grid.service';\r\nimport { ExtendedJsonSchema } from 'src/app/shared/models/extended-json-schema';\r\n\r\nexport abstract class CustomCellRenderer implements ICellRendererAngularComp {\r\n    groupUpdateInfo: GroupUpdateInfo[];\r\n    basePath = '';\r\n    cellRendererParams: ICustomCellRendererParams;\r\n    required = false;\r\n    cellRendererInstance: CustomCellRenderer = this;\r\n    fieldBaseSchema: ExtendedJsonSchema;\r\n    pivotMode = false;\r\n\r\n    //this is not to show editors when the row is grouped\r\n    public showEditor = false;\r\n    constructor(\r\n        public changeHistoryService: ChangeHistoryService,\r\n        public notificationService: NotificationService,\r\n        public aGGridService: AGGridService) { }\r\n\r\n    refresh(params: any): boolean {\r\n        return false;\r\n    }\r\n\r\n    agInit(params: ICustomCellRendererParams): void {\r\n        this.cellRendererParams = params;\r\n        this.pivotMode = this.cellRendererParams.pivotMode;\r\n        this.fieldBaseSchema = this.cellRendererParams.fieldBaseSchema;\r\n        this.required = this.cellRendererParams.required;\r\n        this.showEditor = this.cellRendererParams.colDef.showRowGroup != null || this.cellRendererParams?.node?.group == false;\r\n    }\r\n\r\n    updateGroupNode(node: IRowNode) {\r\n        this.groupUpdateInfo = [];\r\n        this.updateGroupChildren(node);\r\n        if (this.groupUpdateInfo.length > 0) {\r\n            this.changeHistoryService.trackGroupUpdate(this.groupUpdateInfo, this.basePath);\r\n            this.cellRendererParams.api.refreshClientSideRowModel('group');\r\n\r\n            const rowNode = this.cellRendererParams.api.getRowNode(this.groupUpdateInfo[0].id);\r\n            this.cellRendererParams.api.ensureIndexVisible(rowNode.rowIndex);\r\n        }\r\n    }\r\n\r\n    // This function is used to update all the values in group.\r\n    private updateGroupChildren(node: IRowNode) {\r\n        if (node.field === this.cellRendererParams.colDef.showRowGroup) {\r\n            const value = getValue(this.cellRendererParams.value,\r\n                this.cellRendererParams.format?.baseType,\r\n                this.cellRendererParams.format?.baseFormat, this.cellRendererParams.format?.type == EditorColumnType.DateTime);\r\n            if (value === undefined) {\r\n                this.notificationService.showError('Error', 'Input value is not in the right format');\r\n                return;\r\n            }\r\n\r\n            const colId = this.cellRendererParams.colDef.showRowGroup as string;\r\n            node.allLeafChildren.forEach(rowNode => {\r\n                this.trackChange(rowNode, colId, value, false);\r\n                rowNode.setDataValue(colId, value);\r\n                \r\n                // to update the formulas\r\n                this.triggerDataChanged(rowNode.data);\r\n            });\r\n        }\r\n        else {\r\n            this.updateGroupChildren(node.parent);\r\n        }\r\n    }\r\n\r\n    public setData() {\r\n        const value = getValue(this.cellRendererParams.value,\r\n            this.cellRendererParams.format?.baseType,\r\n            this.cellRendererParams.format.baseFormat, this.cellRendererParams.format?.type == EditorColumnType.DateTime);\r\n        if (value === undefined) {\r\n            this.notificationService.showError('Error', 'Input value is not in the right format!');\r\n        }\r\n\r\n        if (this.cellRendererParams.colDef.showRowGroup) {\r\n            this.updateGroupNode(this.cellRendererParams.node);\r\n        } else {\r\n            this.trackChange(this.cellRendererParams.node, this.cellRendererParams.column.getId(), value);\r\n\r\n            // node.setDatavalue replaced with apply transaction which is a safer method, also, setDatavalue doesn't work in some cases\r\n            const data = this.cellRendererParams.node.data;\r\n\r\n            // if the value is the same no update is needed!\r\n            if (data[this.cellRendererParams.column.getId()] !== value) {\r\n                data[this.cellRendererParams.column.getId()] = value;\r\n                this.cellRendererParams.api.applyTransaction({ update: [data] });\r\n\r\n                // to update the formulas\r\n                this.triggerDataChanged(this.cellRendererParams.node.data);\r\n\r\n                // if focus is lost(for example on dates) we need to focus current cell again\r\n                this.focusToCurrentCell();\r\n            }\r\n        }\r\n    }\r\n\r\n    triggerDataChanged(rowData: any) {\r\n        const gridContext = this.cellRendererParams?.context as IRowActionsParamsContext;\r\n\r\n        // if this is detail grid\r\n        if (gridContext?.getRecordPath) {\r\n            const gridId = gridContext.getMainGridId ? gridContext.getMainGridId() : '';\r\n            const recordPath: string[] = gridContext.getRecordPath()?.split(RECORD_PATH_FIELD_SEP);\r\n\r\n            if (gridId && recordPath?.length)\r\n                this.aGGridService.triggerGridDataChangeEvent(gridId, recordPath[0]);\r\n        } else {\r\n            this.aGGridService.triggerGridDataChangeEventWithDocData(this.cellRendererParams.api.getGridId(), rowData);\r\n        }\r\n    }\r\n\r\n    public trackChange(rowNode: IRowNode, colId: string, newValue: any, addHistory = true) {\r\n        if (this.cellRendererParams.trackChange === false || rowNode.data[colId] === newValue)\r\n            return;\r\n\r\n        const data = deepCopy(rowNode.data);\r\n        data[colId] = newValue;\r\n        const id = rowNode.data[DB_PRIMARY_KEY];\r\n        this.basePath = rowNode.id.replace(id, '');\r\n\r\n        if (addHistory)\r\n            this.changeHistoryService.trackUpdate(id, colId, rowNode.data[colId], newValue, this.basePath, !this.fieldBaseSchema?.presentationProperties?.enableFormula);\r\n        else\r\n            this.groupUpdateInfo.push(new GroupUpdateInfo(id, { [colId]: rowNode.data[colId] }, { [colId]: newValue }));\r\n    }\r\n\r\n    getId(): string {\r\n        if (!this.cellRendererParams.data) {\r\n            return '';\r\n        }\r\n        return this.cellRendererParams.data[DB_PRIMARY_KEY];\r\n    }\r\n\r\n    focusToCurrentCell() {\r\n        const focusedCell = this.cellRendererParams.api.getFocusedCell();\r\n        if (focusedCell) {\r\n            const rowIndex = focusedCell.rowIndex;\r\n            const colId = focusedCell.column.getId();\r\n\r\n            const cellRendererInstances = this.cellRendererParams.api.getCellRendererInstances({\r\n                rowNodes: [this.cellRendererParams.api.getDisplayedRowAtIndex(rowIndex)],\r\n                columns: [colId]\r\n            });\r\n\r\n            if (cellRendererInstances?.length && (cellRendererInstances[0] as any).focusElement)\r\n                (cellRendererInstances[0] as any).focusElement();\r\n        }\r\n    }\r\n\r\n    public groupUpdate(data) {\r\n        if (!this.cellRendererParams.baseSchema || !data)\r\n            return;\r\n\r\n        const schemaFields = Object.getOwnPropertyNames(this.cellRendererParams.baseSchema.properties);\r\n        const originalData = deepCopy(this.cellRendererParams.node.data);\r\n        const id = originalData[DB_PRIMARY_KEY];\r\n\r\n        schemaFields.forEach(field => {\r\n            // check to see if the data has this property\r\n            if (Object.prototype.hasOwnProperty.call(data, field)) {\r\n                let fieldValue = data[field];\r\n\r\n                if (fieldValue != originalData[field]) {\r\n                    const fieldType = this.cellRendererParams.baseSchema.properties[field].type;\r\n                    const fieldFormat = this.cellRendererParams.baseSchema.properties[field].format;\r\n\r\n                    fieldValue = fieldValue == null ? getDefaultValue(fieldType, fieldFormat) : fieldValue;\r\n                    this.cellRendererParams.node.setDataValue(field, fieldValue);\r\n                }\r\n            }\r\n        });\r\n\r\n        if (this.cellRendererParams.trackChange !== false) {\r\n            const recordPath = this.paramsContext?.getRecordPath ? this.paramsContext?.getRecordPath() : undefined;\r\n            this.changeHistoryService.trackGroupUpdate([new GroupUpdateInfo(id, originalData, this.cellRendererParams.node.data)], recordPath);\r\n        }\r\n\r\n        // to update the formulas\r\n        this.triggerDataChanged(this.cellRendererParams.node.data);\r\n\r\n        this.cellRendererParams.api.refreshClientSideRowModel('group');\r\n    }\r\n\r\n    get paramsContext(): IRowActionsParamsContext {\r\n        if (!this.cellRendererParams?.context) {\r\n            console.log('Please pass the context of the gridOptions!');\r\n            return undefined;\r\n        }\r\n\r\n        return this.cellRendererParams?.context as IRowActionsParamsContext;\r\n    }\r\n}\r\n"], "mappings": "AAEA,SAASA,QAAQ,QAAQ,yCAAyC;AAGlE,SAASC,eAAe,EAAEC,QAAQ,QAAQ,6CAA6C;AAEvF,SAASC,eAAe,QAAQ,iDAAiD;AACjF,SAASC,cAAc,EAAEC,qBAAqB,QAAQ,uCAAuC;AAC7F,SAASC,gBAAgB,QAAQ,8CAA8C;AAK/E,OAAM,MAAgBC,kBAAkB;EAWpCC,YACWC,oBAA0C,EAC1CC,mBAAwC,EACxCC,aAA4B;IAF5B,KAAAF,oBAAoB,GAApBA,oBAAoB;IACpB,KAAAC,mBAAmB,GAAnBA,mBAAmB;IACnB,KAAAC,aAAa,GAAbA,aAAa;IAZxB,KAAAC,QAAQ,GAAG,EAAE;IAEb,KAAAC,QAAQ,GAAG,KAAK;IAChB,KAAAC,oBAAoB,GAAuB,IAAI;IAE/C,KAAAC,SAAS,GAAG,KAAK;IAEjB;IACO,KAAAC,UAAU,GAAG,KAAK;EAIkB;EAE3CC,OAAOA,CAACC,MAAW;IACf,OAAO,KAAK;EAChB;EAEAC,MAAMA,CAACD,MAAiC;IACpC,IAAI,CAACE,kBAAkB,GAAGF,MAAM;IAChC,IAAI,CAACH,SAAS,GAAG,IAAI,CAACK,kBAAkB,CAACL,SAAS;IAClD,IAAI,CAACM,eAAe,GAAG,IAAI,CAACD,kBAAkB,CAACC,eAAe;IAC9D,IAAI,CAACR,QAAQ,GAAG,IAAI,CAACO,kBAAkB,CAACP,QAAQ;IAChD,IAAI,CAACG,UAAU,GAAG,IAAI,CAACI,kBAAkB,CAACE,MAAM,CAACC,YAAY,IAAI,IAAI,IAAI,IAAI,CAACH,kBAAkB,EAAEI,IAAI,EAAEC,KAAK,IAAI,KAAK;EAC1H;EAEAC,eAAeA,CAACF,IAAc;IAC1B,IAAI,CAACG,eAAe,GAAG,EAAE;IACzB,IAAI,CAACC,mBAAmB,CAACJ,IAAI,CAAC;IAC9B,IAAI,IAAI,CAACG,eAAe,CAACE,MAAM,GAAG,CAAC,EAAE;MACjC,IAAI,CAACpB,oBAAoB,CAACqB,gBAAgB,CAAC,IAAI,CAACH,eAAe,EAAE,IAAI,CAACf,QAAQ,CAAC;MAC/E,IAAI,CAACQ,kBAAkB,CAACW,GAAG,CAACC,yBAAyB,CAAC,OAAO,CAAC;MAE9D,MAAMC,OAAO,GAAG,IAAI,CAACb,kBAAkB,CAACW,GAAG,CAACG,UAAU,CAAC,IAAI,CAACP,eAAe,CAAC,CAAC,CAAC,CAACQ,EAAE,CAAC;MAClF,IAAI,CAACf,kBAAkB,CAACW,GAAG,CAACK,kBAAkB,CAACH,OAAO,CAACI,QAAQ,CAAC;IACpE;EACJ;EAEA;EACQT,mBAAmBA,CAACJ,IAAc;IACtC,IAAIA,IAAI,CAACc,KAAK,KAAK,IAAI,CAAClB,kBAAkB,CAACE,MAAM,CAACC,YAAY,EAAE;MAC5D,MAAMgB,KAAK,GAAGrC,QAAQ,CAAC,IAAI,CAACkB,kBAAkB,CAACmB,KAAK,EAChD,IAAI,CAACnB,kBAAkB,CAACoB,MAAM,EAAEC,QAAQ,EACxC,IAAI,CAACrB,kBAAkB,CAACoB,MAAM,EAAEE,UAAU,EAAE,IAAI,CAACtB,kBAAkB,CAACoB,MAAM,EAAEG,IAAI,IAAIrC,gBAAgB,CAACsC,QAAQ,CAAC;MAClH,IAAIL,KAAK,KAAKM,SAAS,EAAE;QACrB,IAAI,CAACnC,mBAAmB,CAACoC,SAAS,CAAC,OAAO,EAAE,wCAAwC,CAAC;QACrF;MACJ;MAEA,MAAMC,KAAK,GAAG,IAAI,CAAC3B,kBAAkB,CAACE,MAAM,CAACC,YAAsB;MACnEC,IAAI,CAACwB,eAAe,CAACC,OAAO,CAAChB,OAAO,IAAG;QACnC,IAAI,CAACiB,WAAW,CAACjB,OAAO,EAAEc,KAAK,EAAER,KAAK,EAAE,KAAK,CAAC;QAC9CN,OAAO,CAACkB,YAAY,CAACJ,KAAK,EAAER,KAAK,CAAC;QAElC;QACA,IAAI,CAACa,kBAAkB,CAACnB,OAAO,CAACoB,IAAI,CAAC;MACzC,CAAC,CAAC;IACN,CAAC,MACI;MACD,IAAI,CAACzB,mBAAmB,CAACJ,IAAI,CAAC8B,MAAM,CAAC;IACzC;EACJ;EAEOC,OAAOA,CAAA;IACV,MAAMhB,KAAK,GAAGrC,QAAQ,CAAC,IAAI,CAACkB,kBAAkB,CAACmB,KAAK,EAChD,IAAI,CAACnB,kBAAkB,CAACoB,MAAM,EAAEC,QAAQ,EACxC,IAAI,CAACrB,kBAAkB,CAACoB,MAAM,CAACE,UAAU,EAAE,IAAI,CAACtB,kBAAkB,CAACoB,MAAM,EAAEG,IAAI,IAAIrC,gBAAgB,CAACsC,QAAQ,CAAC;IACjH,IAAIL,KAAK,KAAKM,SAAS,EAAE;MACrB,IAAI,CAACnC,mBAAmB,CAACoC,SAAS,CAAC,OAAO,EAAE,yCAAyC,CAAC;IAC1F;IAEA,IAAI,IAAI,CAAC1B,kBAAkB,CAACE,MAAM,CAACC,YAAY,EAAE;MAC7C,IAAI,CAACG,eAAe,CAAC,IAAI,CAACN,kBAAkB,CAACI,IAAI,CAAC;IACtD,CAAC,MAAM;MACH,IAAI,CAAC0B,WAAW,CAAC,IAAI,CAAC9B,kBAAkB,CAACI,IAAI,EAAE,IAAI,CAACJ,kBAAkB,CAACoC,MAAM,CAACC,KAAK,EAAE,EAAElB,KAAK,CAAC;MAE7F;MACA,MAAMc,IAAI,GAAG,IAAI,CAACjC,kBAAkB,CAACI,IAAI,CAAC6B,IAAI;MAE9C;MACA,IAAIA,IAAI,CAAC,IAAI,CAACjC,kBAAkB,CAACoC,MAAM,CAACC,KAAK,EAAE,CAAC,KAAKlB,KAAK,EAAE;QACxDc,IAAI,CAAC,IAAI,CAACjC,kBAAkB,CAACoC,MAAM,CAACC,KAAK,EAAE,CAAC,GAAGlB,KAAK;QACpD,IAAI,CAACnB,kBAAkB,CAACW,GAAG,CAAC2B,gBAAgB,CAAC;UAAEC,MAAM,EAAE,CAACN,IAAI;QAAC,CAAE,CAAC;QAEhE;QACA,IAAI,CAACD,kBAAkB,CAAC,IAAI,CAAChC,kBAAkB,CAACI,IAAI,CAAC6B,IAAI,CAAC;QAE1D;QACA,IAAI,CAACO,kBAAkB,EAAE;MAC7B;IACJ;EACJ;EAEAR,kBAAkBA,CAACS,OAAY;IAC3B,MAAMC,WAAW,GAAG,IAAI,CAAC1C,kBAAkB,EAAE2C,OAAmC;IAEhF;IACA,IAAID,WAAW,EAAEE,aAAa,EAAE;MAC5B,MAAMC,MAAM,GAAGH,WAAW,CAACI,aAAa,GAAGJ,WAAW,CAACI,aAAa,EAAE,GAAG,EAAE;MAC3E,MAAMC,UAAU,GAAaL,WAAW,CAACE,aAAa,EAAE,EAAEI,KAAK,CAAC/D,qBAAqB,CAAC;MAEtF,IAAI4D,MAAM,IAAIE,UAAU,EAAEtC,MAAM,EAC5B,IAAI,CAAClB,aAAa,CAAC0D,0BAA0B,CAACJ,MAAM,EAAEE,UAAU,CAAC,CAAC,CAAC,CAAC;IAC5E,CAAC,MAAM;MACH,IAAI,CAACxD,aAAa,CAAC2D,qCAAqC,CAAC,IAAI,CAAClD,kBAAkB,CAACW,GAAG,CAACwC,SAAS,EAAE,EAAEV,OAAO,CAAC;IAC9G;EACJ;EAEOX,WAAWA,CAACjB,OAAiB,EAAEc,KAAa,EAAEyB,QAAa,EAAEC,UAAU,GAAG,IAAI;IACjF,IAAI,IAAI,CAACrD,kBAAkB,CAAC8B,WAAW,KAAK,KAAK,IAAIjB,OAAO,CAACoB,IAAI,CAACN,KAAK,CAAC,KAAKyB,QAAQ,EACjF;IAEJ,MAAMnB,IAAI,GAAGrD,QAAQ,CAACiC,OAAO,CAACoB,IAAI,CAAC;IACnCA,IAAI,CAACN,KAAK,CAAC,GAAGyB,QAAQ;IACtB,MAAMrC,EAAE,GAAGF,OAAO,CAACoB,IAAI,CAACjD,cAAc,CAAC;IACvC,IAAI,CAACQ,QAAQ,GAAGqB,OAAO,CAACE,EAAE,CAACuC,OAAO,CAACvC,EAAE,EAAE,EAAE,CAAC;IAE1C,IAAIsC,UAAU,EACV,IAAI,CAAChE,oBAAoB,CAACkE,WAAW,CAACxC,EAAE,EAAEY,KAAK,EAAEd,OAAO,CAACoB,IAAI,CAACN,KAAK,CAAC,EAAEyB,QAAQ,EAAE,IAAI,CAAC5D,QAAQ,EAAE,CAAC,IAAI,CAACS,eAAe,EAAEuD,sBAAsB,EAAEC,aAAa,CAAC,CAAC,KAE7J,IAAI,CAAClD,eAAe,CAACmD,IAAI,CAAC,IAAI3E,eAAe,CAACgC,EAAE,EAAE;MAAE,CAACY,KAAK,GAAGd,OAAO,CAACoB,IAAI,CAACN,KAAK;IAAC,CAAE,EAAE;MAAE,CAACA,KAAK,GAAGyB;IAAQ,CAAE,CAAC,CAAC;EACnH;EAEAf,KAAKA,CAAA;IACD,IAAI,CAAC,IAAI,CAACrC,kBAAkB,CAACiC,IAAI,EAAE;MAC/B,OAAO,EAAE;IACb;IACA,OAAO,IAAI,CAACjC,kBAAkB,CAACiC,IAAI,CAACjD,cAAc,CAAC;EACvD;EAEAwD,kBAAkBA,CAAA;IACd,MAAMmB,WAAW,GAAG,IAAI,CAAC3D,kBAAkB,CAACW,GAAG,CAACiD,cAAc,EAAE;IAChE,IAAID,WAAW,EAAE;MACb,MAAM1C,QAAQ,GAAG0C,WAAW,CAAC1C,QAAQ;MACrC,MAAMU,KAAK,GAAGgC,WAAW,CAACvB,MAAM,CAACC,KAAK,EAAE;MAExC,MAAMwB,qBAAqB,GAAG,IAAI,CAAC7D,kBAAkB,CAACW,GAAG,CAACmD,wBAAwB,CAAC;QAC/EC,QAAQ,EAAE,CAAC,IAAI,CAAC/D,kBAAkB,CAACW,GAAG,CAACqD,sBAAsB,CAAC/C,QAAQ,CAAC,CAAC;QACxEgD,OAAO,EAAE,CAACtC,KAAK;OAClB,CAAC;MAEF,IAAIkC,qBAAqB,EAAEpD,MAAM,IAAKoD,qBAAqB,CAAC,CAAC,CAAS,CAACK,YAAY,EAC9EL,qBAAqB,CAAC,CAAC,CAAS,CAACK,YAAY,EAAE;IACxD;EACJ;EAEOC,WAAWA,CAAClC,IAAI;IACnB,IAAI,CAAC,IAAI,CAACjC,kBAAkB,CAACoE,UAAU,IAAI,CAACnC,IAAI,EAC5C;IAEJ,MAAMoC,YAAY,GAAGC,MAAM,CAACC,mBAAmB,CAAC,IAAI,CAACvE,kBAAkB,CAACoE,UAAU,CAACI,UAAU,CAAC;IAC9F,MAAMC,YAAY,GAAG7F,QAAQ,CAAC,IAAI,CAACoB,kBAAkB,CAACI,IAAI,CAAC6B,IAAI,CAAC;IAChE,MAAMlB,EAAE,GAAG0D,YAAY,CAACzF,cAAc,CAAC;IAEvCqF,YAAY,CAACxC,OAAO,CAACX,KAAK,IAAG;MACzB;MACA,IAAIoD,MAAM,CAACI,SAAS,CAACC,cAAc,CAACC,IAAI,CAAC3C,IAAI,EAAEf,KAAK,CAAC,EAAE;QACnD,IAAI2D,UAAU,GAAG5C,IAAI,CAACf,KAAK,CAAC;QAE5B,IAAI2D,UAAU,IAAIJ,YAAY,CAACvD,KAAK,CAAC,EAAE;UACnC,MAAM4D,SAAS,GAAG,IAAI,CAAC9E,kBAAkB,CAACoE,UAAU,CAACI,UAAU,CAACtD,KAAK,CAAC,CAACK,IAAI;UAC3E,MAAMwD,WAAW,GAAG,IAAI,CAAC/E,kBAAkB,CAACoE,UAAU,CAACI,UAAU,CAACtD,KAAK,CAAC,CAACE,MAAM;UAE/EyD,UAAU,GAAGA,UAAU,IAAI,IAAI,GAAGhG,eAAe,CAACiG,SAAS,EAAEC,WAAW,CAAC,GAAGF,UAAU;UACtF,IAAI,CAAC7E,kBAAkB,CAACI,IAAI,CAAC2B,YAAY,CAACb,KAAK,EAAE2D,UAAU,CAAC;QAChE;MACJ;IACJ,CAAC,CAAC;IAEF,IAAI,IAAI,CAAC7E,kBAAkB,CAAC8B,WAAW,KAAK,KAAK,EAAE;MAC/C,MAAMiB,UAAU,GAAG,IAAI,CAACiC,aAAa,EAAEpC,aAAa,GAAG,IAAI,CAACoC,aAAa,EAAEpC,aAAa,EAAE,GAAGnB,SAAS;MACtG,IAAI,CAACpC,oBAAoB,CAACqB,gBAAgB,CAAC,CAAC,IAAI3B,eAAe,CAACgC,EAAE,EAAE0D,YAAY,EAAE,IAAI,CAACzE,kBAAkB,CAACI,IAAI,CAAC6B,IAAI,CAAC,CAAC,EAAEc,UAAU,CAAC;IACtI;IAEA;IACA,IAAI,CAACf,kBAAkB,CAAC,IAAI,CAAChC,kBAAkB,CAACI,IAAI,CAAC6B,IAAI,CAAC;IAE1D,IAAI,CAACjC,kBAAkB,CAACW,GAAG,CAACC,yBAAyB,CAAC,OAAO,CAAC;EAClE;EAEA,IAAIoE,aAAaA,CAAA;IACb,IAAI,CAAC,IAAI,CAAChF,kBAAkB,EAAE2C,OAAO,EAAE;MACnCsC,OAAO,CAACC,GAAG,CAAC,6CAA6C,CAAC;MAC1D,OAAOzD,SAAS;IACpB;IAEA,OAAO,IAAI,CAACzB,kBAAkB,EAAE2C,OAAmC;EACvE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}