{"ast": null, "code": "import _asyncToGenerator from \"C:/Projects/SaltboxActionableGrid/Saltbox-Data-App/saltbox-data-app/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { EventEmitter } from '@angular/core';\nimport { FileUploadDetails } from '../../shared/models/file-upload-details';\nimport moment from 'moment/moment';\nimport { HttpEventType } from '@angular/common/http';\nimport { FileUploadModule } from 'primeng/fileupload';\nimport { UserPermissionLevels } from 'src/app/core/enums/shared';\nimport { TooltipModule } from 'primeng/tooltip';\nimport { ButtonModule } from 'primeng/button';\nimport { TableModule } from 'primeng/table';\nimport { SharedModule } from 'primeng/api';\nimport { NgClass, NgIf } from '@angular/common';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/core/services/notification.service\";\nimport * as i2 from \"primeng/fileupload\";\nimport * as i3 from \"primeng/api\";\nimport * as i4 from \"primeng/button\";\nimport * as i5 from \"primeng/table\";\nimport * as i6 from \"primeng/tooltip\";\nconst _c0 = [\"fileUploader\"];\nfunction FileUploadComponent_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 6);\n    i0.ɵɵtext(1, \"You can upload files with the button or drag-and-drop.\");\n    i0.ɵɵelement(2, \"br\");\n    i0.ɵɵtext(3, \" Once uploaded, make sure you click the Save button on the grid to attach the files.\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction FileUploadComponent_ng_template_4_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 9);\n    i0.ɵɵtext(1, \" Drag & Drop Files here\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction FileUploadComponent_ng_template_4_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 9);\n    i0.ɵɵtext(1, \"Loading...\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction FileUploadComponent_ng_template_4_div_2_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"th\", 15);\n    i0.ɵɵtext(2, \"File\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"th\", 16);\n    i0.ɵɵtext(4, \"Type\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"th\", 17);\n    i0.ɵɵtext(6, \"Uploaded On\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"th\", 18);\n    i0.ɵɵtext(8, \"Uploaded By\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(9, \"th\", 18);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction FileUploadComponent_ng_template_4_div_2_ng_template_3_p_button_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-button\", 22);\n    i0.ɵɵlistener(\"onClick\", function FileUploadComponent_ng_template_4_div_2_ng_template_3_p_button_12_Template_p_button_onClick_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const file_r3 = i0.ɵɵnextContext().$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r3.onRemoveFileClick(file_r3));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"text\", true);\n  }\n}\nfunction FileUploadComponent_ng_template_4_div_2_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\")(10, \"span\", 19)(11, \"p-button\", 20);\n    i0.ɵɵlistener(\"onClick\", function FileUploadComponent_ng_template_4_div_2_ng_template_3_Template_p_button_onClick_11_listener() {\n      const file_r3 = i0.ɵɵrestoreView(_r2).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r3.onDownloadFileClick(file_r3));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(12, FileUploadComponent_ng_template_4_div_2_ng_template_3_p_button_12_Template, 1, 1, \"p-button\", 21);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const file_r3 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(file_r3.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(file_r3.fileShareFileType);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(file_r3.uploadedDate);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(file_r3.uploadedByName);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"outlined\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.userPermissionLevel !== \"View\");\n  }\n}\nfunction FileUploadComponent_ng_template_4_div_2_ng_template_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 23);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"Files: \", ctx_r3.uploadedFiles.length, \"\");\n  }\n}\nfunction FileUploadComponent_ng_template_4_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 10)(1, \"p-table\", 11);\n    i0.ɵɵtemplate(2, FileUploadComponent_ng_template_4_div_2_ng_template_2_Template, 10, 0, \"ng-template\", 12)(3, FileUploadComponent_ng_template_4_div_2_ng_template_3_Template, 13, 6, \"ng-template\", 13)(4, FileUploadComponent_ng_template_4_div_2_ng_template_4_Template, 2, 1, \"ng-template\", 14);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"value\", ctx_r3.uploadedFiles)(\"tableStyle\", ctx_r3.tableStyle);\n  }\n}\nfunction FileUploadComponent_ng_template_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, FileUploadComponent_ng_template_4_div_0_Template, 2, 0, \"div\", 7)(1, FileUploadComponent_ng_template_4_div_1_Template, 2, 0, \"div\", 7)(2, FileUploadComponent_ng_template_4_div_2_Template, 5, 2, \"div\", 8);\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r3.uploadedFiles.length && !ctx_r3.loadingFiles);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.loadingFiles && !ctx_r3.uploadedFiles.length);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.uploadedFiles.length);\n  }\n}\nfunction FileUploadComponent_ng_template_5_Template(rf, ctx) {}\nexport class FileUploadComponent {\n  constructor(messageSvc) {\n    this.messageSvc = messageSvc;\n    this.uploadedFiles = [];\n    this.maximumFileSize = 5242880; // Maximum file size for each file\n    this.multipleAllowed = true; // Defines the number of uploads allowed to upload\n    this.fileToAccept = '.pdf,.xls,.xlsx,.doc,.csv,.docx,.txt,.ppt,.pptx,.jpeg,.jpg,.gif,.png'; // File Types to Accept\n    this.fileUploadContainerClass = 'default-fileUpload'; // Customized Default container DIV wrapper-class\n    this.projectId = '';\n    this.versionId = '';\n    this.dataStoreName = '';\n    this.recordId = '';\n    this.dateFormat = 'YYYY/MM/DD';\n    this.aliasIds = [];\n    this.isPreview = false;\n    this.userPermissionLevel = UserPermissionLevels.EditAndDelete;\n    this.fileUploadEvent = new EventEmitter();\n    this.getFilesUploaded = new EventEmitter();\n    this.loadingFiles = false;\n    this.tableStyle = {\n      'table-layout': 'auto',\n      width: '100%'\n    };\n  }\n  ngOnInit() {\n    if (this.aliasIds) {\n      this.getFilesFromServer();\n    }\n  }\n  ngOnChanges(changes) {\n    if (changes.aliasIds && !changes.aliasIds?.isFirstChange()) {\n      this.getFilesFromServer();\n    }\n  }\n  onFileSelectClick(event) {\n    const currentFiles = event.currentFiles;\n    this.processSelectedFiles(currentFiles).then(res => {\n      this.aliasIds = this.uploadedFiles.map(file => {\n        return file.aliasId;\n      });\n      this.fileUploadEvent.emit(this.aliasIds);\n      this.fileUploader.clear();\n    });\n  }\n  processSelectedFiles(files) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      for (const file of files) {\n        const fileUploadDetails = new FileUploadDetails(_this.projectId, _this.versionId, _this.recordId, _this.dataStoreName, file.name, file);\n        yield _this.saveFile(fileUploadDetails);\n      }\n    })();\n  }\n  saveFile(fileUploadDetails) {\n    return new Promise(resolve => {\n      this.fileService.uploadFile(fileUploadDetails).subscribe(fileInfo => {\n        fileInfo.uploadedDate = moment(fileInfo.uploadedDate, this.dateFormat, false).format(this.dateFormat).toString();\n        this.uploadedFiles.push(fileInfo);\n        resolve();\n      }, error => {\n        console.log(error);\n        resolve();\n      });\n    });\n  }\n  getFilesFromServer() {\n    var _this2 = this;\n    this.loadingFiles = true;\n    const fileUploadDetails = new FileUploadDetails(this.projectId, this.versionId, this.recordId, this.dataStoreName);\n    fileUploadDetails.aliasIds = this.aliasIds;\n    // start rewriting some things here\n    const getFilesCall = /*#__PURE__*/function () {\n      var _ref = _asyncToGenerator(function* () {\n        if (_this2.recordId) {\n          yield _this2.getFileInfo(fileUploadDetails);\n        }\n      });\n      return function getFilesCall() {\n        return _ref.apply(this, arguments);\n      };\n    }();\n    getFilesCall().then(res => {\n      this.loadingFiles = false;\n    });\n  }\n  getFileInfo(fileUploadDetails) {\n    return new Promise(resolve => {\n      this.fileService.getFiles(fileUploadDetails).subscribe(filesInfo => {\n        filesInfo.forEach(fileUpload => {\n          fileUpload.uploadedDate = moment(fileUpload.uploadedDate, this.dateFormat, false).format(this.dateFormat).toString();\n        });\n        this.uploadedFiles = filesInfo;\n        resolve();\n      }, error => {\n        console.log(`File Request Error: ${error.statusText}...`);\n        resolve();\n      });\n    });\n  }\n  onDownloadFileClick(attchmentInfo) {\n    const fileUploadDetails = new FileUploadDetails(this.projectId, this.versionId, this.recordId, this.dataStoreName, attchmentInfo.name);\n    fileUploadDetails.aliasId = attchmentInfo.aliasId;\n    this.fileService.getFileContent(fileUploadDetails).subscribe(event => {\n      if (event.type === HttpEventType.Response) {\n        const targetFileElement = document.createElement('a');\n        targetFileElement.setAttribute('style', 'display:none;');\n        document.body.appendChild(targetFileElement);\n        targetFileElement.href = URL.createObjectURL(event.body);\n        targetFileElement.download = `${attchmentInfo.name}.${attchmentInfo.fileShareFileType}`;\n        targetFileElement.target = '_blank';\n        targetFileElement.click();\n        document.body.removeChild(targetFileElement);\n      }\n    }, error => {\n      console.log(`File Download Error: ${error.status}: Unable to complete request...`);\n    });\n  }\n  onRemoveFileClick(fileInfo) {\n    const fileUploadDetails = new FileUploadDetails(this.projectId, this.versionId, this.recordId, this.dataStoreName, fileInfo.name);\n    fileUploadDetails.aliasId = fileInfo.aliasId;\n    this.fileService.deleteFile(fileUploadDetails).subscribe(fileUpload => {\n      const fileIndex = this.uploadedFiles.findIndex(file => file.aliasId === fileInfo.aliasId);\n      if (fileIndex >= 0) {\n        this.uploadedFiles.splice(fileIndex, 1);\n        this.fileUploadEvent.emit(this.uploadedFiles.map(f => f.aliasId));\n      }\n    }, error => {\n      this.messageSvc.showError('Error', `There was a problem deleting '${fileInfo.name}' from the server`);\n    });\n  }\n  static {\n    this.ɵfac = function FileUploadComponent_Factory(t) {\n      return new (t || FileUploadComponent)(i0.ɵɵdirectiveInject(i1.NotificationService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: FileUploadComponent,\n      selectors: [[\"app-file-upload\"]],\n      viewQuery: function FileUploadComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.fileUploader = _t.first);\n        }\n      },\n      inputs: {\n        uploadedFiles: \"uploadedFiles\",\n        maximumFileSize: \"maximumFileSize\",\n        multipleAllowed: \"multipleAllowed\",\n        fileToAccept: \"fileToAccept\",\n        fileUploadContainerClass: \"fileUploadContainerClass\",\n        projectId: \"projectId\",\n        versionId: \"versionId\",\n        dataStoreName: \"dataStoreName\",\n        recordId: \"recordId\",\n        dateFormat: \"dateFormat\",\n        aliasIds: \"aliasIds\",\n        fileService: \"fileService\",\n        isPreview: \"isPreview\",\n        userPermissionLevel: \"userPermissionLevel\"\n      },\n      outputs: {\n        fileUploadEvent: \"fileUploadEvent\",\n        getFilesUploaded: \"getFilesUploaded\"\n      },\n      standalone: true,\n      features: [i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n      decls: 6,\n      vars: 7,\n      consts: [[\"fileUploader\", \"\"], [3, \"ngClass\"], [\"name\", \"fileUploader\", \"mode\", \"advanced\", \"chooseLabel\", \"Upload a File\", \"chooseIcon\", \"pi pi-upload\", 1, \"customFileUploader\", 3, \"onSelect\", \"disabled\", \"multiple\", \"maxFileSize\", \"accept\", \"showUploadButton\", \"showCancelButton\"], [\"pTemplate\", \"toolbar\"], [\"pTemplate\", \"content\"], [\"pTemplate\", \"file\"], [1, \"fileUpload-toolbar-text\"], [\"class\", \"fileUpload-content-template\", 4, \"ngIf\"], [\"class\", \"fileUpload-datatable\", 4, \"ngIf\"], [1, \"fileUpload-content-template\"], [1, \"fileUpload-datatable\"], [\"styleClass\", \"p-datatable-striped\", 3, \"value\", \"tableStyle\"], [\"pTemplate\", \"header\"], [\"pTemplate\", \"body\"], [\"pTemplate\", \"footer\"], [2, \"width\", \"40%\"], [2, \"width\", \"5%\"], [2, \"width\", \"15%\"], [2, \"width\", \"20%\"], [1, \"flex\"], [\"icon\", \"pi pi-download\", \"pTooltip\", \"Download\", 3, \"onClick\", \"outlined\"], [\"severity\", \"danger\", \"icon\", \"pi pi-trash\", \"pTooltip\", \"Delete\", 3, \"text\", \"onClick\", 4, \"ngIf\"], [\"severity\", \"danger\", \"icon\", \"pi pi-trash\", \"pTooltip\", \"Delete\", 3, \"onClick\", \"text\"], [1, \"template-footer-text\"]],\n      template: function FileUploadComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"div\", 1)(1, \"p-fileUpload\", 2, 0);\n          i0.ɵɵlistener(\"onSelect\", function FileUploadComponent_Template_p_fileUpload_onSelect_1_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onFileSelectClick($event));\n          });\n          i0.ɵɵtemplate(3, FileUploadComponent_ng_template_3_Template, 4, 0, \"ng-template\", 3)(4, FileUploadComponent_ng_template_4_Template, 3, 3, \"ng-template\", 4)(5, FileUploadComponent_ng_template_5_Template, 0, 0, \"ng-template\", 5);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngClass\", ctx.fileUploadContainerClass);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"disabled\", ctx.isPreview || ctx.userPermissionLevel === \"View\")(\"multiple\", ctx.multipleAllowed)(\"maxFileSize\", ctx.maximumFileSize)(\"accept\", ctx.fileToAccept)(\"showUploadButton\", false)(\"showCancelButton\", false);\n        }\n      },\n      dependencies: [NgClass, FileUploadModule, i2.FileUpload, i3.PrimeTemplate, i4.Button, SharedModule, NgIf, TableModule, i5.Table, ButtonModule, TooltipModule, i6.Tooltip],\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["EventEmitter", "FileUploadDetails", "moment", "HttpEventType", "FileUploadModule", "UserPermissionLevels", "TooltipModule", "ButtonModule", "TableModule", "SharedModule", "Ng<PERSON><PERSON>", "NgIf", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelement", "ɵɵelementEnd", "ɵɵlistener", "FileUploadComponent_ng_template_4_div_2_ng_template_3_p_button_12_Template_p_button_onClick_0_listener", "ɵɵrestoreView", "_r5", "file_r3", "ɵɵnextContext", "$implicit", "ctx_r3", "ɵɵresetView", "onRemoveFileClick", "ɵɵproperty", "FileUploadComponent_ng_template_4_div_2_ng_template_3_Template_p_button_onClick_11_listener", "_r2", "onDownloadFileClick", "ɵɵtemplate", "FileUploadComponent_ng_template_4_div_2_ng_template_3_p_button_12_Template", "ɵɵadvance", "ɵɵtextInterpolate", "name", "fileShareFileType", "uploadedDate", "uploadedByName", "userPermissionLevel", "ɵɵtextInterpolate1", "uploadedFiles", "length", "FileUploadComponent_ng_template_4_div_2_ng_template_2_Template", "FileUploadComponent_ng_template_4_div_2_ng_template_3_Template", "FileUploadComponent_ng_template_4_div_2_ng_template_4_Template", "tableStyle", "FileUploadComponent_ng_template_4_div_0_Template", "FileUploadComponent_ng_template_4_div_1_Template", "FileUploadComponent_ng_template_4_div_2_Template", "loadingFiles", "FileUploadComponent", "constructor", "messageSvc", "maximumFileSize", "multipleAllowed", "fileToAccept", "fileUploadContainerClass", "projectId", "versionId", "dataStoreName", "recordId", "dateFormat", "aliasIds", "isPreview", "EditAndDelete", "fileUploadEvent", "getFilesUploaded", "width", "ngOnInit", "getFilesFromServer", "ngOnChanges", "changes", "isFirstChange", "onFileSelectClick", "event", "currentFiles", "processSelectedFiles", "then", "res", "map", "file", "aliasId", "emit", "fileUploader", "clear", "files", "_this", "_asyncToGenerator", "fileUploadDetails", "saveFile", "Promise", "resolve", "fileService", "uploadFile", "subscribe", "fileInfo", "format", "toString", "push", "error", "console", "log", "_this2", "getFilesCall", "_ref", "getFileInfo", "apply", "arguments", "getFiles", "filesInfo", "for<PERSON>ach", "fileUpload", "statusText", "attchmentInfo", "getFileContent", "type", "Response", "targetFileElement", "document", "createElement", "setAttribute", "body", "append<PERSON><PERSON><PERSON>", "href", "URL", "createObjectURL", "download", "target", "click", "<PERSON><PERSON><PERSON><PERSON>", "status", "deleteFile", "fileIndex", "findIndex", "splice", "f", "showError", "ɵɵdirectiveInject", "i1", "NotificationService", "selectors", "viewQuery", "FileUploadComponent_Query", "rf", "ctx", "FileUploadComponent_Template_p_fileUpload_onSelect_1_listener", "$event", "_r1", "FileUploadComponent_ng_template_3_Template", "FileUploadComponent_ng_template_4_Template", "FileUploadComponent_ng_template_5_Template", "i2", "FileUpload", "i3", "PrimeTemplate", "i4", "<PERSON><PERSON>", "i5", "Table", "i6", "<PERSON><PERSON><PERSON>", "styles"], "sources": ["C:\\Projects\\SaltboxActionableGrid\\Saltbox-Data-App\\saltbox-data-app\\src\\app\\shared\\file-upload\\file-upload.component.ts", "C:\\Projects\\SaltboxActionableGrid\\Saltbox-Data-App\\saltbox-data-app\\src\\app\\shared\\file-upload\\file-upload.component.html"], "sourcesContent": ["import { Component, EventEmitter, Input, OnInit, Output, ViewChild, OnChanges, SimpleChanges } from '@angular/core';\r\nimport { FileInfo } from '../models/file-info';\r\nimport { FileUploadDetails } from '../../shared/models/file-upload-details';\r\nimport moment from 'moment/moment';\r\nimport { HttpEventType } from '@angular/common/http';\r\nimport { FileUpload, FileUploadModule } from 'primeng/fileupload';\r\nimport { NotificationService } from 'src/app/core/services/notification.service';\r\nimport { FileUploadService } from './services/file-upload.service';\r\nimport { UserPermissionLevels } from 'src/app/core/enums/shared';\r\nimport { TooltipModule } from 'primeng/tooltip';\r\nimport { ButtonModule } from 'primeng/button';\r\nimport { TableModule } from 'primeng/table';\r\nimport { SharedModule } from 'primeng/api';\r\nimport { NgClass, NgIf } from '@angular/common';\r\n\r\n@Component({\r\n    selector: 'app-file-upload',\r\n    templateUrl: './file-upload.component.html',\r\n    styleUrls: ['./file-upload.component.scss'],\r\n    standalone: true,\r\n    imports: [NgClass, FileUploadModule, SharedModule, NgIf, TableModule, ButtonModule, TooltipModule]\r\n})\r\nexport class FileUploadComponent implements OnInit, OnChanges {\r\n\r\n  @Input() uploadedFiles: FileInfo[] = [];\r\n  @Input() maximumFileSize = 5242880; // Maximum file size for each file\r\n  @Input() multipleAllowed = true; // Defines the number of uploads allowed to upload\r\n  @Input() fileToAccept = '.pdf,.xls,.xlsx,.doc,.csv,.docx,.txt,.ppt,.pptx,.jpeg,.jpg,.gif,.png'; // File Types to Accept\r\n  @Input() fileUploadContainerClass = 'default-fileUpload';  // Customized Default container DIV wrapper-class\r\n  @Input() projectId = '';\r\n  @Input() versionId = '';\r\n  @Input() dataStoreName = '';\r\n  @Input() recordId = '';\r\n  @Input() dateFormat = 'YYYY/MM/DD';\r\n  @Input() aliasIds: string[] = [];\r\n  @Input() fileService: FileUploadService;\r\n  @Input() isPreview = false;\r\n  @Input() userPermissionLevel: UserPermissionLevels = UserPermissionLevels.EditAndDelete;\r\n\r\n  @Output() fileUploadEvent: EventEmitter<any> = new EventEmitter();\r\n  @Output() getFilesUploaded: EventEmitter<FileInfo[]> = new EventEmitter();\r\n\r\n  @ViewChild('fileUploader') fileUploader: FileUpload;\r\n\r\n  loadingFiles = false;\r\n  tableStyle = { 'table-layout': 'auto', width: '100%' };\r\n\r\n  constructor(private messageSvc: NotificationService) { }\r\n\r\n  ngOnInit(): void {\r\n    if (this.aliasIds) {\r\n      this.getFilesFromServer();\r\n     }\r\n  }\r\n\r\n  ngOnChanges(changes: SimpleChanges): void {\r\n    if (changes.aliasIds && !changes.aliasIds?.isFirstChange()) {\r\n      this.getFilesFromServer();\r\n    }\r\n  }\r\n\r\n  onFileSelectClick(event): void {\r\n<<<<<<< Updated upstream\r\n=======\r\n    console.log('onFileSelectClick', event, this.fileUploader);\r\n    return;\r\n    if (this.isPreview) return;\r\n\r\n>>>>>>> Stashed changes\r\n    const currentFiles: File[] = event.currentFiles;\r\n    this.processSelectedFiles(currentFiles).then(res => {\r\n      this.aliasIds = this.uploadedFiles.map(file => {\r\n        return file.aliasId;\r\n      });\r\n\r\n      this.fileUploadEvent.emit(this.aliasIds);\r\n      this.fileUploader.clear();\r\n    });\r\n  }\r\n\r\n  async processSelectedFiles(files: File[]) {\r\n    for (const file of files) {\r\n      const fileUploadDetails: FileUploadDetails = new FileUploadDetails(this.projectId, this.versionId,\r\n        this.recordId, this.dataStoreName, file.name, file);\r\n      await this.saveFile(fileUploadDetails);\r\n    }\r\n  }\r\n\r\n  saveFile(fileUploadDetails: FileUploadDetails) {\r\n    return new Promise<void>(resolve => {\r\n      this.fileService.uploadFile(fileUploadDetails).subscribe(\r\n        (fileInfo: FileInfo) => {\r\n          fileInfo.uploadedDate = moment(fileInfo.uploadedDate, this.dateFormat, false)\r\n            .format(this.dateFormat).toString();\r\n          this.uploadedFiles.push(fileInfo);\r\n          resolve();\r\n        }, error => {\r\n          console.log(error);\r\n          resolve();\r\n        });\r\n    });\r\n  }\r\n\r\n  getFilesFromServer(): void {\r\n\r\n    this.loadingFiles = true;\r\n\r\n    const fileUploadDetails: FileUploadDetails = new FileUploadDetails(\r\n      this.projectId, this.versionId, this.recordId, this.dataStoreName);\r\n    fileUploadDetails.aliasIds = this.aliasIds;\r\n      // start rewriting some things here\r\n    const getFilesCall = async () => {\r\n      if (this.recordId) {\r\n        await this.getFileInfo(fileUploadDetails);\r\n      }\r\n    };\r\n\r\n    getFilesCall().then(res => {\r\n      this.loadingFiles = false;\r\n    });\r\n  }\r\n\r\n  getFileInfo(fileUploadDetails: FileUploadDetails) {\r\n    return new Promise<void>(resolve => {\r\n      this.fileService.getFiles(fileUploadDetails).subscribe(\r\n        (filesInfo: FileInfo[]) => {\r\n          filesInfo.forEach(fileUpload => {\r\n            fileUpload.uploadedDate = moment(fileUpload.uploadedDate, this.dateFormat, false)\r\n              .format(this.dateFormat).toString();\r\n          });\r\n          this.uploadedFiles = filesInfo;\r\n          resolve();\r\n        }, error => {\r\n          console.log(`File Request Error: ${error.statusText}...`);\r\n          resolve();\r\n        });\r\n    });\r\n  }\r\n\r\n  onDownloadFileClick(attchmentInfo: FileInfo) {\r\n    const fileUploadDetails: FileUploadDetails = new FileUploadDetails(this.projectId, this.versionId,\r\n      this.recordId, this.dataStoreName, attchmentInfo.name);\r\n    fileUploadDetails.aliasId = attchmentInfo.aliasId;\r\n\r\n    this.fileService.getFileContent(fileUploadDetails).subscribe(event => {\r\n      if (event.type === HttpEventType.Response) {\r\n\r\n        const targetFileElement = document.createElement('a');\r\n        targetFileElement.setAttribute('style', 'display:none;');\r\n        document.body.appendChild(targetFileElement);\r\n        targetFileElement.href = URL.createObjectURL(event.body);\r\n        targetFileElement.download = `${attchmentInfo.name}.${attchmentInfo.fileShareFileType}`;\r\n        targetFileElement.target = '_blank';\r\n        targetFileElement.click();\r\n        document.body.removeChild(targetFileElement);\r\n      }\r\n    }, error => {\r\n      console.log(`File Download Error: ${error.status}: Unable to complete request...`);\r\n    });\r\n  }\r\n\r\n  onRemoveFileClick(fileInfo: FileInfo) {\r\n    const fileUploadDetails: FileUploadDetails = new FileUploadDetails(this.projectId, this.versionId,\r\n      this.recordId, this.dataStoreName, fileInfo.name);\r\n    fileUploadDetails.aliasId = fileInfo.aliasId;\r\n\r\n    this.fileService.deleteFile(fileUploadDetails).subscribe((fileUpload: any) => {\r\n      const fileIndex = this.uploadedFiles.findIndex(file => file.aliasId === fileInfo.aliasId);\r\n\r\n      if (fileIndex >= 0) {\r\n        this.uploadedFiles.splice(fileIndex, 1);\r\n        this.fileUploadEvent.emit(this.uploadedFiles.map(f => f.aliasId));\r\n      }\r\n    }, error => {\r\n      this.messageSvc.showError('Error', `There was a problem deleting '${fileInfo.name}' from the server`);\r\n    });\r\n  }\r\n}\r\n", "<div [ngClass]=\"fileUploadContainerClass\">\r\n  <p-fileUpload\r\n    [disabled]=\"isPreview || userPermissionLevel === 'View'\"\r\n    name=\"fileUploader\"\r\n    mode=\"advanced\"\r\n    class=\"customFileUploader\"\r\n    #fileUploader\r\n    [multiple]=\"multipleAllowed\"\r\n    [maxFileSize]=\"maximumFileSize\"\r\n    [accept]=\"fileToAccept\"\r\n    (onSelect)=\"onFileSelectClick($event)\"\r\n    chooseLabel=\"Upload a File\"\r\n    chooseIcon=\"pi pi-upload\"\r\n    [showUploadButton]=\"false\"\r\n    [showCancelButton]=\"false\" >\r\n    <ng-template pTemplate=\"toolbar\">\r\n      <div class=\"fileUpload-toolbar-text\">You can upload files with the button or drag-and-drop.<br />\r\n      Once uploaded, make sure you click the Save button on the grid to attach the files.</div>\r\n    </ng-template>\r\n    <ng-template pTemplate=\"content\" >\r\n      <div *ngIf=\"!uploadedFiles.length && !loadingFiles\" class=\"fileUpload-content-template\">\r\n        Drag & Drop Files here</div>\r\n      <div *ngIf=\"loadingFiles && !uploadedFiles.length\" class=\"fileUpload-content-template\">Loading...</div>\r\n      <div *ngIf=\"uploadedFiles.length\" class=\"fileUpload-datatable\">\r\n        <p-table [value]=\"uploadedFiles\"\r\n                 styleClass=\"p-datatable-striped\"\r\n                 [tableStyle]=\"tableStyle\">\r\n          <ng-template pTemplate=\"header\">\r\n            <tr>\r\n              <th style=\"width: 40%;\">File</th>\r\n              <th style=\"width: 5%;\">Type</th>\r\n              <th style=\"width: 15%;\">Uploaded On</th>\r\n              <th style=\"width: 20%;\">Uploaded By</th>\r\n              <th style=\"width: 20%;\"></th>\r\n            </tr>\r\n          </ng-template>\r\n          <ng-template pTemplate=\"body\" let-file>\r\n            <tr>\r\n              <td>{{file.name}}</td>\r\n              <td>{{file.fileShareFileType}}</td>\r\n              <td>{{file.uploadedDate}}</td>\r\n              <td>{{file.uploadedByName}}</td>\r\n              <td>\r\n                <span class=\"flex\">\r\n                  <p-button icon=\"pi pi-download\" [outlined]=\"true\" (onClick)=\"onDownloadFileClick(file)\" pTooltip=\"Download\"></p-button>\r\n                  <p-button *ngIf=\"userPermissionLevel !== 'View'\" [text]=\"true\" severity=\"danger\" icon=\"pi pi-trash\" (onClick)=\"onRemoveFileClick(file)\" pTooltip=\"Delete\"></p-button>\r\n                </span>\r\n              </td>\r\n            </tr>\r\n          </ng-template>\r\n          <ng-template pTemplate=\"footer\">\r\n            <div class=\"template-footer-text\">Files: {{uploadedFiles.length}}</div>\r\n          </ng-template>\r\n        </p-table>\r\n      </div>\r\n    </ng-template>\r\n    <ng-template let-file pTemplate='file'></ng-template>\r\n  </p-fileUpload>\r\n</div>\r\n"], "mappings": ";AAAA,SAAoBA,YAAY,QAAoE,eAAe;AAEnH,SAASC,iBAAiB,QAAQ,yCAAyC;AAC3E,OAAOC,MAAM,MAAM,eAAe;AAClC,SAASC,aAAa,QAAQ,sBAAsB;AACpD,SAAqBC,gBAAgB,QAAQ,oBAAoB;AAGjE,SAASC,oBAAoB,QAAQ,2BAA2B;AAChE,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,WAAW,QAAQ,eAAe;AAC3C,SAASC,YAAY,QAAQ,aAAa;AAC1C,SAASC,OAAO,EAAEC,IAAI,QAAQ,iBAAiB;;;;;;;;;;;ICGzCC,EAAA,CAAAC,cAAA,aAAqC;IAAAD,EAAA,CAAAE,MAAA,6DAAsD;IAAAF,EAAA,CAAAG,SAAA,SAAM;IACjGH,EAAA,CAAAE,MAAA,2FAAmF;IAAAF,EAAA,CAAAI,YAAA,EAAM;;;;;IAGzFJ,EAAA,CAAAC,cAAA,aAAwF;IACtFD,EAAA,CAAAE,MAAA,8BAAsB;IAAAF,EAAA,CAAAI,YAAA,EAAM;;;;;IAC9BJ,EAAA,CAAAC,cAAA,aAAuF;IAAAD,EAAA,CAAAE,MAAA,iBAAU;IAAAF,EAAA,CAAAI,YAAA,EAAM;;;;;IAO/FJ,EADF,CAAAC,cAAA,SAAI,aACsB;IAAAD,EAAA,CAAAE,MAAA,WAAI;IAAAF,EAAA,CAAAI,YAAA,EAAK;IACjCJ,EAAA,CAAAC,cAAA,aAAuB;IAAAD,EAAA,CAAAE,MAAA,WAAI;IAAAF,EAAA,CAAAI,YAAA,EAAK;IAChCJ,EAAA,CAAAC,cAAA,aAAwB;IAAAD,EAAA,CAAAE,MAAA,kBAAW;IAAAF,EAAA,CAAAI,YAAA,EAAK;IACxCJ,EAAA,CAAAC,cAAA,aAAwB;IAAAD,EAAA,CAAAE,MAAA,kBAAW;IAAAF,EAAA,CAAAI,YAAA,EAAK;IACxCJ,EAAA,CAAAG,SAAA,aAA6B;IAC/BH,EAAA,CAAAI,YAAA,EAAK;;;;;;IAWCJ,EAAA,CAAAC,cAAA,mBAA0J;IAAtDD,EAAA,CAAAK,UAAA,qBAAAC,uGAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,OAAA,GAAAT,EAAA,CAAAU,aAAA,GAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAZ,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAa,WAAA,CAAWD,MAAA,CAAAE,iBAAA,CAAAL,OAAA,CAAuB;IAAA,EAAC;IAAmBT,EAAA,CAAAI,YAAA,EAAW;;;IAApHJ,EAAA,CAAAe,UAAA,cAAa;;;;;;IAPlEf,EADF,CAAAC,cAAA,SAAI,SACE;IAAAD,EAAA,CAAAE,MAAA,GAAa;IAAAF,EAAA,CAAAI,YAAA,EAAK;IACtBJ,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,GAA0B;IAAAF,EAAA,CAAAI,YAAA,EAAK;IACnCJ,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,GAAqB;IAAAF,EAAA,CAAAI,YAAA,EAAK;IAC9BJ,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,GAAuB;IAAAF,EAAA,CAAAI,YAAA,EAAK;IAG5BJ,EAFJ,CAAAC,cAAA,SAAI,gBACiB,oBAC2F;IAA1DD,EAAA,CAAAK,UAAA,qBAAAW,4FAAA;MAAA,MAAAP,OAAA,GAAAT,EAAA,CAAAO,aAAA,CAAAU,GAAA,EAAAN,SAAA;MAAA,MAAAC,MAAA,GAAAZ,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAa,WAAA,CAAWD,MAAA,CAAAM,mBAAA,CAAAT,OAAA,CAAyB;IAAA,EAAC;IAAqBT,EAAA,CAAAI,YAAA,EAAW;IACvHJ,EAAA,CAAAmB,UAAA,KAAAC,0EAAA,uBAA0J;IAGhKpB,EAFI,CAAAI,YAAA,EAAO,EACJ,EACF;;;;;IAVCJ,EAAA,CAAAqB,SAAA,GAAa;IAAbrB,EAAA,CAAAsB,iBAAA,CAAAb,OAAA,CAAAc,IAAA,CAAa;IACbvB,EAAA,CAAAqB,SAAA,GAA0B;IAA1BrB,EAAA,CAAAsB,iBAAA,CAAAb,OAAA,CAAAe,iBAAA,CAA0B;IAC1BxB,EAAA,CAAAqB,SAAA,GAAqB;IAArBrB,EAAA,CAAAsB,iBAAA,CAAAb,OAAA,CAAAgB,YAAA,CAAqB;IACrBzB,EAAA,CAAAqB,SAAA,GAAuB;IAAvBrB,EAAA,CAAAsB,iBAAA,CAAAb,OAAA,CAAAiB,cAAA,CAAuB;IAGS1B,EAAA,CAAAqB,SAAA,GAAiB;IAAjBrB,EAAA,CAAAe,UAAA,kBAAiB;IACtCf,EAAA,CAAAqB,SAAA,EAAoC;IAApCrB,EAAA,CAAAe,UAAA,SAAAH,MAAA,CAAAe,mBAAA,YAAoC;;;;;IAMrD3B,EAAA,CAAAC,cAAA,cAAkC;IAAAD,EAAA,CAAAE,MAAA,GAA+B;IAAAF,EAAA,CAAAI,YAAA,EAAM;;;;IAArCJ,EAAA,CAAAqB,SAAA,EAA+B;IAA/BrB,EAAA,CAAA4B,kBAAA,YAAAhB,MAAA,CAAAiB,aAAA,CAAAC,MAAA,KAA+B;;;;;IA3BrE9B,EADF,CAAAC,cAAA,cAA+D,kBAG1B;IAwBjCD,EAvBA,CAAAmB,UAAA,IAAAY,8DAAA,2BAAgC,IAAAC,8DAAA,2BASO,IAAAC,8DAAA,0BAcP;IAIpCjC,EADE,CAAAI,YAAA,EAAU,EACN;;;;IA9BKJ,EAAA,CAAAqB,SAAA,EAAuB;IAEvBrB,EAFA,CAAAe,UAAA,UAAAH,MAAA,CAAAiB,aAAA,CAAuB,eAAAjB,MAAA,CAAAsB,UAAA,CAEE;;;;;IAHpClC,EAHA,CAAAmB,UAAA,IAAAgB,gDAAA,iBAAwF,IAAAC,gDAAA,iBAED,IAAAC,gDAAA,iBACxB;;;;IAHzDrC,EAAA,CAAAe,UAAA,UAAAH,MAAA,CAAAiB,aAAA,CAAAC,MAAA,KAAAlB,MAAA,CAAA0B,YAAA,CAA4C;IAE5CtC,EAAA,CAAAqB,SAAA,EAA2C;IAA3CrB,EAAA,CAAAe,UAAA,SAAAH,MAAA,CAAA0B,YAAA,KAAA1B,MAAA,CAAAiB,aAAA,CAAAC,MAAA,CAA2C;IAC3C9B,EAAA,CAAAqB,SAAA,EAA0B;IAA1BrB,EAAA,CAAAe,UAAA,SAAAH,MAAA,CAAAiB,aAAA,CAAAC,MAAA,CAA0B;;;;ADDtC,OAAM,MAAOS,mBAAmB;EAyB9BC,YAAoBC,UAA+B;IAA/B,KAAAA,UAAU,GAAVA,UAAU;IAvBrB,KAAAZ,aAAa,GAAe,EAAE;IAC9B,KAAAa,eAAe,GAAG,OAAO,CAAC,CAAC;IAC3B,KAAAC,eAAe,GAAG,IAAI,CAAC,CAAC;IACxB,KAAAC,YAAY,GAAG,sEAAsE,CAAC,CAAC;IACvF,KAAAC,wBAAwB,GAAG,oBAAoB,CAAC,CAAE;IAClD,KAAAC,SAAS,GAAG,EAAE;IACd,KAAAC,SAAS,GAAG,EAAE;IACd,KAAAC,aAAa,GAAG,EAAE;IAClB,KAAAC,QAAQ,GAAG,EAAE;IACb,KAAAC,UAAU,GAAG,YAAY;IACzB,KAAAC,QAAQ,GAAa,EAAE;IAEvB,KAAAC,SAAS,GAAG,KAAK;IACjB,KAAAzB,mBAAmB,GAAyBlC,oBAAoB,CAAC4D,aAAa;IAE7E,KAAAC,eAAe,GAAsB,IAAIlE,YAAY,EAAE;IACvD,KAAAmE,gBAAgB,GAA6B,IAAInE,YAAY,EAAE;IAIzE,KAAAkD,YAAY,GAAG,KAAK;IACpB,KAAAJ,UAAU,GAAG;MAAE,cAAc,EAAE,MAAM;MAAEsB,KAAK,EAAE;IAAM,CAAE;EAEC;EAEvDC,QAAQA,CAAA;IACN,IAAI,IAAI,CAACN,QAAQ,EAAE;MACjB,IAAI,CAACO,kBAAkB,EAAE;IAC1B;EACH;EAEAC,WAAWA,CAACC,OAAsB;IAChC,IAAIA,OAAO,CAACT,QAAQ,IAAI,CAACS,OAAO,CAACT,QAAQ,EAAEU,aAAa,EAAE,EAAE;MAC1D,IAAI,CAACH,kBAAkB,EAAE;IAC3B;EACF;EAEAI,iBAAiBA,CAACC,KAAK;IAQrB,MAAMC,YAAY,GAAWD,KAAK,CAACC,YAAY;IAC/C,IAAI,CAACC,oBAAoB,CAACD,YAAY,CAAC,CAACE,IAAI,CAACC,GAAG,IAAG;MACjD,IAAI,CAAChB,QAAQ,GAAG,IAAI,CAACtB,aAAa,CAACuC,GAAG,CAACC,IAAI,IAAG;QAC5C,OAAOA,IAAI,CAACC,OAAO;MACrB,CAAC,CAAC;MAEF,IAAI,CAAChB,eAAe,CAACiB,IAAI,CAAC,IAAI,CAACpB,QAAQ,CAAC;MACxC,IAAI,CAACqB,YAAY,CAACC,KAAK,EAAE;IAC3B,CAAC,CAAC;EACJ;EAEMR,oBAAoBA,CAACS,KAAa;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACtC,KAAK,MAAMP,IAAI,IAAIK,KAAK,EAAE;QACxB,MAAMG,iBAAiB,GAAsB,IAAIxF,iBAAiB,CAACsF,KAAI,CAAC7B,SAAS,EAAE6B,KAAI,CAAC5B,SAAS,EAC/F4B,KAAI,CAAC1B,QAAQ,EAAE0B,KAAI,CAAC3B,aAAa,EAAEqB,IAAI,CAAC9C,IAAI,EAAE8C,IAAI,CAAC;QACrD,MAAMM,KAAI,CAACG,QAAQ,CAACD,iBAAiB,CAAC;MACxC;IAAC;EACH;EAEAC,QAAQA,CAACD,iBAAoC;IAC3C,OAAO,IAAIE,OAAO,CAAOC,OAAO,IAAG;MACjC,IAAI,CAACC,WAAW,CAACC,UAAU,CAACL,iBAAiB,CAAC,CAACM,SAAS,CACrDC,QAAkB,IAAI;QACrBA,QAAQ,CAAC3D,YAAY,GAAGnC,MAAM,CAAC8F,QAAQ,CAAC3D,YAAY,EAAE,IAAI,CAACyB,UAAU,EAAE,KAAK,CAAC,CAC1EmC,MAAM,CAAC,IAAI,CAACnC,UAAU,CAAC,CAACoC,QAAQ,EAAE;QACrC,IAAI,CAACzD,aAAa,CAAC0D,IAAI,CAACH,QAAQ,CAAC;QACjCJ,OAAO,EAAE;MACX,CAAC,EAAEQ,KAAK,IAAG;QACTC,OAAO,CAACC,GAAG,CAACF,KAAK,CAAC;QAClBR,OAAO,EAAE;MACX,CAAC,CAAC;IACN,CAAC,CAAC;EACJ;EAEAtB,kBAAkBA,CAAA;IAAA,IAAAiC,MAAA;IAEhB,IAAI,CAACrD,YAAY,GAAG,IAAI;IAExB,MAAMuC,iBAAiB,GAAsB,IAAIxF,iBAAiB,CAChE,IAAI,CAACyD,SAAS,EAAE,IAAI,CAACC,SAAS,EAAE,IAAI,CAACE,QAAQ,EAAE,IAAI,CAACD,aAAa,CAAC;IACpE6B,iBAAiB,CAAC1B,QAAQ,GAAG,IAAI,CAACA,QAAQ;IACxC;IACF,MAAMyC,YAAY;MAAA,IAAAC,IAAA,GAAAjB,iBAAA,CAAG,aAAW;QAC9B,IAAIe,MAAI,CAAC1C,QAAQ,EAAE;UACjB,MAAM0C,MAAI,CAACG,WAAW,CAACjB,iBAAiB,CAAC;QAC3C;MACF,CAAC;MAAA,gBAJKe,YAAYA,CAAA;QAAA,OAAAC,IAAA,CAAAE,KAAA,OAAAC,SAAA;MAAA;IAAA,GAIjB;IAEDJ,YAAY,EAAE,CAAC1B,IAAI,CAACC,GAAG,IAAG;MACxB,IAAI,CAAC7B,YAAY,GAAG,KAAK;IAC3B,CAAC,CAAC;EACJ;EAEAwD,WAAWA,CAACjB,iBAAoC;IAC9C,OAAO,IAAIE,OAAO,CAAOC,OAAO,IAAG;MACjC,IAAI,CAACC,WAAW,CAACgB,QAAQ,CAACpB,iBAAiB,CAAC,CAACM,SAAS,CACnDe,SAAqB,IAAI;QACxBA,SAAS,CAACC,OAAO,CAACC,UAAU,IAAG;UAC7BA,UAAU,CAAC3E,YAAY,GAAGnC,MAAM,CAAC8G,UAAU,CAAC3E,YAAY,EAAE,IAAI,CAACyB,UAAU,EAAE,KAAK,CAAC,CAC9EmC,MAAM,CAAC,IAAI,CAACnC,UAAU,CAAC,CAACoC,QAAQ,EAAE;QACvC,CAAC,CAAC;QACF,IAAI,CAACzD,aAAa,GAAGqE,SAAS;QAC9BlB,OAAO,EAAE;MACX,CAAC,EAAEQ,KAAK,IAAG;QACTC,OAAO,CAACC,GAAG,CAAC,uBAAuBF,KAAK,CAACa,UAAU,KAAK,CAAC;QACzDrB,OAAO,EAAE;MACX,CAAC,CAAC;IACN,CAAC,CAAC;EACJ;EAEA9D,mBAAmBA,CAACoF,aAAuB;IACzC,MAAMzB,iBAAiB,GAAsB,IAAIxF,iBAAiB,CAAC,IAAI,CAACyD,SAAS,EAAE,IAAI,CAACC,SAAS,EAC/F,IAAI,CAACE,QAAQ,EAAE,IAAI,CAACD,aAAa,EAAEsD,aAAa,CAAC/E,IAAI,CAAC;IACxDsD,iBAAiB,CAACP,OAAO,GAAGgC,aAAa,CAAChC,OAAO;IAEjD,IAAI,CAACW,WAAW,CAACsB,cAAc,CAAC1B,iBAAiB,CAAC,CAACM,SAAS,CAACpB,KAAK,IAAG;MACnE,IAAIA,KAAK,CAACyC,IAAI,KAAKjH,aAAa,CAACkH,QAAQ,EAAE;QAEzC,MAAMC,iBAAiB,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;QACrDF,iBAAiB,CAACG,YAAY,CAAC,OAAO,EAAE,eAAe,CAAC;QACxDF,QAAQ,CAACG,IAAI,CAACC,WAAW,CAACL,iBAAiB,CAAC;QAC5CA,iBAAiB,CAACM,IAAI,GAAGC,GAAG,CAACC,eAAe,CAACnD,KAAK,CAAC+C,IAAI,CAAC;QACxDJ,iBAAiB,CAACS,QAAQ,GAAG,GAAGb,aAAa,CAAC/E,IAAI,IAAI+E,aAAa,CAAC9E,iBAAiB,EAAE;QACvFkF,iBAAiB,CAACU,MAAM,GAAG,QAAQ;QACnCV,iBAAiB,CAACW,KAAK,EAAE;QACzBV,QAAQ,CAACG,IAAI,CAACQ,WAAW,CAACZ,iBAAiB,CAAC;MAC9C;IACF,CAAC,EAAElB,KAAK,IAAG;MACTC,OAAO,CAACC,GAAG,CAAC,wBAAwBF,KAAK,CAAC+B,MAAM,iCAAiC,CAAC;IACpF,CAAC,CAAC;EACJ;EAEAzG,iBAAiBA,CAACsE,QAAkB;IAClC,MAAMP,iBAAiB,GAAsB,IAAIxF,iBAAiB,CAAC,IAAI,CAACyD,SAAS,EAAE,IAAI,CAACC,SAAS,EAC/F,IAAI,CAACE,QAAQ,EAAE,IAAI,CAACD,aAAa,EAAEoC,QAAQ,CAAC7D,IAAI,CAAC;IACnDsD,iBAAiB,CAACP,OAAO,GAAGc,QAAQ,CAACd,OAAO;IAE5C,IAAI,CAACW,WAAW,CAACuC,UAAU,CAAC3C,iBAAiB,CAAC,CAACM,SAAS,CAAEiB,UAAe,IAAI;MAC3E,MAAMqB,SAAS,GAAG,IAAI,CAAC5F,aAAa,CAAC6F,SAAS,CAACrD,IAAI,IAAIA,IAAI,CAACC,OAAO,KAAKc,QAAQ,CAACd,OAAO,CAAC;MAEzF,IAAImD,SAAS,IAAI,CAAC,EAAE;QAClB,IAAI,CAAC5F,aAAa,CAAC8F,MAAM,CAACF,SAAS,EAAE,CAAC,CAAC;QACvC,IAAI,CAACnE,eAAe,CAACiB,IAAI,CAAC,IAAI,CAAC1C,aAAa,CAACuC,GAAG,CAACwD,CAAC,IAAIA,CAAC,CAACtD,OAAO,CAAC,CAAC;MACnE;IACF,CAAC,EAAEkB,KAAK,IAAG;MACT,IAAI,CAAC/C,UAAU,CAACoF,SAAS,CAAC,OAAO,EAAE,iCAAiCzC,QAAQ,CAAC7D,IAAI,mBAAmB,CAAC;IACvG,CAAC,CAAC;EACJ;;;uBA1JWgB,mBAAmB,EAAAvC,EAAA,CAAA8H,iBAAA,CAAAC,EAAA,CAAAC,mBAAA;IAAA;EAAA;;;YAAnBzF,mBAAmB;MAAA0F,SAAA;MAAAC,SAAA,WAAAC,0BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;UCrB9BpI,EADF,CAAAC,cAAA,aAA0C,yBAcV;UAJ5BD,EAAA,CAAAK,UAAA,sBAAAiI,8DAAAC,MAAA;YAAAvI,EAAA,CAAAO,aAAA,CAAAiI,GAAA;YAAA,OAAAxI,EAAA,CAAAa,WAAA,CAAYwH,GAAA,CAAAvE,iBAAA,CAAAyE,MAAA,CAAyB;UAAA,EAAC;UA8CtCvI,EAzCA,CAAAmB,UAAA,IAAAsH,0CAAA,yBAAiC,IAAAC,0CAAA,yBAIC,IAAAC,0CAAA,yBAqCK;UAE3C3I,EADE,CAAAI,YAAA,EAAe,EACX;;;UA1DDJ,EAAA,CAAAe,UAAA,YAAAsH,GAAA,CAAAxF,wBAAA,CAAoC;UAErC7C,EAAA,CAAAqB,SAAA,EAAwD;UAYxDrB,EAZA,CAAAe,UAAA,aAAAsH,GAAA,CAAAjF,SAAA,IAAAiF,GAAA,CAAA1G,mBAAA,YAAwD,aAAA0G,GAAA,CAAA1F,eAAA,CAK5B,gBAAA0F,GAAA,CAAA3F,eAAA,CACG,WAAA2F,GAAA,CAAAzF,YAAA,CACR,2BAIG,2BACA;;;qBDMhB9C,OAAO,EAAEN,gBAAgB,EAAAoJ,EAAA,CAAAC,UAAA,EAAAC,EAAA,CAAAC,aAAA,EAAAC,EAAA,CAAAC,MAAA,EAAEpJ,YAAY,EAAEE,IAAI,EAAEH,WAAW,EAAAsJ,EAAA,CAAAC,KAAA,EAAExJ,YAAY,EAAED,aAAa,EAAA0J,EAAA,CAAAC,OAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}