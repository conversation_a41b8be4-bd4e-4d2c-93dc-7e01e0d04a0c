import { Component, EventEmitter, Input, Output } from '@angular/core';
import { GridApi } from 'ag-grid-community';
import { ReportInfo } from 'src/app/core/models/report-info';
import { NotificationService } from 'src/app/core/services/notification.service';
import { ActionableGridService } from 'src/app/actionable-grid/services/actionable-grid.service';
import { EmailSetup } from 'src/app/core/models/email-setup';
import { AuthenticationService } from 'src/app/core/services/authentication.service';
import { EmailAttachmentType } from 'src/app/core/enums/shared';
import { ButtonModule } from 'primeng/button';
import { SharedModule } from 'primeng/api';
import { DropdownModule } from 'primeng/dropdown';
import { InputTextareaModule } from 'primeng/inputtextarea';
import { InputTextModule } from 'primeng/inputtext';
import { FormsModule } from '@angular/forms';
import { DialogModule } from 'primeng/dialog';
import { getPlatformTokenKey } from 'src/app/shared/utilities/helper.functions';

@Component({
    selector: 'app-email-grid',
    templateUrl: './email-grid.component.html',
    styleUrls: ['./email-grid.component.scss'],
    standalone: true,
    imports: [DialogModule, FormsModule, InputTextModule, InputTextareaModule, DropdownModule, SharedModule, ButtonModule]
})
export class EmailGridComponent {

  constructor(
    private actionableGridService: ActionableGridService,
    private messageSvc: NotificationService,
    private authenticationService: AuthenticationService) { }

  @Input() reportInfo: ReportInfo;
  @Input() gridApi: GridApi;
  @Output() displayChange = new EventEmitter();

  emailSetup: EmailSetup = new EmailSetup();
  attachmentTypes = Object.keys(EmailAttachmentType).map(key => ({ label: key, value: EmailAttachmentType[key] }));
  selectedAttachmentType = EmailAttachmentType.Excel;

  onCancel(): void {
    this.displayChange.emit(false);
  }

  async sendEmail() {
    this.emailSetup.fromAddress = this.authenticationService.decodeTokenIfNotExpired(getPlatformTokenKey())?.email;
    if (this.checkValues()) {
      this.emailSetup.attachmentType = EmailAttachmentType[this.selectedAttachmentType];
      this.emailSetup.attachmentName = this.reportInfo.reportName.concat(this.selectedAttachmentType);
      this.emailSetup.attachment = EmailAttachmentType.Excel ? await this.exportAsExcelFile() : this.exportAsCsvFile();

      this.actionableGridService.sendEmail(this.reportInfo.reportId, this.reportInfo.emailLimit, this.emailSetup).subscribe({
        next: async (response: string) => {
          if (response === 'true') {
            this.messageSvc.showSuccess('Email sent successfully.');
          }
          else {
            this.messageSvc.showError('Unable to send the email.');
          }
          this.onCancel();
        },
        error: err => {
          this.messageSvc.showError(err.message);
          this.onCancel();
        }
      });
    }
  }

  checkValues(): boolean {
    if (!this.emailSetup.fromAddress) {
      this.messageSvc.showWarning('error', 'Could not find logged in user email address');
      return false;
    }
    if (!this.validateEmailList()) {
      this.messageSvc.showWarning('error', 'Please enter valid email(s) to send');
      return false;
    }
    if (!this.emailSetup.message) {
      this.emailSetup.message = '';
    }
    return true;
  }

  async exportAsExcelFile(): Promise<string> {
    const result = this.gridApi.getDataAsExcel({
      processCellCallback: (params) => {
        return params.formatValue(params.value);
      },
      processRowGroupCallback: (params) => {
        return params.node.key;
      },
      columnKeys: this.actionableGridService.getGridDisplayedColumns(this.gridApi), 
      fileName: this.reportInfo.reportName, sheetName: this.reportInfo.reportName, prependContent: []
    });

    return await this.blobToBase64(result);
  }

  exportAsCsvFile() {
    const result = this.gridApi.getDataAsCsv({
      processCellCallback: (params) => {
        return params.formatValue(params.value);
      },
      processRowGroupCallback: (params) => {
        return params.node.key;
      },
      columnKeys: this.actionableGridService.getGridDisplayedColumns(this.gridApi), 
      fileName: this.reportInfo.reportName
    });

    return btoa(result);
  }

  validateEmailList(): boolean {
    const emails = this.emailSetup.toAddress?.split(',');
    return emails?.length ? emails.every(email => this.validateEmail(email)) : false;
  }

  validateEmail(email: string): boolean {
    const regularExpression = /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
    return regularExpression.test(email.toLowerCase().trim());
  }

  async blobToBase64(blob): Promise<string> {
    return new Promise((resolve, _) => {
      const reader = new FileReader();
      reader.onloadend = () => resolve((reader.result as string).split(',')[1]);
      reader.readAsDataURL(blob);
    });
  }

}
