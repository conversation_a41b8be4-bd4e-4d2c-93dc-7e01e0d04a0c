{"ast": null, "code": "import { ActionableGridIconClass } from '../../enums/actionable-grid';\nimport { CustomCellRenderer } from './ag-grid-custom-cell-renderer';\nimport { SharedModule } from 'primeng/api';\nimport { FormsModule } from '@angular/forms';\nimport { DropdownModule } from 'primeng/dropdown';\nimport { NgClass } from '@angular/common';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/actionable-grid/services/change-history.service\";\nimport * as i2 from \"../../services/notification.service\";\nimport * as i3 from \"../../services/ag-grid.service\";\nimport * as i4 from \"primeng/dropdown\";\nimport * as i5 from \"primeng/api\";\nimport * as i6 from \"@angular/forms\";\nconst _c0 = [\"targetElement\"];\nconst _c1 = a0 => ({\n  \"hide-dropdown-arrow\": a0\n});\nfunction AgGridMessageSymbolRendererComponent_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    let tmp_1_0;\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (tmp_1_0 = ctx_r0.cellRendererParams == null ? null : ctx_r0.cellRendererParams.valueFormatted) !== null && tmp_1_0 !== undefined ? tmp_1_0 : ctx_r0.cellRendererParams == null ? null : ctx_r0.cellRendererParams.value, \" \");\n  }\n}\nfunction AgGridMessageSymbolRendererComponent_Conditional_1_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\");\n  }\n  if (rf & 2) {\n    const item_r3 = ctx.$implicit;\n    i0.ɵɵclassMap(item_r3.label);\n  }\n}\nfunction AgGridMessageSymbolRendererComponent_Conditional_1_ng_template_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\");\n  }\n  if (rf & 2) {\n    const object_r4 = ctx.$implicit;\n    i0.ɵɵclassMap(object_r4.label);\n  }\n}\nfunction AgGridMessageSymbolRendererComponent_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 1)(1, \"p-dropdown\", 2, 0);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function AgGridMessageSymbolRendererComponent_Conditional_1_Template_p_dropdown_ngModelChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r0.icon, $event) || (ctx_r0.icon = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"onChange\", function AgGridMessageSymbolRendererComponent_Conditional_1_Template_p_dropdown_onChange_1_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.onChangeData());\n    });\n    i0.ɵɵtemplate(3, AgGridMessageSymbolRendererComponent_Conditional_1_ng_template_3_Template, 1, 3, \"ng-template\", 3)(4, AgGridMessageSymbolRendererComponent_Conditional_1_ng_template_4_Template, 1, 3, \"ng-template\", 4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(5, _c1, !ctx_r0.cellRendererParams.allowUserUpdate));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"options\", ctx_r0.symbolArray);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r0.icon);\n    i0.ɵɵproperty(\"disabled\", !ctx_r0.cellRendererParams.allowUserUpdate)(\"required\", ctx_r0.required);\n  }\n}\n/* This renderer replaces the message symbol (fail, retry, etc.) with icons for display in the grid. */\nexport class AgGridMessageSymbolRendererComponent extends CustomCellRenderer {\n  constructor(changeHistoryService, notificationService, aGGridService) {\n    super(changeHistoryService, notificationService, aGGridService);\n    this.actionableGridIconClass = ActionableGridIconClass;\n    this.symbolArray = [];\n  }\n  agInit(cellRendererParams) {\n    super.agInit(cellRendererParams);\n    for (const allowedValue of this.cellRendererParams.allowedValues) {\n      this.symbolArray.push({\n        label: allowedValue.key,\n        value: allowedValue.key\n      });\n    }\n    const matchResult = this.cellRendererParams.value?.toString().toLowerCase();\n    const resultIndex = this.cellRendererParams.allowedValues.findIndex(x => x.value.toString().toLowerCase() === matchResult?.toString().toLowerCase());\n    this.icon = resultIndex > -1 ? this.cellRendererParams.allowedValues[resultIndex].key.toString().toLowerCase() : this.cellRendererParams.allowedValues.find(x => x.isDefault === true).key;\n  }\n  focusElement() {\n    this.elementRef.focus();\n  }\n  onChangeData() {\n    const newValue = this.cellRendererParams.allowedValues.find(x => x.key.toString().toLowerCase() === this.icon).value;\n    if (newValue !== this.cellRendererParams.value) {\n      this.cellRendererParams.value = newValue;\n      super.setData();\n    }\n  }\n  static {\n    this.ɵfac = function AgGridMessageSymbolRendererComponent_Factory(t) {\n      return new (t || AgGridMessageSymbolRendererComponent)(i0.ɵɵdirectiveInject(i1.ChangeHistoryService), i0.ɵɵdirectiveInject(i2.NotificationService), i0.ɵɵdirectiveInject(i3.AGGridService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AgGridMessageSymbolRendererComponent,\n      selectors: [[\"app-message-symbol-renderer\"]],\n      viewQuery: function AgGridMessageSymbolRendererComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.elementRef = _t.first);\n        }\n      },\n      standalone: true,\n      features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n      decls: 2,\n      vars: 2,\n      consts: [[\"targetElement\", \"\"], [3, \"ngClass\"], [\"appendTo\", \"body\", 3, \"ngModelChange\", \"onChange\", \"options\", \"ngModel\", \"disabled\", \"required\"], [\"pTemplate\", \"selectedItem\"], [\"pTemplate\", \"item\"]],\n      template: function AgGridMessageSymbolRendererComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, AgGridMessageSymbolRendererComponent_Conditional_0_Template, 2, 1, \"div\")(1, AgGridMessageSymbolRendererComponent_Conditional_1_Template, 5, 7, \"div\", 1);\n        }\n        if (rf & 2) {\n          i0.ɵɵconditional(0, !ctx.showEditor && (ctx.cellRendererParams == null ? null : ctx.cellRendererParams.node == null ? null : ctx.cellRendererParams.node.group) ? 0 : -1);\n          i0.ɵɵadvance();\n          i0.ɵɵconditional(1, ctx.showEditor ? 1 : -1);\n        }\n      },\n      dependencies: [NgClass, DropdownModule, i4.Dropdown, i5.PrimeTemplate, FormsModule, i6.NgControlStatus, i6.RequiredValidator, i6.NgModel, SharedModule],\n      encapsulation: 2\n    });\n  }\n}", "map": {"version": 3, "names": ["ActionableGridIconClass", "CustomCellRenderer", "SharedModule", "FormsModule", "DropdownModule", "Ng<PERSON><PERSON>", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "tmp_1_0", "ctx_r0", "cellRendererParams", "valueFormatted", "undefined", "value", "ɵɵelement", "ɵɵclassMap", "item_r3", "label", "object_r4", "ɵɵtwoWayListener", "AgGridMessageSymbolRendererComponent_Conditional_1_Template_p_dropdown_ngModelChange_1_listener", "$event", "ɵɵrestoreView", "_r2", "ɵɵnextContext", "ɵɵtwoWayBindingSet", "icon", "ɵɵresetView", "ɵɵlistener", "AgGridMessageSymbolRendererComponent_Conditional_1_Template_p_dropdown_onChange_1_listener", "onChangeData", "ɵɵtemplate", "AgGridMessageSymbolRendererComponent_Conditional_1_ng_template_3_Template", "AgGridMessageSymbolRendererComponent_Conditional_1_ng_template_4_Template", "ɵɵproperty", "ɵɵpureFunction1", "_c1", "allowUserUpdate", "symbolArray", "ɵɵtwoWayProperty", "required", "AgGridMessageSymbolRendererComponent", "constructor", "changeHistoryService", "notificationService", "aGGridService", "actionableGridIconClass", "agInit", "allowedValue", "<PERSON><PERSON><PERSON><PERSON>", "push", "key", "matchResult", "toString", "toLowerCase", "resultIndex", "findIndex", "x", "find", "isDefault", "focusElement", "elementRef", "focus", "newValue", "setData", "ɵɵdirectiveInject", "i1", "ChangeHistoryService", "i2", "NotificationService", "i3", "AGGridService", "selectors", "viewQuery", "AgGridMessageSymbolRendererComponent_Query", "rf", "ctx", "AgGridMessageSymbolRendererComponent_Conditional_0_Template", "AgGridMessageSymbolRendererComponent_Conditional_1_Template", "ɵɵconditional", "showEditor", "node", "group", "i4", "Dropdown", "i5", "PrimeTemplate", "i6", "NgControlStatus", "RequiredValidator", "NgModel", "encapsulation"], "sources": ["C:\\Projects\\SaltboxActionableGrid\\Saltbox-Data-App\\saltbox-data-app\\src\\app\\core\\ag-grid\\renderers\\ag-grid-symbol-renderer.component.ts", "C:\\Projects\\SaltboxActionableGrid\\Saltbox-Data-App\\saltbox-data-app\\src\\app\\core\\ag-grid\\renderers\\ag-grid-symbol-renderer.component.html"], "sourcesContent": ["import { Component, ViewChild } from '@angular/core';\r\nimport { ActionableGridIconClass } from '../../enums/actionable-grid';\r\nimport { ICustomCellRendererParams } from './ag-grid-custom-cell-renderer-params';\r\nimport { CustomCellRenderer } from './ag-grid-custom-cell-renderer';\r\nimport { NotificationService } from '../../services/notification.service';\r\nimport { ChangeHistoryService } from 'src/app/actionable-grid/services/change-history.service';\r\nimport { AGGridService } from '../../services/ag-grid.service';\r\nimport { SharedModule } from 'primeng/api';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { DropdownModule } from 'primeng/dropdown';\r\nimport { NgClass } from '@angular/common';\r\n\r\n/* This renderer replaces the message symbol (fail, retry, etc.) with icons for display in the grid. */\r\n@Component({\r\n    selector: 'app-message-symbol-renderer',\r\n    templateUrl: './ag-grid-symbol-renderer.component.html',\r\n    standalone: true,\r\n    imports: [NgClass, DropdownModule, FormsModule, SharedModule]\r\n})\r\n\r\nexport class AgGridMessageSymbolRendererComponent extends CustomCellRenderer {\r\n  @ViewChild('targetElement') elementRef;\r\n  constructor(\r\n    changeHistoryService: ChangeHistoryService,\r\n    notificationService: NotificationService,\r\n    aGGridService: AGGridService) {\r\n    super(changeHistoryService, notificationService, aGGridService);\r\n  }\r\n\r\n  icon: string;\r\n  actionableGridIconClass = ActionableGridIconClass;\r\n  symbolArray: { label: string, value: string }[] = [];\r\n\r\n  agInit(cellRendererParams: ICustomCellRendererParams): void {\r\n    super.agInit(cellRendererParams);\r\n\r\n    for (const allowedValue of this.cellRendererParams.allowedValues) {\r\n      this.symbolArray.push({ label: allowedValue.key, value: allowedValue.key });\r\n    }\r\n\r\n    const matchResult = this.cellRendererParams.value?.toString().toLowerCase();\r\n\r\n    const resultIndex = this.cellRendererParams.allowedValues.findIndex(x => x.value.toString().toLowerCase()\r\n      === matchResult?.toString().toLowerCase());\r\n\r\n    this.icon = (resultIndex > -1)\r\n      ? this.cellRendererParams.allowedValues[resultIndex].key.toString().toLowerCase()\r\n      : this.cellRendererParams.allowedValues.find(x => x.isDefault === true).key;\r\n  }\r\n\r\n  focusElement(): void {\r\n    this.elementRef.focus();\r\n  }\r\n\r\n  onChangeData() {\r\n    const newValue = this.cellRendererParams.allowedValues.find(x => x.key.toString().toLowerCase() === this.icon).value;\r\n    if (newValue !== this.cellRendererParams.value) {\r\n      this.cellRendererParams.value = newValue;\r\n      super.setData();\r\n    }\r\n  }\r\n}\r\n", "@if (!showEditor && cellRendererParams?.node?.group) {\r\n  <div>\r\n  {{ cellRendererParams?.valueFormatted ?? cellRendererParams?.value }}\r\n  </div>\r\n}\r\n@if (showEditor) {\r\n  <div [ngClass]=\"{'hide-dropdown-arrow' : !cellRendererParams.allowUserUpdate}\">\r\n    <p-dropdown #targetElement [options]=\"symbolArray\" appendTo=\"body\" [(ngModel)]=\"icon\"\r\n      [disabled]=\"!cellRendererParams.allowUserUpdate\" [required]=\"required\" (onChange)=onChangeData()>\r\n      <ng-template let-item pTemplate=\"selectedItem\"> <i class=\"{{item.label}}\"></i> </ng-template>\r\n      <ng-template let-object pTemplate=\"item\"> <i class=\"{{object.label}}\"></i> </ng-template> </p-dropdown>\r\n  </div>\r\n}"], "mappings": "AACA,SAASA,uBAAuB,QAAQ,6BAA6B;AAErE,SAASC,kBAAkB,QAAQ,gCAAgC;AAInE,SAASC,YAAY,QAAQ,aAAa;AAC1C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,OAAO,QAAQ,iBAAiB;;;;;;;;;;;;;;ICTvCC,EAAA,CAAAC,cAAA,UAAK;IACLD,EAAA,CAAAE,MAAA,GACA;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IADNH,EAAA,CAAAI,SAAA,EACA;IADAJ,EAAA,CAAAK,kBAAA,OAAAC,OAAA,GAAAC,MAAA,CAAAC,kBAAA,kBAAAD,MAAA,CAAAC,kBAAA,CAAAC,cAAA,cAAAH,OAAA,KAAAI,SAAA,GAAAJ,OAAA,GAAAC,MAAA,CAAAC,kBAAA,kBAAAD,MAAA,CAAAC,kBAAA,CAAAG,KAAA,MACA;;;;;IAMoDX,EAAA,CAAAY,SAAA,QAA8B;;;;IAA3BZ,EAAA,CAAAa,UAAA,CAAAC,OAAA,CAAAC,KAAA,CAAsB;;;;;IAC/Bf,EAAA,CAAAY,SAAA,QAAgC;;;;IAA7BZ,EAAA,CAAAa,UAAA,CAAAG,SAAA,CAAAD,KAAA,CAAwB;;;;;;IAHvEf,EADF,CAAAC,cAAA,aAA+E,uBAEsB;IADhCD,EAAA,CAAAiB,gBAAA,2BAAAC,gGAAAC,MAAA;MAAAnB,EAAA,CAAAoB,aAAA,CAAAC,GAAA;MAAA,MAAAd,MAAA,GAAAP,EAAA,CAAAsB,aAAA;MAAAtB,EAAA,CAAAuB,kBAAA,CAAAhB,MAAA,CAAAiB,IAAA,EAAAL,MAAA,MAAAZ,MAAA,CAAAiB,IAAA,GAAAL,MAAA;MAAA,OAAAnB,EAAA,CAAAyB,WAAA,CAAAN,MAAA;IAAA,EAAkB;IACZnB,EAAA,CAAA0B,UAAA,sBAAAC,2FAAA;MAAA3B,EAAA,CAAAoB,aAAA,CAAAC,GAAA;MAAA,MAAAd,MAAA,GAAAP,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAyB,WAAA,CAAWlB,MAAA,CAAAqB,YAAA,EAAc;IAAA;IAEhG5B,EADA,CAAA6B,UAAA,IAAAC,yEAAA,yBAA+C,IAAAC,yEAAA,yBACN;IAC7C/B,EAD8F,CAAAG,YAAA,EAAa,EACrG;;;;IALDH,EAAA,CAAAgC,UAAA,YAAAhC,EAAA,CAAAiC,eAAA,IAAAC,GAAA,GAAA3B,MAAA,CAAAC,kBAAA,CAAA2B,eAAA,EAAyE;IACjDnC,EAAA,CAAAI,SAAA,EAAuB;IAAvBJ,EAAA,CAAAgC,UAAA,YAAAzB,MAAA,CAAA6B,WAAA,CAAuB;IAAiBpC,EAAA,CAAAqC,gBAAA,YAAA9B,MAAA,CAAAiB,IAAA,CAAkB;IAClCxB,EAAjD,CAAAgC,UAAA,cAAAzB,MAAA,CAAAC,kBAAA,CAAA2B,eAAA,CAAgD,aAAA5B,MAAA,CAAA+B,QAAA,CAAsB;;;ADI5E;AAQA,OAAM,MAAOC,oCAAqC,SAAQ5C,kBAAkB;EAE1E6C,YACEC,oBAA0C,EAC1CC,mBAAwC,EACxCC,aAA4B;IAC5B,KAAK,CAACF,oBAAoB,EAAEC,mBAAmB,EAAEC,aAAa,CAAC;IAIjE,KAAAC,uBAAuB,GAAGlD,uBAAuB;IACjD,KAAA0C,WAAW,GAAuC,EAAE;EAJpD;EAMAS,MAAMA,CAACrC,kBAA6C;IAClD,KAAK,CAACqC,MAAM,CAACrC,kBAAkB,CAAC;IAEhC,KAAK,MAAMsC,YAAY,IAAI,IAAI,CAACtC,kBAAkB,CAACuC,aAAa,EAAE;MAChE,IAAI,CAACX,WAAW,CAACY,IAAI,CAAC;QAAEjC,KAAK,EAAE+B,YAAY,CAACG,GAAG;QAAEtC,KAAK,EAAEmC,YAAY,CAACG;MAAG,CAAE,CAAC;IAC7E;IAEA,MAAMC,WAAW,GAAG,IAAI,CAAC1C,kBAAkB,CAACG,KAAK,EAAEwC,QAAQ,EAAE,CAACC,WAAW,EAAE;IAE3E,MAAMC,WAAW,GAAG,IAAI,CAAC7C,kBAAkB,CAACuC,aAAa,CAACO,SAAS,CAACC,CAAC,IAAIA,CAAC,CAAC5C,KAAK,CAACwC,QAAQ,EAAE,CAACC,WAAW,EAAE,KACnGF,WAAW,EAAEC,QAAQ,EAAE,CAACC,WAAW,EAAE,CAAC;IAE5C,IAAI,CAAC5B,IAAI,GAAI6B,WAAW,GAAG,CAAC,CAAC,GACzB,IAAI,CAAC7C,kBAAkB,CAACuC,aAAa,CAACM,WAAW,CAAC,CAACJ,GAAG,CAACE,QAAQ,EAAE,CAACC,WAAW,EAAE,GAC/E,IAAI,CAAC5C,kBAAkB,CAACuC,aAAa,CAACS,IAAI,CAACD,CAAC,IAAIA,CAAC,CAACE,SAAS,KAAK,IAAI,CAAC,CAACR,GAAG;EAC/E;EAEAS,YAAYA,CAAA;IACV,IAAI,CAACC,UAAU,CAACC,KAAK,EAAE;EACzB;EAEAhC,YAAYA,CAAA;IACV,MAAMiC,QAAQ,GAAG,IAAI,CAACrD,kBAAkB,CAACuC,aAAa,CAACS,IAAI,CAACD,CAAC,IAAIA,CAAC,CAACN,GAAG,CAACE,QAAQ,EAAE,CAACC,WAAW,EAAE,KAAK,IAAI,CAAC5B,IAAI,CAAC,CAACb,KAAK;IACpH,IAAIkD,QAAQ,KAAK,IAAI,CAACrD,kBAAkB,CAACG,KAAK,EAAE;MAC9C,IAAI,CAACH,kBAAkB,CAACG,KAAK,GAAGkD,QAAQ;MACxC,KAAK,CAACC,OAAO,EAAE;IACjB;EACF;;;uBAxCWvB,oCAAoC,EAAAvC,EAAA,CAAA+D,iBAAA,CAAAC,EAAA,CAAAC,oBAAA,GAAAjE,EAAA,CAAA+D,iBAAA,CAAAG,EAAA,CAAAC,mBAAA,GAAAnE,EAAA,CAAA+D,iBAAA,CAAAK,EAAA,CAAAC,aAAA;IAAA;EAAA;;;YAApC9B,oCAAoC;MAAA+B,SAAA;MAAAC,SAAA,WAAAC,2CAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;;;UCfjDzE,EALA,CAAA6B,UAAA,IAAA8C,2DAAA,cAAsD,IAAAC,2DAAA,iBAKpC;;;UALlB5E,EAAA,CAAA6E,aAAA,KAAAH,GAAA,CAAAI,UAAA,KAAAJ,GAAA,CAAAlE,kBAAA,kBAAAkE,GAAA,CAAAlE,kBAAA,CAAAuE,IAAA,kBAAAL,GAAA,CAAAlE,kBAAA,CAAAuE,IAAA,CAAAC,KAAA,WAIC;UACDhF,EAAA,CAAAI,SAAA,EAOC;UAPDJ,EAAA,CAAA6E,aAAA,IAAAH,GAAA,CAAAI,UAAA,UAOC;;;qBDKa/E,OAAO,EAAED,cAAc,EAAAmF,EAAA,CAAAC,QAAA,EAAAC,EAAA,CAAAC,aAAA,EAAEvF,WAAW,EAAAwF,EAAA,CAAAC,eAAA,EAAAD,EAAA,CAAAE,iBAAA,EAAAF,EAAA,CAAAG,OAAA,EAAE5F,YAAY;MAAA6F,aAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}