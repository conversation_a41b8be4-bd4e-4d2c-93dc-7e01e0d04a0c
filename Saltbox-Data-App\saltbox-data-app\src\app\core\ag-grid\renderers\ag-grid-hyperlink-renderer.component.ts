import { Component } from '@angular/core';
import { CustomCellRenderer } from './ag-grid-custom-cell-renderer';
import { ICustomCellRendererParams } from './ag-grid-custom-cell-renderer-params';
import { NotificationService } from '../../services/notification.service';
import { getDateByISO8601Format } from 'src/app/shared/utilities/date.functions';
import { formatCurrency, formatDate } from 'src/app/shared/utilities/format.functions';
import { JsonStringFormats } from 'src/app/shared/enums/json-schema.enum';
import { ChangeHistoryService } from 'src/app/actionable-grid/services/change-history.service';
import { AGGridService } from '../../services/ag-grid.service';

@Component({
    selector: 'app-hyperlink-renderer',
    templateUrl: './ag-grid-hyperlink-renderer.component.html',
    standalone: true,
})

export class AgGridHyperlinkRendererComponent extends CustomCellRenderer {
    public urlString: string;
    public value: string;

    constructor(
        changeHistoryService: ChangeHistoryService,
        notificationService: NotificationService,
        aGGridService: AGGridService) {
        super(changeHistoryService, notificationService, aGGridService);
    }

    refresh(cellRendererParams: ICustomCellRendererParams): boolean {
        this.setHyperlinkValues(cellRendererParams);
        return true;
    }

    agInit(cellRendererParams: ICustomCellRendererParams): void {
        super.agInit(cellRendererParams);
        this.setHyperlinkValues(cellRendererParams);
    }

    private setHyperlinkValues(cellRendererParams: ICustomCellRendererParams) {
        this.value = cellRendererParams.value;
        if (cellRendererParams.format.baseFormat === JsonStringFormats.DateTime) {
            this.value = formatDate(getDateByISO8601Format(cellRendererParams.value)?.toString());
        }
        else if (cellRendererParams.format.currency && cellRendererParams.format.decimalPlaces) {
            this.value = formatCurrency(
                cellRendererParams.value, cellRendererParams.format.currency, cellRendererParams.format.decimalPlaces);
        }

        this.setUrl(cellRendererParams);
    }

    private setUrl(cellRendererParams: ICustomCellRendererParams) {
        if (cellRendererParams.node.group)
            return;

        this.urlString = cellRendererParams.link.urlAlias;
        cellRendererParams.link.urlSubstitutions?.forEach((param, index) => {
            const placeholder = `{${index}}`;
            const value = param.isProjectVariable
                ? cellRendererParams.projectVariables?.find(v => v.variable === param.urlName)?.configValue
                : cellRendererParams.data[param.urlName];
            this.urlString = this.urlString.replace(placeholder, `${value ?? ''}`);
        });
    }
}
