<p-dialog id=header name="header" header="Email Grid" [visible]="true" [modal]="true" (visibleChange)="onCancel()">
    <div class="card">
        <div class="p-fluid grid field">
            <div class="col-12 p-float-label">
                <input id="toMail" type="text" pInputText [(ngModel)]="emailSetup.toAddress" />
                <label for="toMail">To</label>
                <small id="toMail-help">Enter multiple email address using commas(',').</small>
            </div>
            <div class="col-12 p-float-label">
                <input id="subject" type="text" pInputText [(ngModel)]="emailSetup.subject" />
                <label for="subject">Subject</label>
            </div>
            <div class="col-12 p-float-label">
                <textarea id="message" type="text" class="col-12" rows="4" pInputText [(ngModel)]="emailSetup.message"
                    pInputTextarea></textarea>
                <label for="message">Message</label>
            </div>
            <div class="col-12">
                <label for="attachment">Attachment</label>
                <div class="p-text-left p-text-lowercase">{{reportInfo?.reportName}}{{selectedAttachmentType}}</div>
            </div>
            <div class="col-12 p-float-label">
                <p-dropdown inputId="attatchmentType" appendTo="body" [options]="attachmentTypes"
                    [(ngModel)]="selectedAttachmentType" placeholder="Select Attachment Type" optionLabel="label"
                    optionValue="value">
                </p-dropdown>
                <label for="attatchmentType">Attachment Type</label>
            </div>
        </div>
    </div>
    <ng-template pTemplate="footer">
        <p-button type="button" icon="pi pi-send" label="Send" (onClick)="sendEmail()"></p-button>
        <p-button type="button" icon="pi pi-ban" [outlined]="true" severity="secondary" label="Cancel"
            (onClick)="onCancel()"></p-button>
    </ng-template>
</p-dialog>