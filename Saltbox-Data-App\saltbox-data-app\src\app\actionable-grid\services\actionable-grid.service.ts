import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { catchError, tap } from 'rxjs/operators';
import { BehaviorSubject, Observable, Subject, firstValueFrom, throwError } from 'rxjs';
import { GridApi } from 'ag-grid-community';
import { EnvService } from '../../core/services/env.service';
import { ReportInfo } from '../../core/models/report-info';
import { ActionableGridColumnConfig } from '../../core/models/actionable-grid-column-config';
import { EmailSetup } from '../../core/models/email-setup';
import { ExtendedJsonSchema } from 'src/app/shared/models/extended-json-schema';
import { JsonDataTypes } from 'src/app/shared/enums/json-schema.enum';
import { UserService } from 'src/app/core/services/user.service';
import { convertDefaultValue } from 'src/app/shared/utilities/datatype.functions';
import { ReportListModel } from 'src/app/core/models/report-list-model';
import { SimpleDatastoreStructure } from 'src/app/core/models/simple-datastore-structure';

@Injectable({
  providedIn: 'root'
})

export class ActionableGridService {
  header = new HttpHeaders().set('content-type', 'application/json');
  reportingGatewayServiceApiUrl: string = this.envService.gatewayServiceApiUrl;

  public onActionableGridInfoLoaded: Subject<ReportInfo> = new Subject<ReportInfo>();
  public onActionableGridDataLoaded: Subject<any> = new Subject<any>();
  getReportData$ = this.onActionableGridDataLoaded.asObservable();

  public lockRowsSubject: BehaviorSubject<boolean> = new BehaviorSubject(false);
  lockRowsAction = this.lockRowsSubject.asObservable();

  public projectIdSubject: BehaviorSubject<string> = new BehaviorSubject('0');
  projectIdSubject$ = this.projectIdSubject.asObservable();
  public versionIdSubject: BehaviorSubject<string> = new BehaviorSubject('0');
  versionIdSubject$ = this.versionIdSubject.asObservable();
  public dataStoreNameSubject: BehaviorSubject<string> = new BehaviorSubject('0');
  datastoreNameSubject$ = this.dataStoreNameSubject.asObservable();

  dataEntryFormClosed = new Subject<string>();
  
  constructor(
    private httpClient: HttpClient,
    private envService: EnvService,
    private userService: UserService) {
  }

  setProject(projId: string, versId: string, dataStore: string): void {
    this.projectIdSubject.next(projId);
    this.versionIdSubject.next(versId);
    this.dataStoreNameSubject.next(dataStore);
  }

  createReport(reportInfo: ReportInfo) {
    return this.httpClient.post<ReportInfo>(this.reportingGatewayServiceApiUrl
      + `api/admin/reports`, reportInfo, { withCredentials: true })
  }

  getReportInfo(reportId: string): Observable<ReportInfo> {
    return this.httpClient.get<ReportInfo>(this.reportingGatewayServiceApiUrl
      + `api/gateway/report/getReportInfo/${reportId}`, { withCredentials: true })
      .pipe(
        tap((reportInfo: ReportInfo) => {
          this.onActionableGridInfoLoaded.next(reportInfo);
          this.setProject(reportInfo.projectId, reportInfo.projectVersionId, reportInfo.formName);
        }),
        catchError(error => throwError(() => error))
      );
  }

  getReportList(projectVersionId: number) {
    return this.httpClient.get<ReportListModel[]>(this.reportingGatewayServiceApiUrl
      + `api/admin/reportsList?projectVersionId=${projectVersionId}`, { withCredentials: true })
  }

  updateAttachments(sbmeta, aliasIds) {
    if (!sbmeta) {
      sbmeta = {};
    }

    sbmeta.Attachments = aliasIds;
    return sbmeta;
  }

  getDatastoreStructure(columnConfigList: ActionableGridColumnConfig[], baseSchema: ExtendedJsonSchema): SimpleDatastoreStructure[] {
    if (!columnConfigList) {
      return null;
    }

    const columns: SimpleDatastoreStructure[] = [];

    columnConfigList.forEach(col => {
      const baseProperty = baseSchema?.properties[col.column];
      const column: SimpleDatastoreStructure = { name: col.column, baseProperty };

      if (baseProperty.type == JsonDataTypes.Collection || baseProperty.type == JsonDataTypes.Object) {
        column.children = this.getDatastoreStructure(col.children, baseProperty.type == JsonDataTypes.Collection ? baseProperty.items : baseProperty);
      }

      columns.push(column);
    });

    return columns;
  }

  focusOnCellEditor(colId, rowIndex, gridApi: GridApi) {
    if (colId == null)
      return;

    const params = { columns: [colId], rowNodes: [gridApi.getDisplayedRowAtIndex(rowIndex)] };
    if (!params.rowNodes[0])
      return;

    const instances = gridApi.getCellRendererInstances(params);

    // Check if it has a custom cell renderer and implements focus element then invoke
    if (instances?.length && (instances[0] as any)?.focusElement) {
      (instances[0] as any).focusElement();
    }
  }

  sendEmail(reportId: string, emailLimit: number, emailParameter: EmailSetup): any {
    return this.httpClient.post<any>(this.reportingGatewayServiceApiUrl
      + `api/reportview/report/sentReportEmail/${reportId}/${emailLimit}`, emailParameter, { withCredentials: true, responseType: 'text' as 'json' });
  }

  async getNewRowData(columnsConfig: ActionableGridColumnConfig[]): Promise<any> {
    const data = {};

    if (!columnsConfig?.length)
      return data;

    const profileProperties = await firstValueFrom(this.userService.getCompleteUserProfilePropertyList());
    columnsConfig.forEach(col => {
      let value: any = col.defaultValue?.isUserProfileValue
        ? profileProperties?.find(x => x.name === col.defaultValue?.value)?.defaultValue
        : col.defaultValue?.value;
      value = convertDefaultValue(value, col.format.baseType, col.format?.baseFormat)

      if (value !== undefined)
        data[col.column] = value;
    });

    return data;
  }

    /**
     * Returns the columns to exclude any columns with no header like master detail column and actions column.
     * 
     * @param {GridApi} gridApi - The grid API.
     * @return {string[]} - Returns the list of columns.
     */
  getGridDisplayedColumns(gridApi: GridApi): (string | import("ag-grid-community").Column<any>)[] {
    // To remove master detail column and actions column
    const columnsWithHeaders = gridApi.getAllDisplayedColumns()
      .filter(col => {
        const headerName = col.getColDef().headerName;
        return headerName && headerName.trim() !== '';
      })
      .map(col => col.getColId());

    return columnsWithHeaders;
  }
}
