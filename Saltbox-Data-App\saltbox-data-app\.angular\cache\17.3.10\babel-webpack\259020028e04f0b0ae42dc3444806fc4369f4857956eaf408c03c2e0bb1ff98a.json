{"ast": null, "code": "import _asyncToGenerator from \"C:/Projects/SaltboxActionableGrid/Saltbox-Data-App/saltbox-data-app/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { ActionableGridColumnConfig } from '../../core/models/actionable-grid-column-config';\nimport { sortingDateComparator } from 'src/app/shared/utilities/compare.functions';\nimport { dateValueFormatter, formatCurrency, numericValueFormatter } from 'src/app/shared/utilities/format.functions';\nimport { AgGridCheckBoxRendererComponent } from '../../core/ag-grid/renderers/ag-grid-checkbox-renderer.component';\nimport { AgGridDropDownRendererComponent } from '../../core/ag-grid/renderers/ag-grid-dropdown-renderer.component';\nimport { ActionableGridColumnFilters } from '../../core/enums/actionable-grid-column-filters';\nimport { AgGridEditableRendererComponent } from '../../core/ag-grid/renderers/ag-grid-editable-renderer.component';\nimport { AgGridMessageSymbolRendererComponent } from '../../core/ag-grid/renderers/ag-grid-symbol-renderer';\nimport { AgGridRowActionsRendererComponent } from '../../core/ag-grid/renderers/ag-grid-row-actions-renderer.component';\nimport { JsonDataTypes, JsonStringFormats } from 'src/app/shared/enums/json-schema.enum';\nimport { AgGridHeaderCheckBoxRendererComponent } from '../../core/ag-grid/renderers/headers/ag-grid-header-checkbox-renderer.component';\nimport { EditorColumnType } from 'src/app/shared/enums/editor-column-type.enum';\nimport { AgGridHyperlinkRendererComponent } from '../../core/ag-grid/renderers/ag-grid-hyperlink-renderer.component';\nimport { AgGridMasterDetailRendererComponent } from '../../core/ag-grid/renderers/ag-grid-master-detail-renderer.component';\nimport { ActionableGridMasterDetailConfigOptions } from '../../core/models/actionable-grid-master-detail-config-options';\nimport { ColDefOptions } from 'src/app/actionable-grid/model/col-def-options';\nimport { UserPermissionLevels } from 'src/app/core/enums/shared';\nimport { FormlyRendererTypes } from 'src/app/sb-formly-renderer/enums/formly-renderer-types.enum';\nimport { ActionableGridColumnFormat } from 'src/app/core/models/actionable-grid-column-format';\nimport { AgGridButtonRendererComponent } from 'src/app/core/ag-grid/renderers/ag-grid-button-renderer.component';\nimport { OptionValue } from 'src/app/shared/models/option-value';\nimport { AgGridHeaderFormulaRendererComponent } from 'src/app/core/ag-grid/renderers/headers/ag-grid-header-formula-renderer.component';\nimport { AgGridDateFilterComponent } from 'src/app/core/ag-grid/filter/ag-grid-date-filter.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../core/services/projects.service\";\nimport * as i2 from \"./actionable-grid.service\";\nexport class ActionableGridColService {\n  constructor(projectsService, actionableGridService) {\n    this.projectsService = projectsService;\n    this.actionableGridService = actionableGridService;\n    this.actionableGridColumnTypeColDef = {\n      [EditorColumnType.String]: options => this.setStringColDef(options),\n      [EditorColumnType.Date]: options => this.setDateColDef(options),\n      [EditorColumnType.DateTime]: options => this.setDateColDef(options),\n      [EditorColumnType.Numeric]: options => this.setNumericColDef(options),\n      [EditorColumnType.Currency]: options => this.setNumericColDef(options),\n      [EditorColumnType.Symbol]: options => this.setSymbolColDef(options),\n      [EditorColumnType.Checkbox]: options => this.setCheckboxColDef(options),\n      [EditorColumnType.Collection]: options => this.setArrayColDef(options),\n      [EditorColumnType.Link]: options => this.setLinkColDef(options),\n      [EditorColumnType.Object]: options => this.setButtonColDef(options)\n    };\n  }\n  getColumnDefs(columnConfigList, baseSchema, gridOptions, showFloatingFilter, projectVersionId, allowAddNewRow, parentColumn = null, userPermissionLevel, bypassObjects = true) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      if (!columnConfigList) {\n        return null;\n      }\n      let projectVariables;\n      if (columnConfigList?.some(columnConfig => columnConfig.format.type === EditorColumnType.Link)) {\n        projectVariables = yield _this.projectsService.getProjectVariablesAsync(projectVersionId);\n      }\n      const columnDefs = [{\n        field: '_id',\n        hide: true,\n        suppressColumnsToolPanel: true\n      }]; // adding the hidden primary key to the collection\n      // check if there is any array in the collection, set the grid as master detail\n      const hasArrays = columnConfigList.some(x => x.format.type === EditorColumnType.Collection);\n      if (hasArrays) {\n        columnDefs.push(_this.getMasterDetailColumn());\n      }\n      for (const col of columnConfigList) {\n        // Bypass objects if the bypassObjects flag is set\n        if (bypassObjects && col.format.type === EditorColumnType.Object) continue;\n        const baseProperty = baseSchema?.properties[col.column];\n        col.format.baseType = baseProperty?.type;\n        col.format.baseFormat = baseProperty?.format;\n        if (userPermissionLevel === UserPermissionLevels.View || baseProperty.presentationProperties?.enableFormula) {\n          col.allowUserUpdate = false;\n        }\n        const isArray = baseProperty.type === JsonDataTypes.Collection;\n        const isObject = baseProperty.type === JsonDataTypes.Object;\n        const colDef = {\n          field: col.column,\n          colId: col.column,\n          headerName: col.displayName || col.column,\n          rowGroup: col.groupByHeader && !isArray && !isObject,\n          enableRowGroup: true && !isArray && !isObject,\n          filter: !isArray && !isObject,\n          floatingFilter: showFloatingFilter,\n          sortable: !isArray && !isObject,\n          minWidth: 50,\n          hide: isArray,\n          suppressColumnsToolPanel: isArray || isObject\n        };\n        if (baseProperty.presentationProperties?.enableFormula) colDef.headerComponent = AgGridHeaderFormulaRendererComponent;\n        // override allow user update if the parent collection is not editable and this column is not a collection\n        if (parentColumn?.allowUserUpdate === false && !isArray) col.allowUserUpdate = false;\n        const colDefOptions = new ColDefOptions({\n          columnConfig: col,\n          colDef,\n          baseProperty,\n          gridOptions,\n          projectVariables,\n          projectVersionId,\n          allowAddNewRow,\n          parentColumn,\n          baseSchema,\n          bypassObjects,\n          showFloatingFilter\n        });\n        _this.actionableGridColumnTypeColDef[col.format.type](colDefOptions);\n        columnDefs.push(colDef);\n        if (col.format.type === EditorColumnType.Date || col.format.type === EditorColumnType.DateTime) {\n          columnDefs.push(..._this.getExtendedDateColumns(colDefOptions));\n        }\n      }\n      if (parentColumn == null || parentColumn.allowUserUpdate) columnDefs.push(_this.getRowActionsTools()); // Returned to last column position as per Pat 22.11.24\n      return columnDefs;\n    })();\n  }\n  getColumnDefsByFormlyFieldsConfig(fieldsConfig, baseSchema, showFloatingFilter, projectVersionId, userPermissionLevel) {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      if (!fieldsConfig) return null;\n      // adding the hidden primary key to the collection\n      const columnDefs = [{\n        field: '_id',\n        hide: true,\n        suppressColumnsToolPanel: true\n      }];\n      fieldsConfig.forEach(field => {\n        const baseProperty = baseSchema?.properties[field.key];\n        const agConfigCol = new ActionableGridColumnConfig(field.key);\n        agConfigCol.format = _this2.getAGColFormatByFormlyField(baseProperty, field);\n        agConfigCol.displayName = field.props.label;\n        agConfigCol.enableLookup = field.props.enableLookup;\n        //dropdowns\n        agConfigCol.dropdown = field.props?.options?.length > 0;\n        if (agConfigCol.dropdown) agConfigCol.values = (field.props?.options).map(x => {\n          return new OptionValue(x, x);\n        });\n        agConfigCol.allowUserUpdate = field.props?.readonly !== true && field.props?.disabled !== true && !baseProperty.presentationProperties?.enableFormula;\n        agConfigCol.required = field.props?.required;\n        if (userPermissionLevel === UserPermissionLevels.View) {\n          agConfigCol.allowUserUpdate = false;\n        }\n        const colDef = {\n          field: agConfigCol.column,\n          headerName: agConfigCol.displayName || agConfigCol.column,\n          rowGroup: agConfigCol.groupByHeader,\n          enableRowGroup: true,\n          filter: true,\n          floatingFilter: showFloatingFilter,\n          sortable: true,\n          minWidth: 100,\n          hide: agConfigCol.groupByHeader\n        };\n        const colDefOptions = new ColDefOptions({\n          columnConfig: agConfigCol,\n          colDef,\n          baseProperty,\n          projectVersionId,\n          baseSchema\n        });\n        if ([EditorColumnType.Object, EditorColumnType.Collection].includes(agConfigCol.format.type)) _this2.setButtonColDef(colDefOptions);else _this2.actionableGridColumnTypeColDef[agConfigCol.format.type](colDefOptions);\n        if (colDef.cellRendererParams) colDef.cellRendererParams.trackChange = false;\n        columnDefs.push(colDef);\n      });\n      if (userPermissionLevel != UserPermissionLevels.View) columnDefs.push(_this2.getRowActionsTools());\n      return columnDefs;\n    })();\n  }\n  getFormlyFieldsConfigByGridConfig(config) {\n    if (!config) return [];\n    const fields = [];\n    for (const fieldConfig of config) {\n      const field = this.mapToFormlyField(fieldConfig);\n      fields.push(field);\n    }\n    return fields;\n  }\n  mapBaseSchemaToActionableGridConfig(baseSchema) {\n    const formatSettings = [];\n    Object.keys(baseSchema.properties).forEach(property => {\n      const colConfig = new ActionableGridColumnConfig(property.toString());\n      const presentationProperties = baseSchema.properties[property].presentationProperties;\n      const typeProperties = baseSchema.properties[property].typeProperties;\n      colConfig.format.baseType = baseSchema.properties[property].type;\n      colConfig.format.baseFormat = baseSchema.properties[property].format;\n      colConfig.displayName = presentationProperties?.displayName;\n      colConfig.enableLookup = presentationProperties?.enableLookup;\n      colConfig.required = typeProperties?.required;\n      // setting default formats\n      switch (colConfig.format.baseType) {\n        case JsonDataTypes.String:\n          if (colConfig.format.baseFormat === JsonStringFormats.DateTime) {\n            colConfig.format.type = presentationProperties.displayTime ? EditorColumnType.DateTime : EditorColumnType.Date;\n          } else {\n            colConfig.format.type = EditorColumnType.String;\n          }\n          break;\n        case JsonDataTypes.Boolean:\n          colConfig.format.type = EditorColumnType.Checkbox;\n          break;\n        case JsonDataTypes.Decimal:\n        case JsonDataTypes.Integer:\n          colConfig.format.decimalPlaces = colConfig.format.baseType === JsonDataTypes.Integer ? 0 : presentationProperties?.decimalPlaces;\n          colConfig.format.type = typeProperties?.currency ? EditorColumnType.Currency : EditorColumnType.Numeric;\n          if (typeProperties?.currency) {\n            colConfig.format.currency = typeProperties.currency;\n          }\n          break;\n        case JsonDataTypes.Collection:\n          colConfig.format.type = EditorColumnType.Collection;\n          colConfig.children = this.mapBaseSchemaToActionableGridConfig(baseSchema.properties[property].items);\n          break;\n        case JsonDataTypes.Object:\n          colConfig.format.type = EditorColumnType.Object;\n          break;\n        default:\n          colConfig.format.type = EditorColumnType.String;\n          break;\n      }\n      formatSettings.push(colConfig);\n    });\n    return formatSettings;\n  }\n  mapToFormlyField(fieldConfig) {\n    const type = this.getFormlyRendererType(fieldConfig);\n    const props = {\n      label: fieldConfig.displayName || fieldConfig.column,\n      required: fieldConfig.required,\n      enableLookup: fieldConfig.enableLookup\n    };\n    // setting properties based on type\n    switch (fieldConfig.format.type) {\n      case EditorColumnType.DateTime:\n        props.displayTime = true;\n        break;\n      case EditorColumnType.Currency:\n        props.currency = fieldConfig.format?.currency;\n        props.decimalPlaces = fieldConfig.format?.decimalPlaces;\n        break;\n      case EditorColumnType.Numeric:\n        props.decimalPlaces = fieldConfig.format?.decimalPlaces;\n        break;\n    }\n    if (fieldConfig.dropdown) props.options = fieldConfig.values.map(validValue => ({\n      label: validValue.key,\n      value: validValue.value\n    }));\n    if (fieldConfig.defaultValue) props.defaultValue = {\n      isUserProfileValue: fieldConfig.defaultValue.isUserProfileValue,\n      value: fieldConfig.defaultValue.value\n    };\n    props.readonly = !fieldConfig.allowUserUpdate;\n    const field = {\n      key: fieldConfig.column,\n      type,\n      props\n    };\n    if (fieldConfig.format.type === EditorColumnType.Collection) field.fieldArray = {\n      fieldGroup: this.getFormlyFieldsConfigByGridConfig(fieldConfig.children)\n    };\n    if (fieldConfig.format.type === EditorColumnType.Object) field.fieldGroup = this.getFormlyFieldsConfigByGridConfig(fieldConfig.children);\n    return field;\n  }\n  getFormlyRendererType(fieldConfig) {\n    switch (fieldConfig.format.type) {\n      case EditorColumnType.String:\n        return fieldConfig.dropdown ? FormlyRendererTypes.Dropdown : FormlyRendererTypes.InputText;\n      case EditorColumnType.Link:\n      case EditorColumnType.Symbol:\n        return FormlyRendererTypes.InputText;\n      case EditorColumnType.Date:\n      case EditorColumnType.DateTime:\n        return FormlyRendererTypes.Calendar;\n      case EditorColumnType.Numeric:\n      case EditorColumnType.Currency:\n        return FormlyRendererTypes.InputNumber;\n      case EditorColumnType.Checkbox:\n        return FormlyRendererTypes.Checkbox;\n      case EditorColumnType.Collection:\n        return FormlyRendererTypes.CollectionGrid;\n      case EditorColumnType.Object:\n        return FormlyRendererTypes.Object;\n      default:\n        return FormlyRendererTypes.None;\n      // Default type\n    }\n  }\n  getAGColFormatByFormlyField(baseProperty, field) {\n    const colFormat = new ActionableGridColumnFormat();\n    colFormat.baseType = baseProperty?.type;\n    colFormat.baseFormat = baseProperty?.format;\n    switch (field.type) {\n      case FormlyRendererTypes.InputText:\n      case FormlyRendererTypes.Dropdown:\n      case FormlyRendererTypes.InputMask:\n      case FormlyRendererTypes.InputTextarea:\n        colFormat.type = EditorColumnType.String;\n        break;\n      case FormlyRendererTypes.Calendar:\n        colFormat.type = field.props?.displayTime ? EditorColumnType.DateTime : EditorColumnType.Date;\n        break;\n      case FormlyRendererTypes.Checkbox:\n        colFormat.type = EditorColumnType.Checkbox;\n        break;\n      case FormlyRendererTypes.InputNumber:\n        colFormat.type = field.props?.currency ? EditorColumnType.Currency : EditorColumnType.Numeric;\n        colFormat.currency = field.props?.currency;\n        colFormat.decimalPlaces = field.props?.decimalPlaces;\n        break;\n      case FormlyRendererTypes.Object:\n        colFormat.type = EditorColumnType.Object;\n        break;\n      case FormlyRendererTypes.CollectionGrid:\n      case FormlyRendererTypes.CollectionForm:\n      case FormlyRendererTypes.CollectionRepeat:\n        colFormat.type = EditorColumnType.Collection;\n        break;\n    }\n    return colFormat;\n  }\n  // Returns the tools for edit, delete and file-upload\n  getRowActionsTools() {\n    const colDef = {\n      field: '__rowActions',\n      headerName: '',\n      enableRowGroup: false,\n      filter: false,\n      floatingFilter: false,\n      chartDataType: 'series',\n      sortable: false,\n      width: 93,\n      minWidth: 50,\n      resizable: false,\n      pinned: 'left',\n      suppressHeaderMenuButton: true,\n      suppressHeaderContextMenu: true,\n      suppressMovable: true,\n      suppressColumnsToolPanel: true,\n      menuTabs: []\n    };\n    colDef.cellRenderer = AgGridRowActionsRendererComponent;\n    colDef.cellRendererParams = {\n      projectId: this.actionableGridService.projectIdSubject.getValue(),\n      versionId: this.actionableGridService.versionIdSubject.getValue(),\n      dataStoreName: this.actionableGridService.dataStoreNameSubject.getValue()\n    };\n    return colDef;\n  }\n  // Returns master detail arrow.\n  getMasterDetailColumn() {\n    return {\n      floatingFilter: false,\n      sortable: false,\n      resizable: false,\n      maxWidth: 40,\n      rowGroup: false,\n      enableRowGroup: false,\n      filter: false,\n      suppressHeaderMenuButton: true,\n      cellRenderer: 'agGroupCellRenderer',\n      suppressColumnsToolPanel: true,\n      suppressMovable: true,\n      cellRendererParams: {\n        suppressCount: true\n      }\n    };\n  }\n  // Start of ColDef functions for Column Type.\n  setStringColDef(options) {\n    options.colDef.headerClass = params => options.columnConfig.required ? 'show-required' : '';\n    options.colDef.filter = ActionableGridColumnFilters.Text;\n    options.colDef.chartDataType = 'category';\n    options.colDef.filterParams = {\n      defaultOption: 'startsWith'\n    };\n    const cellRendererParams = {\n      format: options.columnConfig.format,\n      allowUserUpdate: options.columnConfig.allowUserUpdate,\n      required: options.columnConfig.required,\n      fieldBaseSchema: options.baseProperty,\n      baseSchema: options.baseSchema,\n      fieldName: options.colDef.field,\n      fieldDisplayName: options.colDef.headerName,\n      enableLookup: options.columnConfig.enableLookup,\n      projectVersionId: options.projectVersionId\n    };\n    if (options.columnConfig.allowUserUpdate) {\n      if (options.columnConfig.dropdown) {\n        options.colDef.cellRenderer = AgGridDropDownRendererComponent;\n        options.colDef.cellRendererParams = {\n          ...cellRendererParams,\n          allowedValues: options.columnConfig.values\n        };\n      } else {\n        options.colDef.cellRenderer = AgGridEditableRendererComponent;\n        options.colDef.cellRendererParams = cellRendererParams;\n      }\n    }\n  }\n  setDateColDef(options) {\n    this.setAgGridColDefRendererAndStyle(options);\n    options.colDef.filter = AgGridDateFilterComponent;\n    options.colDef.filterParams = {\n      showTime: options.columnConfig.format.type === EditorColumnType.DateTime\n    };\n    options.colDef.comparator = sortingDateComparator;\n    options.colDef.valueFormatter = params => dateValueFormatter(params.value, options.columnConfig.format.type === EditorColumnType.DateTime);\n  }\n  // getExtendedDateColumns(sourceDateColDefOptions: ColDefOptions): ColDef[] {\n  //   const colId = sourceDateColDefOptions.colDef.colId;\n  //   const months = ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'];\n  //   const quarters = ['Q1', 'Q2', 'Q3', 'Q4'];\n  //   const commonColDef = {\n  //     enableRowGroup: true,\n  //     floatingFilter: sourceDateColDefOptions.showFloatingFilter,\n  //     sortable: true,\n  //     width: 100,\n  //     hide: true,\n  //   };\n  //   const yearSortComparator = (a: string, b: string) => {\n  //     return months.indexOf(a) - months.indexOf(b);\n  //   };\n  //   const quarterSortComparator = (a: string, b: string) => {\n  //     return quarters.indexOf(a) - quarters.indexOf(b);\n  //   };\n  //   return [\n  //     {\n  //       ...commonColDef,\n  //       headerName: sourceDateColDefOptions.colDef.headerName + ' (Year)',\n  //       colId: `${colId}_year`,\n  //       valueGetter: (params) => {\n  //         const date = new Date(params.data?.[colId]);\n  //         return isNaN(date.getTime()) ? null : date.getFullYear();\n  //       },\n  //     },\n  //     {\n  //       ...commonColDef,\n  //       headerName: sourceDateColDefOptions.colDef.headerName + ' (Quarter)',\n  //       colId: `${colId}_quarter`,\n  //       valueGetter: (params) => {\n  //         const date = new Date(params.data?.[colId]);\n  //         if (isNaN(date.getTime())) return null;\n  //         const quarter = Math.floor(date.getMonth() / 3) + 1;\n  //         return `Q${quarter}`;\n  //       },\n  //       filter: 'agSetColumnFilter',\n  //       filterParams: {\n  //         comparator: quarterSortComparator,\n  //       },\n  //       comparator: quarterSortComparator,\n  //       pivotComparator: quarterSortComparator,\n  //     },\n  //     {\n  //       ...commonColDef,\n  //       headerName: sourceDateColDefOptions.colDef.headerName + ' (Month)',\n  //       colId: `${colId}_month`,\n  //       valueGetter: (params) => {\n  //         const date = new Date(params.data?.[colId]);\n  //         const monthIndex = isNaN(date.getTime()) ? null : date.getMonth();\n  //         return monthIndex != null\n  //           ? months[monthIndex]\n  //           : null;\n  //       },\n  //       filter: 'agSetColumnFilter',\n  //       filterParams: {\n  //         comparator: yearSortComparator,\n  //       },\n  //       comparator: yearSortComparator,\n  //       pivotComparator: yearSortComparator,\n  //     }\n  //   ];\n  // }\n  getExtendedDateColumns(sourceDateColDefOptions) {\n    const colId = sourceDateColDefOptions.colDef.colId;\n    const months = ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'];\n    const commonColDef = {\n      enableRowGroup: true,\n      floatingFilter: sourceDateColDefOptions.showFloatingFilter,\n      sortable: true,\n      width: 100,\n      hide: true\n    };\n    return [{\n      ...commonColDef,\n      headerName: sourceDateColDefOptions.colDef.headerName + ' (Year)',\n      colId: `${colId}_year`,\n      valueGetter: params => {\n        const date = new Date(params.data?.[colId]);\n        return isNaN(date.getTime()) ? null : date.getFullYear();\n      }\n    }, {\n      ...commonColDef,\n      headerName: sourceDateColDefOptions.colDef.headerName + ' (Quarter)',\n      colId: `${colId}_quarter`,\n      valueGetter: params => {\n        const date = new Date(params.data?.[colId]);\n        return isNaN(date.getTime()) ? null : Math.floor(date.getMonth() / 3) + 1; // 1 to 4\n      },\n      valueFormatter: params => params.value ? `Q${params.value}` : '',\n      filter: 'agSetColumnFilter',\n      filterParams: {\n        values: [1, 2, 3, 4],\n        valueFormatter: value => `Q${value}`,\n        comparator: (a, b) => a - b\n      },\n      comparator: (a, b) => a - b,\n      pivotComparator: (a, b) => a - b\n    }, {\n      ...commonColDef,\n      headerName: sourceDateColDefOptions.colDef.headerName + ' (Month)',\n      colId: `${colId}_month`,\n      valueGetter: params => {\n        const date = new Date(params.data?.[colId]);\n        return isNaN(date.getTime()) ? null : date.getMonth(); // 0 to 11\n      },\n      valueFormatter: params => params.value != null ? months[params.value] : '',\n      filter: 'agSetColumnFilter',\n      filterParams: {\n        values: months.map((_, index) => index),\n        valueFormatter: value => months[value],\n        comparator: (a, b) => a - b\n      },\n      comparator: (a, b) => a - b,\n      pivotComparator: (a, b) => a - b\n    }];\n  }\n  setNumericColDef(options) {\n    this.setAgGridColDefRendererAndStyle(options);\n    options.colDef.filter = ActionableGridColumnFilters.Number;\n    options.colDef.valueGetter = params => this.numericValueGetter(params.data?.[params.column?.getColId()]);\n    if (options.columnConfig.format.type === EditorColumnType.Currency) {\n      options.colDef.valueFormatter = params => formatCurrency(params.value, options.columnConfig.format.currency, options.columnConfig.format.decimalPlaces);\n    } else {\n      options.colDef.valueFormatter = params => numericValueFormatter(params.value, options.columnConfig.format.decimalPlaces);\n    }\n  }\n  setSymbolColDef(options) {\n    options.colDef.headerClass = params => options.columnConfig.required ? 'show-required' : '';\n    options.colDef.cellRenderer = AgGridMessageSymbolRendererComponent;\n    options.colDef.cellRendererParams = {\n      allowedValues: options.columnConfig.values,\n      allowUserUpdate: options.columnConfig.allowUserUpdate,\n      format: options.columnConfig.format,\n      required: options.columnConfig.required,\n      fieldBaseSchema: options.baseProperty\n    };\n  }\n  setCheckboxColDef(options) {\n    options.colDef.cellRenderer = AgGridCheckBoxRendererComponent;\n    options.colDef.headerClass = params => options.columnConfig.required ? 'show-required' : '';\n    if (options.columnConfig.allowUserUpdate) options.colDef.headerComponent = AgGridHeaderCheckBoxRendererComponent;\n    options.colDef.cellRendererParams = {\n      allowedValues: options.columnConfig.values,\n      allowUserUpdate: options.columnConfig.allowUserUpdate,\n      format: options.columnConfig.format,\n      required: options.columnConfig.required,\n      fieldBaseSchema: options.baseProperty\n    };\n  }\n  setArrayColDef(options) {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      options.gridOptions.detailCellRenderer = AgGridMasterDetailRendererComponent;\n      options.gridOptions.masterDetail = true;\n      const childGridOptions = {\n        detailRowAutoHeight: true,\n        defaultColDef: {\n          resizable: true\n        },\n        popupParent: document.querySelector('body'),\n        context: {\n          getMainGridId: options.gridOptions.context?.getMainGridId,\n          getMasterRecordParams: options.gridOptions.context?.getMasterRecordParams\n        }\n      };\n      const columnDefs = yield _this3.getColumnDefs(options.columnConfig.children, options.baseProperty.items, childGridOptions, false, options.projectVersionId, options.allowAddNewRow, options.columnConfig, null, options.bypassObjects);\n      if (!options.gridOptions.detailCellRendererParams?.configs?.length) options.gridOptions.detailCellRendererParams = {\n        configs: []\n      };\n      options.gridOptions.detailCellRendererParams.configs.push(new ActionableGridMasterDetailConfigOptions(options.columnConfig, options.baseProperty, childGridOptions, options.allowAddNewRow, columnDefs, options.projectVersionId, options.projectId, options.gridOptions.context?.getMainGridId, options.gridOptions.context?.getMasterRecordParams));\n    })();\n  }\n  setLinkColDef(options) {\n    options.colDef.cellRenderer = AgGridHyperlinkRendererComponent;\n    options.colDef.cellRendererParams = {\n      allowedValues: options.columnConfig.values,\n      allowUserUpdate: options.columnConfig.allowUserUpdate,\n      link: options.columnConfig.link,\n      format: options.columnConfig.format,\n      required: options.columnConfig.required,\n      projectVariables: options.projectVariables,\n      fieldBaseSchema: options.baseProperty\n    };\n  }\n  numericValueGetter(paramsValue) {\n    if (paramsValue !== undefined) {\n      return !isNaN(Number(paramsValue)) ? Number(paramsValue) : paramsValue;\n    }\n  }\n  setButtonColDef(options) {\n    const cellRendererParams = {\n      format: options.columnConfig.format,\n      allowUserUpdate: options.columnConfig.allowUserUpdate,\n      required: options.columnConfig.required,\n      fieldBaseSchema: options.baseProperty\n    };\n    options.colDef.headerClass = params => options.columnConfig.required ? 'show-required' : '';\n    options.colDef.cellRenderer = AgGridButtonRendererComponent;\n    options.colDef.cellRendererParams = {\n      ...cellRendererParams\n    };\n  }\n  setAgGridColDefRendererAndStyle(options) {\n    const cellRendererParams = {\n      format: options.columnConfig.format,\n      allowUserUpdate: options.columnConfig.allowUserUpdate,\n      required: options.columnConfig.required,\n      fieldBaseSchema: options.baseProperty\n    };\n    if (options.columnConfig.allowUserUpdate) {\n      options.colDef.headerClass = params => options.columnConfig.allowUserUpdate && options.columnConfig.required ? 'show-required' : '';\n      options.colDef.cellRenderer = AgGridEditableRendererComponent;\n      options.colDef.cellRendererParams = cellRendererParams;\n      options.colDef.cellClass = '';\n    } else {\n      options.colDef.cellClass = 'sb-field-right-align';\n    }\n  }\n  static {\n    this.ɵfac = function ActionableGridColService_Factory(t) {\n      return new (t || ActionableGridColService)(i0.ɵɵinject(i1.ProjectsService), i0.ɵɵinject(i2.ActionableGridService));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: ActionableGridColService,\n      factory: ActionableGridColService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["ActionableGridColumnConfig", "sortingDateComparator", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "formatCurrency", "numericValueFormatter", "AgGridCheckBoxRendererComponent", "AgGridDropDownRendererComponent", "ActionableGridColumnFilters", "AgGridEditableRendererComponent", "AgGridMessageSymbolRendererComponent", "AgGridRowActionsRendererComponent", "JsonDataTypes", "JsonStringFormats", "AgGridHeaderCheckBoxRendererComponent", "EditorColumnType", "AgGridHyperlinkRendererComponent", "AgGridMasterDetailRendererComponent", "ActionableGridMasterDetailConfigOptions", "ColDefOptions", "UserPermissionLevels", "FormlyRendererTypes", "ActionableGridColumnFormat", "AgGridButtonRendererComponent", "OptionValue", "AgGridHeaderFormulaRendererComponent", "AgGridDateFilterComponent", "ActionableGridColService", "constructor", "projectsService", "actionableGridService", "actionableGridColumnTypeColDef", "String", "options", "setStringColDef", "Date", "setDateColDef", "DateTime", "Numeric", "setNumericColDef", "<PERSON><PERSON><PERSON><PERSON>", "Symbol", "setSymbolColDef", "Checkbox", "setCheckboxColDef", "Collection", "setArrayColDef", "Link", "setLinkColDef", "Object", "setButtonColDef", "getColumnDefs", "columnConfigList", "baseSchema", "gridOptions", "showFloatingFilter", "projectVersionId", "allowAddNewRow", "parentColumn", "userPermissionLevel", "bypassObjects", "_this", "_asyncToGenerator", "projectVariables", "some", "columnConfig", "format", "type", "getProjectVariablesAsync", "columnDefs", "field", "hide", "suppressColumnsToolPanel", "<PERSON><PERSON><PERSON><PERSON>", "x", "push", "getMasterDetailColumn", "col", "baseProperty", "properties", "column", "baseType", "baseFormat", "View", "presentationProperties", "enableFormula", "allowUserUpdate", "isArray", "isObject", "colDef", "colId", "headerName", "displayName", "rowGroup", "groupByHeader", "enableRowGroup", "filter", "floatingFilter", "sortable", "min<PERSON><PERSON><PERSON>", "headerComponent", "colDefOptions", "getExtendedDateColumns", "getRowActionsTools", "getColumnDefsByFormlyFieldsConfig", "fieldsConfig", "_this2", "for<PERSON>ach", "key", "agConfigCol", "getAGColFormatByFormlyField", "props", "label", "enableLookup", "dropdown", "length", "values", "map", "readonly", "disabled", "required", "includes", "cellRendererParams", "trackChange", "getFormlyFieldsConfigByGridConfig", "config", "fields", "fieldConfig", "mapToFormlyField", "mapBaseSchemaToActionableGridConfig", "formatSettings", "keys", "property", "colConfig", "toString", "typeProperties", "displayTime", "Boolean", "Decimal", "Integer", "decimalPlaces", "currency", "children", "items", "getFormlyRendererType", "validValue", "value", "defaultValue", "isUserProfileValue", "fieldArray", "fieldGroup", "Dropdown", "InputText", "Calendar", "InputNumber", "CollectionGrid", "None", "colFormat", "InputMask", "InputTextarea", "CollectionForm", "CollectionRepeat", "chartDataType", "width", "resizable", "pinned", "suppressHeaderMenuButton", "suppressHeaderContextMenu", "suppressMovable", "menuTabs", "cell<PERSON><PERSON><PERSON>", "projectId", "projectIdSubject", "getValue", "versionId", "versionIdSubject", "dataStoreName", "dataStoreNameSubject", "max<PERSON><PERSON><PERSON>", "suppressCount", "headerClass", "params", "Text", "filterParams", "defaultOption", "fieldBaseSchema", "fieldName", "fieldDisplayName", "<PERSON><PERSON><PERSON><PERSON>", "setAgGridColDefRendererAndStyle", "showTime", "comparator", "valueFormatter", "sourceDateColDefOptions", "months", "commonColDef", "valueGetter", "date", "data", "isNaN", "getTime", "getFullYear", "Math", "floor", "getMonth", "a", "b", "pivotComparator", "_", "index", "Number", "numericValueGetter", "getColId", "_this3", "detail<PERSON><PERSON><PERSON><PERSON><PERSON>", "masterDetail", "childGridOptions", "detailRowAutoHeight", "defaultColDef", "popupParent", "document", "querySelector", "context", "getMainGridId", "getMasterRecordParams", "detailCellRendererParams", "configs", "link", "paramsValue", "undefined", "cellClass", "i0", "ɵɵinject", "i1", "ProjectsService", "i2", "ActionableGridService", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Projects\\SaltboxActionableGrid\\Saltbox-Data-App\\saltbox-data-app\\src\\app\\actionable-grid\\services\\actionable-grid-col.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { ColDef, GridOptions } from 'ag-grid-community';\r\nimport { ActionableGridColumnConfig } from '../../core/models/actionable-grid-column-config';\r\nimport { sortingDateComparator } from 'src/app/shared/utilities/compare.functions';\r\nimport { dateValueFormatter, formatCurrency, numericValueFormatter } from 'src/app/shared/utilities/format.functions';\r\nimport { AgGridCheckBoxRendererComponent } from '../../core/ag-grid/renderers/ag-grid-checkbox-renderer.component';\r\nimport { AgGridDropDownRendererComponent } from '../../core/ag-grid/renderers/ag-grid-dropdown-renderer.component';\r\nimport { ActionableGridColumnFilters } from '../../core/enums/actionable-grid-column-filters';\r\nimport { AgGridEditableRendererComponent } from '../../core/ag-grid/renderers/ag-grid-editable-renderer.component';\r\nimport { AgGridMessageSymbolRendererComponent } from '../../core/ag-grid/renderers/ag-grid-symbol-renderer';\r\nimport { AgGridRowActionsRendererComponent } from '../../core/ag-grid/renderers/ag-grid-row-actions-renderer.component';\r\nimport { ExtendedJsonSchema } from 'src/app/shared/models/extended-json-schema';\r\nimport { JsonDataTypes, JsonStringFormats } from 'src/app/shared/enums/json-schema.enum';\r\nimport { AgGridHeaderCheckBoxRendererComponent } from '../../core/ag-grid/renderers/headers/ag-grid-header-checkbox-renderer.component';\r\nimport { EditorColumnType } from 'src/app/shared/enums/editor-column-type.enum';\r\nimport { AgGridHyperlinkRendererComponent } from '../../core/ag-grid/renderers/ag-grid-hyperlink-renderer.component';\r\nimport { ProjectsService } from '../../core/services/projects.service';\r\nimport { AgGridMasterDetailRendererComponent } from '../../core/ag-grid/renderers/ag-grid-master-detail-renderer.component';\r\nimport { ActionableGridMasterDetailConfigOptions } from '../../core/models/actionable-grid-master-detail-config-options';\r\nimport { ColDefOptions } from 'src/app/actionable-grid/model/col-def-options';\r\nimport { UserPermissionLevels } from 'src/app/core/enums/shared';\r\nimport { FormlyFieldConfig } from '@ngx-formly/core';\r\nimport { FormlyRendererTypes } from 'src/app/sb-formly-renderer/enums/formly-renderer-types.enum';\r\nimport { ActionableGridColumnFormat } from 'src/app/core/models/actionable-grid-column-format';\r\nimport { ICustomCellRendererParams } from 'src/app/core/ag-grid/renderers/ag-grid-custom-cell-renderer-params';\r\nimport { AgGridButtonRendererComponent } from 'src/app/core/ag-grid/renderers/ag-grid-button-renderer.component';\r\nimport { OptionValue } from 'src/app/shared/models/option-value';\r\nimport { IRowActionsParamsContext } from 'src/app/core/models/row-actions-params-context';\r\nimport { AgGridHeaderFormulaRendererComponent } from 'src/app/core/ag-grid/renderers/headers/ag-grid-header-formula-renderer.component';\r\nimport { ActionableGridService } from './actionable-grid.service';\r\nimport { AgGridDateFilterComponent } from 'src/app/core/ag-grid/filter/ag-grid-date-filter.component';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\n\r\nexport class ActionableGridColService {\r\n\r\n  private actionableGridColumnTypeColDef = {\r\n    [EditorColumnType.String]: (options: ColDefOptions) => this.setStringColDef(options),\r\n    [EditorColumnType.Date]: (options: ColDefOptions) => this.setDateColDef(options),\r\n    [EditorColumnType.DateTime]: (options: ColDefOptions) => this.setDateColDef(options),\r\n    [EditorColumnType.Numeric]: (options: ColDefOptions) => this.setNumericColDef(options),\r\n    [EditorColumnType.Currency]: (options: ColDefOptions) => this.setNumericColDef(options),\r\n    [EditorColumnType.Symbol]: (options: ColDefOptions) => this.setSymbolColDef(options),\r\n    [EditorColumnType.Checkbox]: (options: ColDefOptions) => this.setCheckboxColDef(options),\r\n    [EditorColumnType.Collection]: (options: ColDefOptions) => this.setArrayColDef(options),\r\n    [EditorColumnType.Link]: (options: ColDefOptions) => this.setLinkColDef(options),\r\n    [EditorColumnType.Object]: (options: ColDefOptions) => this.setButtonColDef(options)\r\n  };\r\n\r\n  constructor(\r\n    private projectsService: ProjectsService, private actionableGridService: ActionableGridService) {\r\n  }\r\n\r\n  async getColumnDefs(\r\n    columnConfigList: ActionableGridColumnConfig[],\r\n    baseSchema: ExtendedJsonSchema,\r\n    gridOptions: GridOptions,\r\n    showFloatingFilter: boolean,\r\n    projectVersionId: number,\r\n    allowAddNewRow: boolean,\r\n    parentColumn: ActionableGridColumnConfig = null,\r\n    userPermissionLevel?: UserPermissionLevels,\r\n    bypassObjects = true): Promise<ColDef[]> {\r\n\r\n    if (!columnConfigList) {\r\n      return null;\r\n    }\r\n\r\n    let projectVariables;\r\n    if (columnConfigList?.some(columnConfig => columnConfig.format.type === EditorColumnType.Link)) {\r\n      projectVariables = await this.projectsService.getProjectVariablesAsync(projectVersionId);\r\n    }\r\n\r\n    const columnDefs: ColDef[] = [{ field: '_id', hide: true, suppressColumnsToolPanel: true }]; // adding the hidden primary key to the collection\r\n\r\n    // check if there is any array in the collection, set the grid as master detail\r\n    const hasArrays = columnConfigList.some(x => x.format.type === EditorColumnType.Collection);\r\n\r\n    if (hasArrays) {\r\n      columnDefs.push(this.getMasterDetailColumn());\r\n    }\r\n\r\n    for (const col of columnConfigList) {\r\n      // Bypass objects if the bypassObjects flag is set\r\n      if (bypassObjects && col.format.type === EditorColumnType.Object)\r\n        continue;\r\n\r\n      const baseProperty = baseSchema?.properties[col.column];\r\n      col.format.baseType = baseProperty?.type;\r\n      col.format.baseFormat = baseProperty?.format;\r\n\r\n      if (userPermissionLevel === UserPermissionLevels.View || baseProperty.presentationProperties?.enableFormula) {\r\n        col.allowUserUpdate = false;\r\n      }\r\n\r\n      const isArray = baseProperty.type === JsonDataTypes.Collection;\r\n      const isObject = baseProperty.type === JsonDataTypes.Object;\r\n      const colDef: ColDef = {\r\n        field: col.column,\r\n        colId: col.column,\r\n        headerName: col.displayName || col.column,\r\n        rowGroup: col.groupByHeader && !isArray && !isObject,\r\n        enableRowGroup: true && !isArray && !isObject,\r\n        filter: !isArray && !isObject,\r\n        floatingFilter: showFloatingFilter,\r\n        sortable: !isArray && !isObject,\r\n        minWidth: 50,\r\n        hide: isArray,\r\n        suppressColumnsToolPanel: isArray || isObject,\r\n      };\r\n\r\n      if (baseProperty.presentationProperties?.enableFormula)\r\n        colDef.headerComponent = AgGridHeaderFormulaRendererComponent;\r\n\r\n      // override allow user update if the parent collection is not editable and this column is not a collection\r\n      if (parentColumn?.allowUserUpdate === false && !isArray)\r\n        col.allowUserUpdate = false;\r\n\r\n      const colDefOptions = new ColDefOptions({ columnConfig: col, colDef, baseProperty, gridOptions, projectVariables, projectVersionId, allowAddNewRow, parentColumn, baseSchema, bypassObjects, showFloatingFilter });\r\n      this.actionableGridColumnTypeColDef[col.format.type](colDefOptions);\r\n\r\n      columnDefs.push(colDef);\r\n      if (col.format.type === EditorColumnType.Date || col.format.type === EditorColumnType.DateTime) {\r\n        columnDefs.push(...this.getExtendedDateColumns(colDefOptions));\r\n      }\r\n    }\r\n\r\n    if (parentColumn == null || parentColumn.allowUserUpdate)\r\n      columnDefs.push(this.getRowActionsTools());  // Returned to last column position as per Pat 22.11.24\r\n\r\n    return columnDefs;\r\n  }\r\n\r\n  async getColumnDefsByFormlyFieldsConfig(\r\n    fieldsConfig: FormlyFieldConfig[],\r\n    baseSchema: ExtendedJsonSchema,\r\n    showFloatingFilter: boolean,\r\n    projectVersionId: number,\r\n    userPermissionLevel?: UserPermissionLevels): Promise<ColDef[]> {\r\n\r\n    if (!fieldsConfig)\r\n      return null;\r\n\r\n    // adding the hidden primary key to the collection\r\n    const columnDefs: ColDef[] = [{ field: '_id', hide: true, suppressColumnsToolPanel: true }];\r\n\r\n    fieldsConfig.forEach(field => {\r\n      const baseProperty = baseSchema?.properties[field.key as string];\r\n      const agConfigCol = new ActionableGridColumnConfig(field.key as string)\r\n      agConfigCol.format = this.getAGColFormatByFormlyField(baseProperty, field);\r\n      agConfigCol.displayName = field.props.label;\r\n      agConfigCol.enableLookup = field.props.enableLookup;\r\n\r\n      //dropdowns\r\n      agConfigCol.dropdown = (field.props?.options as any[])?.length > 0;\r\n      if (agConfigCol.dropdown)\r\n        agConfigCol.values = (field.props?.options as any[]).map(x => { return new OptionValue(x, x); });\r\n\r\n      agConfigCol.allowUserUpdate = field.props?.readonly !== true && field.props?.disabled !== true && !baseProperty.presentationProperties?.enableFormula;\r\n      agConfigCol.required = field.props?.required;\r\n\r\n      if (userPermissionLevel === UserPermissionLevels.View) {\r\n        agConfigCol.allowUserUpdate = false;\r\n      }\r\n\r\n      const colDef: ColDef = {\r\n        field: agConfigCol.column,\r\n        headerName: agConfigCol.displayName || agConfigCol.column,\r\n        rowGroup: agConfigCol.groupByHeader,\r\n        enableRowGroup: true,\r\n        filter: true,\r\n        floatingFilter: showFloatingFilter,\r\n        sortable: true,\r\n        minWidth: 100,\r\n        hide: agConfigCol.groupByHeader\r\n      };\r\n\r\n      const colDefOptions = new ColDefOptions({ columnConfig: agConfigCol, colDef, baseProperty, projectVersionId, baseSchema });\r\n      if ([EditorColumnType.Object, EditorColumnType.Collection].includes(agConfigCol.format.type))\r\n        this.setButtonColDef(colDefOptions);\r\n      else\r\n        this.actionableGridColumnTypeColDef[agConfigCol.format.type](colDefOptions);\r\n\r\n      if (colDef.cellRendererParams as ICustomCellRendererParams)\r\n        (colDef.cellRendererParams as ICustomCellRendererParams).trackChange = false;\r\n\r\n      columnDefs.push(colDef);\r\n    });\r\n\r\n    if (userPermissionLevel != UserPermissionLevels.View)\r\n      columnDefs.push(this.getRowActionsTools());\r\n\r\n    return columnDefs;\r\n  }\r\n\r\n  getFormlyFieldsConfigByGridConfig(config: ActionableGridColumnConfig[]): FormlyFieldConfig[] {\r\n    if (!config)\r\n      return [];\r\n\r\n    const fields: FormlyFieldConfig[] = [];\r\n\r\n    for (const fieldConfig of config) {\r\n      const field: FormlyFieldConfig = this.mapToFormlyField(fieldConfig);\r\n      fields.push(field);\r\n    }\r\n\r\n    return fields;\r\n  }\r\n\r\n  mapBaseSchemaToActionableGridConfig(baseSchema: ExtendedJsonSchema): ActionableGridColumnConfig[] {\r\n    const formatSettings: ActionableGridColumnConfig[] = [];\r\n\r\n    Object.keys(baseSchema.properties).forEach(property => {\r\n      const colConfig = new ActionableGridColumnConfig(property.toString());\r\n      const presentationProperties = baseSchema.properties[property].presentationProperties;\r\n      const typeProperties = baseSchema.properties[property].typeProperties;\r\n\r\n      colConfig.format.baseType = baseSchema.properties[property].type;\r\n      colConfig.format.baseFormat = baseSchema.properties[property].format;\r\n      colConfig.displayName = presentationProperties?.displayName;\r\n      colConfig.enableLookup = presentationProperties?.enableLookup;\r\n      colConfig.required = typeProperties?.required;\r\n\r\n      // setting default formats\r\n      switch (colConfig.format.baseType) {\r\n        case JsonDataTypes.String:\r\n          if (colConfig.format.baseFormat === JsonStringFormats.DateTime) {\r\n            colConfig.format.type = presentationProperties.displayTime ? EditorColumnType.DateTime : EditorColumnType.Date;\r\n          } else {\r\n            colConfig.format.type = EditorColumnType.String;\r\n          }\r\n          break;\r\n        case JsonDataTypes.Boolean:\r\n          colConfig.format.type = EditorColumnType.Checkbox;\r\n          break;\r\n        case JsonDataTypes.Decimal:\r\n        case JsonDataTypes.Integer:\r\n          colConfig.format.decimalPlaces = colConfig.format.baseType === JsonDataTypes.Integer ? 0 : presentationProperties?.decimalPlaces;\r\n          colConfig.format.type = typeProperties?.currency ? EditorColumnType.Currency : EditorColumnType.Numeric;\r\n          if (typeProperties?.currency) {\r\n            colConfig.format.currency = typeProperties.currency;\r\n          }\r\n          break;\r\n        case JsonDataTypes.Collection:\r\n          colConfig.format.type = EditorColumnType.Collection;\r\n          colConfig.children = this.mapBaseSchemaToActionableGridConfig(baseSchema.properties[property].items);\r\n          break;\r\n        case JsonDataTypes.Object:\r\n          colConfig.format.type = EditorColumnType.Object;\r\n          break;\r\n        default:\r\n          colConfig.format.type = EditorColumnType.String;\r\n          break;\r\n      }\r\n\r\n      formatSettings.push(colConfig);\r\n    });\r\n\r\n    return formatSettings;\r\n  }\r\n\r\n  private mapToFormlyField(fieldConfig: ActionableGridColumnConfig): FormlyFieldConfig {\r\n    const type = this.getFormlyRendererType(fieldConfig);\r\n    const props: any = {\r\n      label: fieldConfig.displayName || fieldConfig.column,\r\n      required: fieldConfig.required,\r\n      enableLookup: fieldConfig.enableLookup,\r\n    };\r\n\r\n    // setting properties based on type\r\n    switch (fieldConfig.format.type) {\r\n      case EditorColumnType.DateTime:\r\n        props.displayTime = true;\r\n        break;\r\n      case EditorColumnType.Currency:\r\n        props.currency = fieldConfig.format?.currency;\r\n        props.decimalPlaces = fieldConfig.format?.decimalPlaces;\r\n        break;\r\n      case EditorColumnType.Numeric:\r\n        props.decimalPlaces = fieldConfig.format?.decimalPlaces;\r\n        break;\r\n    }\r\n\r\n    if (fieldConfig.dropdown)\r\n      props.options = fieldConfig.values.map(validValue => ({ label: validValue.key, value: validValue.value }));\r\n\r\n    if (fieldConfig.defaultValue)\r\n      props.defaultValue = { isUserProfileValue: fieldConfig.defaultValue.isUserProfileValue, value: fieldConfig.defaultValue.value };\r\n\r\n    props.readonly = !fieldConfig.allowUserUpdate;\r\n\r\n\r\n    const field: FormlyFieldConfig = { key: fieldConfig.column, type, props };\r\n\r\n    if (fieldConfig.format.type === EditorColumnType.Collection)\r\n      field.fieldArray = { fieldGroup: this.getFormlyFieldsConfigByGridConfig(fieldConfig.children) };\r\n\r\n    if (fieldConfig.format.type === EditorColumnType.Object)\r\n      field.fieldGroup = this.getFormlyFieldsConfigByGridConfig(fieldConfig.children);\r\n\r\n    return field;\r\n  }\r\n\r\n  private getFormlyRendererType(fieldConfig: ActionableGridColumnConfig): FormlyRendererTypes {\r\n    switch (fieldConfig.format.type) {\r\n      case EditorColumnType.String:\r\n        return fieldConfig.dropdown ? FormlyRendererTypes.Dropdown : FormlyRendererTypes.InputText;\r\n      case EditorColumnType.Link:\r\n      case EditorColumnType.Symbol:\r\n        return FormlyRendererTypes.InputText;\r\n      case EditorColumnType.Date:\r\n      case EditorColumnType.DateTime:\r\n        return FormlyRendererTypes.Calendar;\r\n      case EditorColumnType.Numeric:\r\n      case EditorColumnType.Currency:\r\n        return FormlyRendererTypes.InputNumber;\r\n      case EditorColumnType.Checkbox:\r\n        return FormlyRendererTypes.Checkbox;\r\n      case EditorColumnType.Collection:\r\n        return FormlyRendererTypes.CollectionGrid;\r\n      case EditorColumnType.Object:\r\n        return FormlyRendererTypes.Object;\r\n      default:\r\n        return FormlyRendererTypes.None; // Default type\r\n    }\r\n  }\r\n\r\n  private getAGColFormatByFormlyField(baseProperty: ExtendedJsonSchema, field: FormlyFieldConfig): ActionableGridColumnFormat {\r\n    const colFormat = new ActionableGridColumnFormat();\r\n    colFormat.baseType = baseProperty?.type;\r\n    colFormat.baseFormat = baseProperty?.format;\r\n\r\n    switch (field.type) {\r\n      case FormlyRendererTypes.InputText:\r\n      case FormlyRendererTypes.Dropdown:\r\n      case FormlyRendererTypes.InputMask:\r\n      case FormlyRendererTypes.InputTextarea:\r\n        colFormat.type = EditorColumnType.String;\r\n        break;\r\n      case FormlyRendererTypes.Calendar:\r\n        colFormat.type = field.props?.displayTime ? EditorColumnType.DateTime : EditorColumnType.Date;\r\n        break;\r\n      case FormlyRendererTypes.Checkbox:\r\n        colFormat.type = EditorColumnType.Checkbox\r\n        break;\r\n\r\n      case FormlyRendererTypes.InputNumber:\r\n        colFormat.type = field.props?.currency ? EditorColumnType.Currency : EditorColumnType.Numeric;\r\n        colFormat.currency = field.props?.currency;\r\n        colFormat.decimalPlaces = field.props?.decimalPlaces;\r\n        break;\r\n      case FormlyRendererTypes.Object:\r\n        colFormat.type = EditorColumnType.Object;\r\n        break;\r\n      case FormlyRendererTypes.CollectionGrid:\r\n      case FormlyRendererTypes.CollectionForm:\r\n      case FormlyRendererTypes.CollectionRepeat:\r\n        colFormat.type = EditorColumnType.Collection;\r\n        break;\r\n    }\r\n    return colFormat;\r\n  }\r\n\r\n  // Returns the tools for edit, delete and file-upload\r\n  private getRowActionsTools(): ColDef {\r\n    const colDef: ColDef = {\r\n      field: '__rowActions',\r\n      headerName: '',\r\n      enableRowGroup: false,\r\n      filter: false,\r\n      floatingFilter: false,\r\n      chartDataType: 'series',\r\n      sortable: false,\r\n      width: 93,\r\n      minWidth: 50,\r\n      resizable: false,\r\n      pinned: 'left',\r\n      suppressHeaderMenuButton: true,\r\n      suppressHeaderContextMenu: true,\r\n      suppressMovable: true,\r\n      suppressColumnsToolPanel: true,\r\n      menuTabs: []\r\n    };\r\n\r\n    colDef.cellRenderer = AgGridRowActionsRendererComponent;\r\n    colDef.cellRendererParams = {\r\n      projectId: this.actionableGridService.projectIdSubject.getValue(),\r\n      versionId: this.actionableGridService.versionIdSubject.getValue(),\r\n      dataStoreName: this.actionableGridService.dataStoreNameSubject.getValue()\r\n    };\r\n\r\n    return colDef;\r\n  }\r\n\r\n  // Returns master detail arrow.\r\n  private getMasterDetailColumn(): ColDef {\r\n    return {\r\n      floatingFilter: false,\r\n      sortable: false,\r\n      resizable: false,\r\n      maxWidth: 40,\r\n      rowGroup: false,\r\n      enableRowGroup: false,\r\n      filter: false,\r\n      suppressHeaderMenuButton: true,\r\n      cellRenderer: 'agGroupCellRenderer',\r\n      suppressColumnsToolPanel: true,\r\n      suppressMovable: true,\r\n      cellRendererParams: {\r\n        suppressCount: true,\r\n      }\r\n    };\r\n  }\r\n\r\n  // Start of ColDef functions for Column Type.\r\n  private setStringColDef(options: ColDefOptions): void {\r\n    options.colDef.headerClass = (params) => options.columnConfig.required ? 'show-required' : '';\r\n    options.colDef.filter = ActionableGridColumnFilters.Text;\r\n    options.colDef.chartDataType = 'category';\r\n    options.colDef.filterParams = { defaultOption: 'startsWith' };\r\n\r\n    const cellRendererParams = {\r\n      format: options.columnConfig.format,\r\n      allowUserUpdate: options.columnConfig.allowUserUpdate,\r\n      required: options.columnConfig.required,\r\n      fieldBaseSchema: options.baseProperty,\r\n      baseSchema: options.baseSchema,\r\n      fieldName: options.colDef.field,\r\n      fieldDisplayName: options.colDef.headerName,\r\n      enableLookup: options.columnConfig.enableLookup,\r\n      projectVersionId: options.projectVersionId,\r\n    };\r\n\r\n    if (options.columnConfig.allowUserUpdate) {\r\n      if (options.columnConfig.dropdown) {\r\n        options.colDef.cellRenderer = AgGridDropDownRendererComponent;\r\n        options.colDef.cellRendererParams = { ...cellRendererParams, allowedValues: options.columnConfig.values };\r\n      } else {\r\n        options.colDef.cellRenderer = AgGridEditableRendererComponent;\r\n        options.colDef.cellRendererParams = cellRendererParams;\r\n      }\r\n    }\r\n  }\r\n\r\n  private setDateColDef(options: ColDefOptions): void {\r\n    this.setAgGridColDefRendererAndStyle(options);\r\n    options.colDef.filter = AgGridDateFilterComponent;\r\n    options.colDef.filterParams = {\r\n      showTime: options.columnConfig.format.type === EditorColumnType.DateTime,\r\n    };\r\n    options.colDef.comparator = sortingDateComparator;\r\n    options.colDef.valueFormatter = (params) => dateValueFormatter(params.value, options.columnConfig.format.type === EditorColumnType.DateTime);\r\n  }\r\n\r\n  // getExtendedDateColumns(sourceDateColDefOptions: ColDefOptions): ColDef[] {\r\n  //   const colId = sourceDateColDefOptions.colDef.colId;\r\n  //   const months = ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'];\r\n  //   const quarters = ['Q1', 'Q2', 'Q3', 'Q4'];\r\n\r\n  //   const commonColDef = {\r\n  //     enableRowGroup: true,\r\n  //     floatingFilter: sourceDateColDefOptions.showFloatingFilter,\r\n  //     sortable: true,\r\n  //     width: 100,\r\n  //     hide: true,\r\n  //   };\r\n\r\n  //   const yearSortComparator = (a: string, b: string) => {\r\n  //     return months.indexOf(a) - months.indexOf(b);\r\n  //   };\r\n  //   const quarterSortComparator = (a: string, b: string) => {\r\n  //     return quarters.indexOf(a) - quarters.indexOf(b);\r\n  //   };\r\n\r\n  //   return [\r\n  //     {\r\n  //       ...commonColDef,\r\n  //       headerName: sourceDateColDefOptions.colDef.headerName + ' (Year)',\r\n  //       colId: `${colId}_year`,\r\n  //       valueGetter: (params) => {\r\n  //         const date = new Date(params.data?.[colId]);\r\n  //         return isNaN(date.getTime()) ? null : date.getFullYear();\r\n  //       },\r\n  //     },\r\n  //     {\r\n  //       ...commonColDef,\r\n  //       headerName: sourceDateColDefOptions.colDef.headerName + ' (Quarter)',\r\n  //       colId: `${colId}_quarter`,\r\n  //       valueGetter: (params) => {\r\n  //         const date = new Date(params.data?.[colId]);\r\n  //         if (isNaN(date.getTime())) return null;\r\n  //         const quarter = Math.floor(date.getMonth() / 3) + 1;\r\n  //         return `Q${quarter}`;\r\n  //       },\r\n  //       filter: 'agSetColumnFilter',\r\n  //       filterParams: {\r\n  //         comparator: quarterSortComparator,\r\n  //       },\r\n  //       comparator: quarterSortComparator,\r\n  //       pivotComparator: quarterSortComparator,\r\n  //     },\r\n  //     {\r\n  //       ...commonColDef,\r\n  //       headerName: sourceDateColDefOptions.colDef.headerName + ' (Month)',\r\n  //       colId: `${colId}_month`,\r\n  //       valueGetter: (params) => {\r\n  //         const date = new Date(params.data?.[colId]);\r\n  //         const monthIndex = isNaN(date.getTime()) ? null : date.getMonth();\r\n  //         return monthIndex != null\r\n  //           ? months[monthIndex]\r\n  //           : null;\r\n  //       },\r\n  //       filter: 'agSetColumnFilter',\r\n  //       filterParams: {\r\n  //         comparator: yearSortComparator,\r\n  //       },\r\n  //       comparator: yearSortComparator,\r\n  //       pivotComparator: yearSortComparator,\r\n  //     }\r\n  //   ];\r\n  // }\r\n\r\n  getExtendedDateColumns(sourceDateColDefOptions: ColDefOptions): ColDef[] {\r\n  const colId = sourceDateColDefOptions.colDef.colId;\r\n  const months = [\r\n    'January', 'February', 'March', 'April', 'May', 'June',\r\n    'July', 'August', 'September', 'October', 'November', 'December'\r\n  ];\r\n\r\n  const commonColDef: Partial<ColDef> = {\r\n    enableRowGroup: true,\r\n    floatingFilter: sourceDateColDefOptions.showFloatingFilter,\r\n    sortable: true,\r\n    width: 100,\r\n    hide: true,\r\n  };\r\n\r\n  return [\r\n    {\r\n      ...commonColDef,\r\n      headerName: sourceDateColDefOptions.colDef.headerName + ' (Year)',\r\n      colId: `${colId}_year`,\r\n      valueGetter: (params) => {\r\n        const date = new Date(params.data?.[colId]);\r\n        return isNaN(date.getTime()) ? null : date.getFullYear();\r\n      },\r\n    },\r\n    {\r\n      ...commonColDef,\r\n      headerName: sourceDateColDefOptions.colDef.headerName + ' (Quarter)',\r\n      colId: `${colId}_quarter`,\r\n      valueGetter: (params) => {\r\n        const date = new Date(params.data?.[colId]);\r\n        return isNaN(date.getTime()) ? null : Math.floor(date.getMonth() / 3) + 1; // 1 to 4\r\n      },\r\n      valueFormatter: (params) =>\r\n        params.value ? `Q${params.value}` : '',\r\n      filter: 'agSetColumnFilter',\r\n      filterParams: {\r\n        values: [1, 2, 3, 4],\r\n        valueFormatter: (value: number) => `Q${value}`,\r\n        comparator: (a: number, b: number) => a - b,\r\n      },\r\n      comparator: (a: number, b: number) => a - b,\r\n      pivotComparator: (a: number, b: number) => a - b,\r\n    },\r\n    {\r\n      ...commonColDef,\r\n      headerName: sourceDateColDefOptions.colDef.headerName + ' (Month)',\r\n      colId: `${colId}_month`,\r\n      valueGetter: (params) => {\r\n        const date = new Date(params.data?.[colId]);\r\n        return isNaN(date.getTime()) ? null : date.getMonth(); // 0 to 11\r\n      },\r\n      valueFormatter: (params) =>\r\n        params.value != null ? months[params.value] : '',\r\n      filter: 'agSetColumnFilter',\r\n      filterParams: {\r\n        values: months.map((_, index) => index),\r\n        valueFormatter: (value: number) => months[value],\r\n        comparator: (a: number, b: number) => a - b,\r\n      },\r\n      comparator: (a: number, b: number) => a - b,\r\n      pivotComparator: (a: number, b: number) => a - b,\r\n    }\r\n  ];\r\n}\r\n\r\n\r\n  private setNumericColDef(options: ColDefOptions): void {\r\n    this.setAgGridColDefRendererAndStyle(options);\r\n    options.colDef.filter = ActionableGridColumnFilters.Number;\r\n    options.colDef.valueGetter = (params) => this.numericValueGetter((params.data?.[params.column?.getColId()]));\r\n\r\n    if (options.columnConfig.format.type === EditorColumnType.Currency) {\r\n      options.colDef.valueFormatter = (params) => formatCurrency(params.value, options.columnConfig.format.currency,\r\n        options.columnConfig.format.decimalPlaces);\r\n    } else {\r\n      options.colDef.valueFormatter = (params) => numericValueFormatter(params.value, options.columnConfig.format.decimalPlaces);\r\n    }\r\n  }\r\n\r\n  private setSymbolColDef(options: ColDefOptions): void {\r\n    options.colDef.headerClass = (params) => options.columnConfig.required ? 'show-required' : '';\r\n    options.colDef.cellRenderer = AgGridMessageSymbolRendererComponent;\r\n    options.colDef.cellRendererParams = {\r\n      allowedValues: options.columnConfig.values, allowUserUpdate: options.columnConfig.allowUserUpdate,\r\n      format: options.columnConfig.format, required: options.columnConfig.required,\r\n      fieldBaseSchema: options.baseProperty,\r\n    };\r\n  }\r\n\r\n  private setCheckboxColDef(options: ColDefOptions): void {\r\n    options.colDef.cellRenderer = AgGridCheckBoxRendererComponent;\r\n    options.colDef.headerClass = (params) => options.columnConfig.required ? 'show-required' : '';\r\n    if (options.columnConfig.allowUserUpdate)\r\n      options.colDef.headerComponent = AgGridHeaderCheckBoxRendererComponent;\r\n\r\n    options.colDef.cellRendererParams = {\r\n      allowedValues: options.columnConfig.values,\r\n      allowUserUpdate: options.columnConfig.allowUserUpdate,\r\n      format: options.columnConfig.format,\r\n      required: options.columnConfig.required,\r\n      fieldBaseSchema: options.baseProperty,\r\n    };\r\n  }\r\n\r\n  private async setArrayColDef(options: ColDefOptions) {\r\n    options.gridOptions.detailCellRenderer = AgGridMasterDetailRendererComponent;\r\n    options.gridOptions.masterDetail = true;\r\n    const childGridOptions = {\r\n      detailRowAutoHeight: true,\r\n      defaultColDef: {\r\n        resizable: true\r\n      },\r\n      popupParent: document.querySelector('body'),\r\n      context: {\r\n        getMainGridId: options.gridOptions.context?.getMainGridId,\r\n        getMasterRecordParams: options.gridOptions.context?.getMasterRecordParams,\r\n      } as IRowActionsParamsContext\r\n    };\r\n    const columnDefs =\r\n      await this.getColumnDefs(options.columnConfig.children,\r\n        options.baseProperty.items,\r\n        childGridOptions,\r\n        false,\r\n        options.projectVersionId,\r\n        options.allowAddNewRow,\r\n        options.columnConfig,\r\n        null,\r\n        options.bypassObjects\r\n      );\r\n\r\n    if (!options.gridOptions.detailCellRendererParams?.configs?.length)\r\n      options.gridOptions.detailCellRendererParams = { configs: [] };\r\n\r\n    options.gridOptions.detailCellRendererParams.configs.push(\r\n      new ActionableGridMasterDetailConfigOptions(\r\n        options.columnConfig,\r\n        options.baseProperty,\r\n        childGridOptions,\r\n        options.allowAddNewRow,\r\n        columnDefs,\r\n        options.projectVersionId,\r\n        options.projectId,\r\n        options.gridOptions.context?.getMainGridId,\r\n        options.gridOptions.context?.getMasterRecordParams));\r\n  }\r\n\r\n  private setLinkColDef(options: ColDefOptions): void {\r\n    options.colDef.cellRenderer = AgGridHyperlinkRendererComponent;\r\n    options.colDef.cellRendererParams = {\r\n      allowedValues: options.columnConfig.values, allowUserUpdate: options.columnConfig.allowUserUpdate,\r\n      link: options.columnConfig.link, format: options.columnConfig.format, required: options.columnConfig.required,\r\n      projectVariables: options.projectVariables, fieldBaseSchema: options.baseProperty,\r\n    };\r\n  }\r\n\r\n  private numericValueGetter(paramsValue: any) {\r\n    if (paramsValue !== undefined) {\r\n      return !isNaN(Number(paramsValue)) ? Number(paramsValue) : paramsValue;\r\n    }\r\n  }\r\n\r\n  private setButtonColDef(options: ColDefOptions): void {\r\n    const cellRendererParams = {\r\n      format: options.columnConfig.format,\r\n      allowUserUpdate: options.columnConfig.allowUserUpdate,\r\n      required: options.columnConfig.required,\r\n      fieldBaseSchema: options.baseProperty,\r\n    };\r\n\r\n    options.colDef.headerClass = (params) => options.columnConfig.required ? 'show-required' : '';\r\n    options.colDef.cellRenderer = AgGridButtonRendererComponent;\r\n    options.colDef.cellRendererParams = { ...cellRendererParams };\r\n  }\r\n\r\n  private setAgGridColDefRendererAndStyle(options: ColDefOptions) {\r\n    const cellRendererParams = {\r\n      format: options.columnConfig.format,\r\n      allowUserUpdate: options.columnConfig.allowUserUpdate,\r\n      required: options.columnConfig.required,\r\n      fieldBaseSchema: options.baseProperty,\r\n    };\r\n\r\n    if (options.columnConfig.allowUserUpdate) {\r\n      options.colDef.headerClass = (params) => (options.columnConfig.allowUserUpdate && options.columnConfig.required) ? 'show-required' : '';\r\n      options.colDef.cellRenderer = AgGridEditableRendererComponent;\r\n      options.colDef.cellRendererParams = cellRendererParams;\r\n      options.colDef.cellClass = '';\r\n    } else {\r\n      options.colDef.cellClass = 'sb-field-right-align';\r\n    }\r\n  }\r\n}\r\n"], "mappings": ";AAEA,SAASA,0BAA0B,QAAQ,iDAAiD;AAC5F,SAASC,qBAAqB,QAAQ,4CAA4C;AAClF,SAASC,kBAAkB,EAAEC,cAAc,EAAEC,qBAAqB,QAAQ,2CAA2C;AACrH,SAASC,+BAA+B,QAAQ,kEAAkE;AAClH,SAASC,+BAA+B,QAAQ,kEAAkE;AAClH,SAASC,2BAA2B,QAAQ,iDAAiD;AAC7F,SAASC,+BAA+B,QAAQ,kEAAkE;AAClH,SAASC,oCAAoC,QAAQ,sDAAsD;AAC3G,SAASC,iCAAiC,QAAQ,qEAAqE;AAEvH,SAASC,aAAa,EAAEC,iBAAiB,QAAQ,uCAAuC;AACxF,SAASC,qCAAqC,QAAQ,iFAAiF;AACvI,SAASC,gBAAgB,QAAQ,8CAA8C;AAC/E,SAASC,gCAAgC,QAAQ,mEAAmE;AAEpH,SAASC,mCAAmC,QAAQ,uEAAuE;AAC3H,SAASC,uCAAuC,QAAQ,gEAAgE;AACxH,SAASC,aAAa,QAAQ,+CAA+C;AAC7E,SAASC,oBAAoB,QAAQ,2BAA2B;AAEhE,SAASC,mBAAmB,QAAQ,6DAA6D;AACjG,SAASC,0BAA0B,QAAQ,mDAAmD;AAE9F,SAASC,6BAA6B,QAAQ,kEAAkE;AAChH,SAASC,WAAW,QAAQ,oCAAoC;AAEhE,SAASC,oCAAoC,QAAQ,kFAAkF;AAEvI,SAASC,yBAAyB,QAAQ,2DAA2D;;;;AAMrG,OAAM,MAAOC,wBAAwB;EAenCC,YACUC,eAAgC,EAAUC,qBAA4C;IAAtF,KAAAD,eAAe,GAAfA,eAAe;IAA2B,KAAAC,qBAAqB,GAArBA,qBAAqB;IAdjE,KAAAC,8BAA8B,GAAG;MACvC,CAAChB,gBAAgB,CAACiB,MAAM,GAAIC,OAAsB,IAAK,IAAI,CAACC,eAAe,CAACD,OAAO,CAAC;MACpF,CAAClB,gBAAgB,CAACoB,IAAI,GAAIF,OAAsB,IAAK,IAAI,CAACG,aAAa,CAACH,OAAO,CAAC;MAChF,CAAClB,gBAAgB,CAACsB,QAAQ,GAAIJ,OAAsB,IAAK,IAAI,CAACG,aAAa,CAACH,OAAO,CAAC;MACpF,CAAClB,gBAAgB,CAACuB,OAAO,GAAIL,OAAsB,IAAK,IAAI,CAACM,gBAAgB,CAACN,OAAO,CAAC;MACtF,CAAClB,gBAAgB,CAACyB,QAAQ,GAAIP,OAAsB,IAAK,IAAI,CAACM,gBAAgB,CAACN,OAAO,CAAC;MACvF,CAAClB,gBAAgB,CAAC0B,MAAM,GAAIR,OAAsB,IAAK,IAAI,CAACS,eAAe,CAACT,OAAO,CAAC;MACpF,CAAClB,gBAAgB,CAAC4B,QAAQ,GAAIV,OAAsB,IAAK,IAAI,CAACW,iBAAiB,CAACX,OAAO,CAAC;MACxF,CAAClB,gBAAgB,CAAC8B,UAAU,GAAIZ,OAAsB,IAAK,IAAI,CAACa,cAAc,CAACb,OAAO,CAAC;MACvF,CAAClB,gBAAgB,CAACgC,IAAI,GAAId,OAAsB,IAAK,IAAI,CAACe,aAAa,CAACf,OAAO,CAAC;MAChF,CAAClB,gBAAgB,CAACkC,MAAM,GAAIhB,OAAsB,IAAK,IAAI,CAACiB,eAAe,CAACjB,OAAO;KACpF;EAID;EAEMkB,aAAaA,CACjBC,gBAA8C,EAC9CC,UAA8B,EAC9BC,WAAwB,EACxBC,kBAA2B,EAC3BC,gBAAwB,EACxBC,cAAuB,EACvBC,YAAA,GAA2C,IAAI,EAC/CC,mBAA0C,EAC1CC,aAAa,GAAG,IAAI;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MAEpB,IAAI,CAACV,gBAAgB,EAAE;QACrB,OAAO,IAAI;MACb;MAEA,IAAIW,gBAAgB;MACpB,IAAIX,gBAAgB,EAAEY,IAAI,CAACC,YAAY,IAAIA,YAAY,CAACC,MAAM,CAACC,IAAI,KAAKpD,gBAAgB,CAACgC,IAAI,CAAC,EAAE;QAC9FgB,gBAAgB,SAASF,KAAI,CAAChC,eAAe,CAACuC,wBAAwB,CAACZ,gBAAgB,CAAC;MAC1F;MAEA,MAAMa,UAAU,GAAa,CAAC;QAAEC,KAAK,EAAE,KAAK;QAAEC,IAAI,EAAE,IAAI;QAAEC,wBAAwB,EAAE;MAAI,CAAE,CAAC,CAAC,CAAC;MAE7F;MACA,MAAMC,SAAS,GAAGrB,gBAAgB,CAACY,IAAI,CAACU,CAAC,IAAIA,CAAC,CAACR,MAAM,CAACC,IAAI,KAAKpD,gBAAgB,CAAC8B,UAAU,CAAC;MAE3F,IAAI4B,SAAS,EAAE;QACbJ,UAAU,CAACM,IAAI,CAACd,KAAI,CAACe,qBAAqB,EAAE,CAAC;MAC/C;MAEA,KAAK,MAAMC,GAAG,IAAIzB,gBAAgB,EAAE;QAClC;QACA,IAAIQ,aAAa,IAAIiB,GAAG,CAACX,MAAM,CAACC,IAAI,KAAKpD,gBAAgB,CAACkC,MAAM,EAC9D;QAEF,MAAM6B,YAAY,GAAGzB,UAAU,EAAE0B,UAAU,CAACF,GAAG,CAACG,MAAM,CAAC;QACvDH,GAAG,CAACX,MAAM,CAACe,QAAQ,GAAGH,YAAY,EAAEX,IAAI;QACxCU,GAAG,CAACX,MAAM,CAACgB,UAAU,GAAGJ,YAAY,EAAEZ,MAAM;QAE5C,IAAIP,mBAAmB,KAAKvC,oBAAoB,CAAC+D,IAAI,IAAIL,YAAY,CAACM,sBAAsB,EAAEC,aAAa,EAAE;UAC3GR,GAAG,CAACS,eAAe,GAAG,KAAK;QAC7B;QAEA,MAAMC,OAAO,GAAGT,YAAY,CAACX,IAAI,KAAKvD,aAAa,CAACiC,UAAU;QAC9D,MAAM2C,QAAQ,GAAGV,YAAY,CAACX,IAAI,KAAKvD,aAAa,CAACqC,MAAM;QAC3D,MAAMwC,MAAM,GAAW;UACrBnB,KAAK,EAAEO,GAAG,CAACG,MAAM;UACjBU,KAAK,EAAEb,GAAG,CAACG,MAAM;UACjBW,UAAU,EAAEd,GAAG,CAACe,WAAW,IAAIf,GAAG,CAACG,MAAM;UACzCa,QAAQ,EAAEhB,GAAG,CAACiB,aAAa,IAAI,CAACP,OAAO,IAAI,CAACC,QAAQ;UACpDO,cAAc,EAAE,IAAI,IAAI,CAACR,OAAO,IAAI,CAACC,QAAQ;UAC7CQ,MAAM,EAAE,CAACT,OAAO,IAAI,CAACC,QAAQ;UAC7BS,cAAc,EAAE1C,kBAAkB;UAClC2C,QAAQ,EAAE,CAACX,OAAO,IAAI,CAACC,QAAQ;UAC/BW,QAAQ,EAAE,EAAE;UACZ5B,IAAI,EAAEgB,OAAO;UACbf,wBAAwB,EAAEe,OAAO,IAAIC;SACtC;QAED,IAAIV,YAAY,CAACM,sBAAsB,EAAEC,aAAa,EACpDI,MAAM,CAACW,eAAe,GAAG3E,oCAAoC;QAE/D;QACA,IAAIiC,YAAY,EAAE4B,eAAe,KAAK,KAAK,IAAI,CAACC,OAAO,EACrDV,GAAG,CAACS,eAAe,GAAG,KAAK;QAE7B,MAAMe,aAAa,GAAG,IAAIlF,aAAa,CAAC;UAAE8C,YAAY,EAAEY,GAAG;UAAEY,MAAM;UAAEX,YAAY;UAAExB,WAAW;UAAES,gBAAgB;UAAEP,gBAAgB;UAAEC,cAAc;UAAEC,YAAY;UAAEL,UAAU;UAAEO,aAAa;UAAEL;QAAkB,CAAE,CAAC;QAClNM,KAAI,CAAC9B,8BAA8B,CAAC8C,GAAG,CAACX,MAAM,CAACC,IAAI,CAAC,CAACkC,aAAa,CAAC;QAEnEhC,UAAU,CAACM,IAAI,CAACc,MAAM,CAAC;QACvB,IAAIZ,GAAG,CAACX,MAAM,CAACC,IAAI,KAAKpD,gBAAgB,CAACoB,IAAI,IAAI0C,GAAG,CAACX,MAAM,CAACC,IAAI,KAAKpD,gBAAgB,CAACsB,QAAQ,EAAE;UAC9FgC,UAAU,CAACM,IAAI,CAAC,GAAGd,KAAI,CAACyC,sBAAsB,CAACD,aAAa,CAAC,CAAC;QAChE;MACF;MAEA,IAAI3C,YAAY,IAAI,IAAI,IAAIA,YAAY,CAAC4B,eAAe,EACtDjB,UAAU,CAACM,IAAI,CAACd,KAAI,CAAC0C,kBAAkB,EAAE,CAAC,CAAC,CAAE;MAE/C,OAAOlC,UAAU;IAAC;EACpB;EAEMmC,iCAAiCA,CACrCC,YAAiC,EACjCpD,UAA8B,EAC9BE,kBAA2B,EAC3BC,gBAAwB,EACxBG,mBAA0C;IAAA,IAAA+C,MAAA;IAAA,OAAA5C,iBAAA;MAE1C,IAAI,CAAC2C,YAAY,EACf,OAAO,IAAI;MAEb;MACA,MAAMpC,UAAU,GAAa,CAAC;QAAEC,KAAK,EAAE,KAAK;QAAEC,IAAI,EAAE,IAAI;QAAEC,wBAAwB,EAAE;MAAI,CAAE,CAAC;MAE3FiC,YAAY,CAACE,OAAO,CAACrC,KAAK,IAAG;QAC3B,MAAMQ,YAAY,GAAGzB,UAAU,EAAE0B,UAAU,CAACT,KAAK,CAACsC,GAAa,CAAC;QAChE,MAAMC,WAAW,GAAG,IAAI5G,0BAA0B,CAACqE,KAAK,CAACsC,GAAa,CAAC;QACvEC,WAAW,CAAC3C,MAAM,GAAGwC,MAAI,CAACI,2BAA2B,CAAChC,YAAY,EAAER,KAAK,CAAC;QAC1EuC,WAAW,CAACjB,WAAW,GAAGtB,KAAK,CAACyC,KAAK,CAACC,KAAK;QAC3CH,WAAW,CAACI,YAAY,GAAG3C,KAAK,CAACyC,KAAK,CAACE,YAAY;QAEnD;QACAJ,WAAW,CAACK,QAAQ,GAAI5C,KAAK,CAACyC,KAAK,EAAE9E,OAAiB,EAAEkF,MAAM,GAAG,CAAC;QAClE,IAAIN,WAAW,CAACK,QAAQ,EACtBL,WAAW,CAACO,MAAM,GAAG,CAAC9C,KAAK,CAACyC,KAAK,EAAE9E,OAAiB,EAACoF,GAAG,CAAC3C,CAAC,IAAG;UAAG,OAAO,IAAIlD,WAAW,CAACkD,CAAC,EAAEA,CAAC,CAAC;QAAE,CAAC,CAAC;QAElGmC,WAAW,CAACvB,eAAe,GAAGhB,KAAK,CAACyC,KAAK,EAAEO,QAAQ,KAAK,IAAI,IAAIhD,KAAK,CAACyC,KAAK,EAAEQ,QAAQ,KAAK,IAAI,IAAI,CAACzC,YAAY,CAACM,sBAAsB,EAAEC,aAAa;QACrJwB,WAAW,CAACW,QAAQ,GAAGlD,KAAK,CAACyC,KAAK,EAAES,QAAQ;QAE5C,IAAI7D,mBAAmB,KAAKvC,oBAAoB,CAAC+D,IAAI,EAAE;UACrD0B,WAAW,CAACvB,eAAe,GAAG,KAAK;QACrC;QAEA,MAAMG,MAAM,GAAW;UACrBnB,KAAK,EAAEuC,WAAW,CAAC7B,MAAM;UACzBW,UAAU,EAAEkB,WAAW,CAACjB,WAAW,IAAIiB,WAAW,CAAC7B,MAAM;UACzDa,QAAQ,EAAEgB,WAAW,CAACf,aAAa;UACnCC,cAAc,EAAE,IAAI;UACpBC,MAAM,EAAE,IAAI;UACZC,cAAc,EAAE1C,kBAAkB;UAClC2C,QAAQ,EAAE,IAAI;UACdC,QAAQ,EAAE,GAAG;UACb5B,IAAI,EAAEsC,WAAW,CAACf;SACnB;QAED,MAAMO,aAAa,GAAG,IAAIlF,aAAa,CAAC;UAAE8C,YAAY,EAAE4C,WAAW;UAAEpB,MAAM;UAAEX,YAAY;UAAEtB,gBAAgB;UAAEH;QAAU,CAAE,CAAC;QAC1H,IAAI,CAACtC,gBAAgB,CAACkC,MAAM,EAAElC,gBAAgB,CAAC8B,UAAU,CAAC,CAAC4E,QAAQ,CAACZ,WAAW,CAAC3C,MAAM,CAACC,IAAI,CAAC,EAC1FuC,MAAI,CAACxD,eAAe,CAACmD,aAAa,CAAC,CAAC,KAEpCK,MAAI,CAAC3E,8BAA8B,CAAC8E,WAAW,CAAC3C,MAAM,CAACC,IAAI,CAAC,CAACkC,aAAa,CAAC;QAE7E,IAAIZ,MAAM,CAACiC,kBAA+C,EACvDjC,MAAM,CAACiC,kBAAgD,CAACC,WAAW,GAAG,KAAK;QAE9EtD,UAAU,CAACM,IAAI,CAACc,MAAM,CAAC;MACzB,CAAC,CAAC;MAEF,IAAI9B,mBAAmB,IAAIvC,oBAAoB,CAAC+D,IAAI,EAClDd,UAAU,CAACM,IAAI,CAAC+B,MAAI,CAACH,kBAAkB,EAAE,CAAC;MAE5C,OAAOlC,UAAU;IAAC;EACpB;EAEAuD,iCAAiCA,CAACC,MAAoC;IACpE,IAAI,CAACA,MAAM,EACT,OAAO,EAAE;IAEX,MAAMC,MAAM,GAAwB,EAAE;IAEtC,KAAK,MAAMC,WAAW,IAAIF,MAAM,EAAE;MAChC,MAAMvD,KAAK,GAAsB,IAAI,CAAC0D,gBAAgB,CAACD,WAAW,CAAC;MACnED,MAAM,CAACnD,IAAI,CAACL,KAAK,CAAC;IACpB;IAEA,OAAOwD,MAAM;EACf;EAEAG,mCAAmCA,CAAC5E,UAA8B;IAChE,MAAM6E,cAAc,GAAiC,EAAE;IAEvDjF,MAAM,CAACkF,IAAI,CAAC9E,UAAU,CAAC0B,UAAU,CAAC,CAAC4B,OAAO,CAACyB,QAAQ,IAAG;MACpD,MAAMC,SAAS,GAAG,IAAIpI,0BAA0B,CAACmI,QAAQ,CAACE,QAAQ,EAAE,CAAC;MACrE,MAAMlD,sBAAsB,GAAG/B,UAAU,CAAC0B,UAAU,CAACqD,QAAQ,CAAC,CAAChD,sBAAsB;MACrF,MAAMmD,cAAc,GAAGlF,UAAU,CAAC0B,UAAU,CAACqD,QAAQ,CAAC,CAACG,cAAc;MAErEF,SAAS,CAACnE,MAAM,CAACe,QAAQ,GAAG5B,UAAU,CAAC0B,UAAU,CAACqD,QAAQ,CAAC,CAACjE,IAAI;MAChEkE,SAAS,CAACnE,MAAM,CAACgB,UAAU,GAAG7B,UAAU,CAAC0B,UAAU,CAACqD,QAAQ,CAAC,CAAClE,MAAM;MACpEmE,SAAS,CAACzC,WAAW,GAAGR,sBAAsB,EAAEQ,WAAW;MAC3DyC,SAAS,CAACpB,YAAY,GAAG7B,sBAAsB,EAAE6B,YAAY;MAC7DoB,SAAS,CAACb,QAAQ,GAAGe,cAAc,EAAEf,QAAQ;MAE7C;MACA,QAAQa,SAAS,CAACnE,MAAM,CAACe,QAAQ;QAC/B,KAAKrE,aAAa,CAACoB,MAAM;UACvB,IAAIqG,SAAS,CAACnE,MAAM,CAACgB,UAAU,KAAKrE,iBAAiB,CAACwB,QAAQ,EAAE;YAC9DgG,SAAS,CAACnE,MAAM,CAACC,IAAI,GAAGiB,sBAAsB,CAACoD,WAAW,GAAGzH,gBAAgB,CAACsB,QAAQ,GAAGtB,gBAAgB,CAACoB,IAAI;UAChH,CAAC,MAAM;YACLkG,SAAS,CAACnE,MAAM,CAACC,IAAI,GAAGpD,gBAAgB,CAACiB,MAAM;UACjD;UACA;QACF,KAAKpB,aAAa,CAAC6H,OAAO;UACxBJ,SAAS,CAACnE,MAAM,CAACC,IAAI,GAAGpD,gBAAgB,CAAC4B,QAAQ;UACjD;QACF,KAAK/B,aAAa,CAAC8H,OAAO;QAC1B,KAAK9H,aAAa,CAAC+H,OAAO;UACxBN,SAAS,CAACnE,MAAM,CAAC0E,aAAa,GAAGP,SAAS,CAACnE,MAAM,CAACe,QAAQ,KAAKrE,aAAa,CAAC+H,OAAO,GAAG,CAAC,GAAGvD,sBAAsB,EAAEwD,aAAa;UAChIP,SAAS,CAACnE,MAAM,CAACC,IAAI,GAAGoE,cAAc,EAAEM,QAAQ,GAAG9H,gBAAgB,CAACyB,QAAQ,GAAGzB,gBAAgB,CAACuB,OAAO;UACvG,IAAIiG,cAAc,EAAEM,QAAQ,EAAE;YAC5BR,SAAS,CAACnE,MAAM,CAAC2E,QAAQ,GAAGN,cAAc,CAACM,QAAQ;UACrD;UACA;QACF,KAAKjI,aAAa,CAACiC,UAAU;UAC3BwF,SAAS,CAACnE,MAAM,CAACC,IAAI,GAAGpD,gBAAgB,CAAC8B,UAAU;UACnDwF,SAAS,CAACS,QAAQ,GAAG,IAAI,CAACb,mCAAmC,CAAC5E,UAAU,CAAC0B,UAAU,CAACqD,QAAQ,CAAC,CAACW,KAAK,CAAC;UACpG;QACF,KAAKnI,aAAa,CAACqC,MAAM;UACvBoF,SAAS,CAACnE,MAAM,CAACC,IAAI,GAAGpD,gBAAgB,CAACkC,MAAM;UAC/C;QACF;UACEoF,SAAS,CAACnE,MAAM,CAACC,IAAI,GAAGpD,gBAAgB,CAACiB,MAAM;UAC/C;MACJ;MAEAkG,cAAc,CAACvD,IAAI,CAAC0D,SAAS,CAAC;IAChC,CAAC,CAAC;IAEF,OAAOH,cAAc;EACvB;EAEQF,gBAAgBA,CAACD,WAAuC;IAC9D,MAAM5D,IAAI,GAAG,IAAI,CAAC6E,qBAAqB,CAACjB,WAAW,CAAC;IACpD,MAAMhB,KAAK,GAAQ;MACjBC,KAAK,EAAEe,WAAW,CAACnC,WAAW,IAAImC,WAAW,CAAC/C,MAAM;MACpDwC,QAAQ,EAAEO,WAAW,CAACP,QAAQ;MAC9BP,YAAY,EAAEc,WAAW,CAACd;KAC3B;IAED;IACA,QAAQc,WAAW,CAAC7D,MAAM,CAACC,IAAI;MAC7B,KAAKpD,gBAAgB,CAACsB,QAAQ;QAC5B0E,KAAK,CAACyB,WAAW,GAAG,IAAI;QACxB;MACF,KAAKzH,gBAAgB,CAACyB,QAAQ;QAC5BuE,KAAK,CAAC8B,QAAQ,GAAGd,WAAW,CAAC7D,MAAM,EAAE2E,QAAQ;QAC7C9B,KAAK,CAAC6B,aAAa,GAAGb,WAAW,CAAC7D,MAAM,EAAE0E,aAAa;QACvD;MACF,KAAK7H,gBAAgB,CAACuB,OAAO;QAC3ByE,KAAK,CAAC6B,aAAa,GAAGb,WAAW,CAAC7D,MAAM,EAAE0E,aAAa;QACvD;IACJ;IAEA,IAAIb,WAAW,CAACb,QAAQ,EACtBH,KAAK,CAAC9E,OAAO,GAAG8F,WAAW,CAACX,MAAM,CAACC,GAAG,CAAC4B,UAAU,KAAK;MAAEjC,KAAK,EAAEiC,UAAU,CAACrC,GAAG;MAAEsC,KAAK,EAAED,UAAU,CAACC;IAAK,CAAE,CAAC,CAAC;IAE5G,IAAInB,WAAW,CAACoB,YAAY,EAC1BpC,KAAK,CAACoC,YAAY,GAAG;MAAEC,kBAAkB,EAAErB,WAAW,CAACoB,YAAY,CAACC,kBAAkB;MAAEF,KAAK,EAAEnB,WAAW,CAACoB,YAAY,CAACD;IAAK,CAAE;IAEjInC,KAAK,CAACO,QAAQ,GAAG,CAACS,WAAW,CAACzC,eAAe;IAG7C,MAAMhB,KAAK,GAAsB;MAAEsC,GAAG,EAAEmB,WAAW,CAAC/C,MAAM;MAAEb,IAAI;MAAE4C;IAAK,CAAE;IAEzE,IAAIgB,WAAW,CAAC7D,MAAM,CAACC,IAAI,KAAKpD,gBAAgB,CAAC8B,UAAU,EACzDyB,KAAK,CAAC+E,UAAU,GAAG;MAAEC,UAAU,EAAE,IAAI,CAAC1B,iCAAiC,CAACG,WAAW,CAACe,QAAQ;IAAC,CAAE;IAEjG,IAAIf,WAAW,CAAC7D,MAAM,CAACC,IAAI,KAAKpD,gBAAgB,CAACkC,MAAM,EACrDqB,KAAK,CAACgF,UAAU,GAAG,IAAI,CAAC1B,iCAAiC,CAACG,WAAW,CAACe,QAAQ,CAAC;IAEjF,OAAOxE,KAAK;EACd;EAEQ0E,qBAAqBA,CAACjB,WAAuC;IACnE,QAAQA,WAAW,CAAC7D,MAAM,CAACC,IAAI;MAC7B,KAAKpD,gBAAgB,CAACiB,MAAM;QAC1B,OAAO+F,WAAW,CAACb,QAAQ,GAAG7F,mBAAmB,CAACkI,QAAQ,GAAGlI,mBAAmB,CAACmI,SAAS;MAC5F,KAAKzI,gBAAgB,CAACgC,IAAI;MAC1B,KAAKhC,gBAAgB,CAAC0B,MAAM;QAC1B,OAAOpB,mBAAmB,CAACmI,SAAS;MACtC,KAAKzI,gBAAgB,CAACoB,IAAI;MAC1B,KAAKpB,gBAAgB,CAACsB,QAAQ;QAC5B,OAAOhB,mBAAmB,CAACoI,QAAQ;MACrC,KAAK1I,gBAAgB,CAACuB,OAAO;MAC7B,KAAKvB,gBAAgB,CAACyB,QAAQ;QAC5B,OAAOnB,mBAAmB,CAACqI,WAAW;MACxC,KAAK3I,gBAAgB,CAAC4B,QAAQ;QAC5B,OAAOtB,mBAAmB,CAACsB,QAAQ;MACrC,KAAK5B,gBAAgB,CAAC8B,UAAU;QAC9B,OAAOxB,mBAAmB,CAACsI,cAAc;MAC3C,KAAK5I,gBAAgB,CAACkC,MAAM;QAC1B,OAAO5B,mBAAmB,CAAC4B,MAAM;MACnC;QACE,OAAO5B,mBAAmB,CAACuI,IAAI;MAAE;IACrC;EACF;EAEQ9C,2BAA2BA,CAAChC,YAAgC,EAAER,KAAwB;IAC5F,MAAMuF,SAAS,GAAG,IAAIvI,0BAA0B,EAAE;IAClDuI,SAAS,CAAC5E,QAAQ,GAAGH,YAAY,EAAEX,IAAI;IACvC0F,SAAS,CAAC3E,UAAU,GAAGJ,YAAY,EAAEZ,MAAM;IAE3C,QAAQI,KAAK,CAACH,IAAI;MAChB,KAAK9C,mBAAmB,CAACmI,SAAS;MAClC,KAAKnI,mBAAmB,CAACkI,QAAQ;MACjC,KAAKlI,mBAAmB,CAACyI,SAAS;MAClC,KAAKzI,mBAAmB,CAAC0I,aAAa;QACpCF,SAAS,CAAC1F,IAAI,GAAGpD,gBAAgB,CAACiB,MAAM;QACxC;MACF,KAAKX,mBAAmB,CAACoI,QAAQ;QAC/BI,SAAS,CAAC1F,IAAI,GAAGG,KAAK,CAACyC,KAAK,EAAEyB,WAAW,GAAGzH,gBAAgB,CAACsB,QAAQ,GAAGtB,gBAAgB,CAACoB,IAAI;QAC7F;MACF,KAAKd,mBAAmB,CAACsB,QAAQ;QAC/BkH,SAAS,CAAC1F,IAAI,GAAGpD,gBAAgB,CAAC4B,QAAQ;QAC1C;MAEF,KAAKtB,mBAAmB,CAACqI,WAAW;QAClCG,SAAS,CAAC1F,IAAI,GAAGG,KAAK,CAACyC,KAAK,EAAE8B,QAAQ,GAAG9H,gBAAgB,CAACyB,QAAQ,GAAGzB,gBAAgB,CAACuB,OAAO;QAC7FuH,SAAS,CAAChB,QAAQ,GAAGvE,KAAK,CAACyC,KAAK,EAAE8B,QAAQ;QAC1CgB,SAAS,CAACjB,aAAa,GAAGtE,KAAK,CAACyC,KAAK,EAAE6B,aAAa;QACpD;MACF,KAAKvH,mBAAmB,CAAC4B,MAAM;QAC7B4G,SAAS,CAAC1F,IAAI,GAAGpD,gBAAgB,CAACkC,MAAM;QACxC;MACF,KAAK5B,mBAAmB,CAACsI,cAAc;MACvC,KAAKtI,mBAAmB,CAAC2I,cAAc;MACvC,KAAK3I,mBAAmB,CAAC4I,gBAAgB;QACvCJ,SAAS,CAAC1F,IAAI,GAAGpD,gBAAgB,CAAC8B,UAAU;QAC5C;IACJ;IACA,OAAOgH,SAAS;EAClB;EAEA;EACQtD,kBAAkBA,CAAA;IACxB,MAAMd,MAAM,GAAW;MACrBnB,KAAK,EAAE,cAAc;MACrBqB,UAAU,EAAE,EAAE;MACdI,cAAc,EAAE,KAAK;MACrBC,MAAM,EAAE,KAAK;MACbC,cAAc,EAAE,KAAK;MACrBiE,aAAa,EAAE,QAAQ;MACvBhE,QAAQ,EAAE,KAAK;MACfiE,KAAK,EAAE,EAAE;MACThE,QAAQ,EAAE,EAAE;MACZiE,SAAS,EAAE,KAAK;MAChBC,MAAM,EAAE,MAAM;MACdC,wBAAwB,EAAE,IAAI;MAC9BC,yBAAyB,EAAE,IAAI;MAC/BC,eAAe,EAAE,IAAI;MACrBhG,wBAAwB,EAAE,IAAI;MAC9BiG,QAAQ,EAAE;KACX;IAEDhF,MAAM,CAACiF,YAAY,GAAG/J,iCAAiC;IACvD8E,MAAM,CAACiC,kBAAkB,GAAG;MAC1BiD,SAAS,EAAE,IAAI,CAAC7I,qBAAqB,CAAC8I,gBAAgB,CAACC,QAAQ,EAAE;MACjEC,SAAS,EAAE,IAAI,CAAChJ,qBAAqB,CAACiJ,gBAAgB,CAACF,QAAQ,EAAE;MACjEG,aAAa,EAAE,IAAI,CAAClJ,qBAAqB,CAACmJ,oBAAoB,CAACJ,QAAQ;KACxE;IAED,OAAOpF,MAAM;EACf;EAEA;EACQb,qBAAqBA,CAAA;IAC3B,OAAO;MACLqB,cAAc,EAAE,KAAK;MACrBC,QAAQ,EAAE,KAAK;MACfkE,SAAS,EAAE,KAAK;MAChBc,QAAQ,EAAE,EAAE;MACZrF,QAAQ,EAAE,KAAK;MACfE,cAAc,EAAE,KAAK;MACrBC,MAAM,EAAE,KAAK;MACbsE,wBAAwB,EAAE,IAAI;MAC9BI,YAAY,EAAE,qBAAqB;MACnClG,wBAAwB,EAAE,IAAI;MAC9BgG,eAAe,EAAE,IAAI;MACrB9C,kBAAkB,EAAE;QAClByD,aAAa,EAAE;;KAElB;EACH;EAEA;EACQjJ,eAAeA,CAACD,OAAsB;IAC5CA,OAAO,CAACwD,MAAM,CAAC2F,WAAW,GAAIC,MAAM,IAAKpJ,OAAO,CAACgC,YAAY,CAACuD,QAAQ,GAAG,eAAe,GAAG,EAAE;IAC7FvF,OAAO,CAACwD,MAAM,CAACO,MAAM,GAAGxF,2BAA2B,CAAC8K,IAAI;IACxDrJ,OAAO,CAACwD,MAAM,CAACyE,aAAa,GAAG,UAAU;IACzCjI,OAAO,CAACwD,MAAM,CAAC8F,YAAY,GAAG;MAAEC,aAAa,EAAE;IAAY,CAAE;IAE7D,MAAM9D,kBAAkB,GAAG;MACzBxD,MAAM,EAAEjC,OAAO,CAACgC,YAAY,CAACC,MAAM;MACnCoB,eAAe,EAAErD,OAAO,CAACgC,YAAY,CAACqB,eAAe;MACrDkC,QAAQ,EAAEvF,OAAO,CAACgC,YAAY,CAACuD,QAAQ;MACvCiE,eAAe,EAAExJ,OAAO,CAAC6C,YAAY;MACrCzB,UAAU,EAAEpB,OAAO,CAACoB,UAAU;MAC9BqI,SAAS,EAAEzJ,OAAO,CAACwD,MAAM,CAACnB,KAAK;MAC/BqH,gBAAgB,EAAE1J,OAAO,CAACwD,MAAM,CAACE,UAAU;MAC3CsB,YAAY,EAAEhF,OAAO,CAACgC,YAAY,CAACgD,YAAY;MAC/CzD,gBAAgB,EAAEvB,OAAO,CAACuB;KAC3B;IAED,IAAIvB,OAAO,CAACgC,YAAY,CAACqB,eAAe,EAAE;MACxC,IAAIrD,OAAO,CAACgC,YAAY,CAACiD,QAAQ,EAAE;QACjCjF,OAAO,CAACwD,MAAM,CAACiF,YAAY,GAAGnK,+BAA+B;QAC7D0B,OAAO,CAACwD,MAAM,CAACiC,kBAAkB,GAAG;UAAE,GAAGA,kBAAkB;UAAEkE,aAAa,EAAE3J,OAAO,CAACgC,YAAY,CAACmD;QAAM,CAAE;MAC3G,CAAC,MAAM;QACLnF,OAAO,CAACwD,MAAM,CAACiF,YAAY,GAAGjK,+BAA+B;QAC7DwB,OAAO,CAACwD,MAAM,CAACiC,kBAAkB,GAAGA,kBAAkB;MACxD;IACF;EACF;EAEQtF,aAAaA,CAACH,OAAsB;IAC1C,IAAI,CAAC4J,+BAA+B,CAAC5J,OAAO,CAAC;IAC7CA,OAAO,CAACwD,MAAM,CAACO,MAAM,GAAGtE,yBAAyB;IACjDO,OAAO,CAACwD,MAAM,CAAC8F,YAAY,GAAG;MAC5BO,QAAQ,EAAE7J,OAAO,CAACgC,YAAY,CAACC,MAAM,CAACC,IAAI,KAAKpD,gBAAgB,CAACsB;KACjE;IACDJ,OAAO,CAACwD,MAAM,CAACsG,UAAU,GAAG7L,qBAAqB;IACjD+B,OAAO,CAACwD,MAAM,CAACuG,cAAc,GAAIX,MAAM,IAAKlL,kBAAkB,CAACkL,MAAM,CAACnC,KAAK,EAAEjH,OAAO,CAACgC,YAAY,CAACC,MAAM,CAACC,IAAI,KAAKpD,gBAAgB,CAACsB,QAAQ,CAAC;EAC9I;EAEA;EACA;EACA;EACA;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EAEA;EACA;EACA;EACA;EACA;EACA;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAEAiE,sBAAsBA,CAAC2F,uBAAsC;IAC7D,MAAMvG,KAAK,GAAGuG,uBAAuB,CAACxG,MAAM,CAACC,KAAK;IAClD,MAAMwG,MAAM,GAAG,CACb,SAAS,EAAE,UAAU,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EACtD,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,SAAS,EAAE,UAAU,EAAE,UAAU,CACjE;IAED,MAAMC,YAAY,GAAoB;MACpCpG,cAAc,EAAE,IAAI;MACpBE,cAAc,EAAEgG,uBAAuB,CAAC1I,kBAAkB;MAC1D2C,QAAQ,EAAE,IAAI;MACdiE,KAAK,EAAE,GAAG;MACV5F,IAAI,EAAE;KACP;IAED,OAAO,CACL;MACE,GAAG4H,YAAY;MACfxG,UAAU,EAAEsG,uBAAuB,CAACxG,MAAM,CAACE,UAAU,GAAG,SAAS;MACjED,KAAK,EAAE,GAAGA,KAAK,OAAO;MACtB0G,WAAW,EAAGf,MAAM,IAAI;QACtB,MAAMgB,IAAI,GAAG,IAAIlK,IAAI,CAACkJ,MAAM,CAACiB,IAAI,GAAG5G,KAAK,CAAC,CAAC;QAC3C,OAAO6G,KAAK,CAACF,IAAI,CAACG,OAAO,EAAE,CAAC,GAAG,IAAI,GAAGH,IAAI,CAACI,WAAW,EAAE;MAC1D;KACD,EACD;MACE,GAAGN,YAAY;MACfxG,UAAU,EAAEsG,uBAAuB,CAACxG,MAAM,CAACE,UAAU,GAAG,YAAY;MACpED,KAAK,EAAE,GAAGA,KAAK,UAAU;MACzB0G,WAAW,EAAGf,MAAM,IAAI;QACtB,MAAMgB,IAAI,GAAG,IAAIlK,IAAI,CAACkJ,MAAM,CAACiB,IAAI,GAAG5G,KAAK,CAAC,CAAC;QAC3C,OAAO6G,KAAK,CAACF,IAAI,CAACG,OAAO,EAAE,CAAC,GAAG,IAAI,GAAGE,IAAI,CAACC,KAAK,CAACN,IAAI,CAACO,QAAQ,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;MAC7E,CAAC;MACDZ,cAAc,EAAGX,MAAM,IACrBA,MAAM,CAACnC,KAAK,GAAG,IAAImC,MAAM,CAACnC,KAAK,EAAE,GAAG,EAAE;MACxClD,MAAM,EAAE,mBAAmB;MAC3BuF,YAAY,EAAE;QACZnE,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QACpB4E,cAAc,EAAG9C,KAAa,IAAK,IAAIA,KAAK,EAAE;QAC9C6C,UAAU,EAAEA,CAACc,CAAS,EAAEC,CAAS,KAAKD,CAAC,GAAGC;OAC3C;MACDf,UAAU,EAAEA,CAACc,CAAS,EAAEC,CAAS,KAAKD,CAAC,GAAGC,CAAC;MAC3CC,eAAe,EAAEA,CAACF,CAAS,EAAEC,CAAS,KAAKD,CAAC,GAAGC;KAChD,EACD;MACE,GAAGX,YAAY;MACfxG,UAAU,EAAEsG,uBAAuB,CAACxG,MAAM,CAACE,UAAU,GAAG,UAAU;MAClED,KAAK,EAAE,GAAGA,KAAK,QAAQ;MACvB0G,WAAW,EAAGf,MAAM,IAAI;QACtB,MAAMgB,IAAI,GAAG,IAAIlK,IAAI,CAACkJ,MAAM,CAACiB,IAAI,GAAG5G,KAAK,CAAC,CAAC;QAC3C,OAAO6G,KAAK,CAACF,IAAI,CAACG,OAAO,EAAE,CAAC,GAAG,IAAI,GAAGH,IAAI,CAACO,QAAQ,EAAE,CAAC,CAAC;MACzD,CAAC;MACDZ,cAAc,EAAGX,MAAM,IACrBA,MAAM,CAACnC,KAAK,IAAI,IAAI,GAAGgD,MAAM,CAACb,MAAM,CAACnC,KAAK,CAAC,GAAG,EAAE;MAClDlD,MAAM,EAAE,mBAAmB;MAC3BuF,YAAY,EAAE;QACZnE,MAAM,EAAE8E,MAAM,CAAC7E,GAAG,CAAC,CAAC2F,CAAC,EAAEC,KAAK,KAAKA,KAAK,CAAC;QACvCjB,cAAc,EAAG9C,KAAa,IAAKgD,MAAM,CAAChD,KAAK,CAAC;QAChD6C,UAAU,EAAEA,CAACc,CAAS,EAAEC,CAAS,KAAKD,CAAC,GAAGC;OAC3C;MACDf,UAAU,EAAEA,CAACc,CAAS,EAAEC,CAAS,KAAKD,CAAC,GAAGC,CAAC;MAC3CC,eAAe,EAAEA,CAACF,CAAS,EAAEC,CAAS,KAAKD,CAAC,GAAGC;KAChD,CACF;EACH;EAGUvK,gBAAgBA,CAACN,OAAsB;IAC7C,IAAI,CAAC4J,+BAA+B,CAAC5J,OAAO,CAAC;IAC7CA,OAAO,CAACwD,MAAM,CAACO,MAAM,GAAGxF,2BAA2B,CAAC0M,MAAM;IAC1DjL,OAAO,CAACwD,MAAM,CAAC2G,WAAW,GAAIf,MAAM,IAAK,IAAI,CAAC8B,kBAAkB,CAAE9B,MAAM,CAACiB,IAAI,GAAGjB,MAAM,CAACrG,MAAM,EAAEoI,QAAQ,EAAE,CAAE,CAAC;IAE5G,IAAInL,OAAO,CAACgC,YAAY,CAACC,MAAM,CAACC,IAAI,KAAKpD,gBAAgB,CAACyB,QAAQ,EAAE;MAClEP,OAAO,CAACwD,MAAM,CAACuG,cAAc,GAAIX,MAAM,IAAKjL,cAAc,CAACiL,MAAM,CAACnC,KAAK,EAAEjH,OAAO,CAACgC,YAAY,CAACC,MAAM,CAAC2E,QAAQ,EAC3G5G,OAAO,CAACgC,YAAY,CAACC,MAAM,CAAC0E,aAAa,CAAC;IAC9C,CAAC,MAAM;MACL3G,OAAO,CAACwD,MAAM,CAACuG,cAAc,GAAIX,MAAM,IAAKhL,qBAAqB,CAACgL,MAAM,CAACnC,KAAK,EAAEjH,OAAO,CAACgC,YAAY,CAACC,MAAM,CAAC0E,aAAa,CAAC;IAC5H;EACF;EAEQlG,eAAeA,CAACT,OAAsB;IAC5CA,OAAO,CAACwD,MAAM,CAAC2F,WAAW,GAAIC,MAAM,IAAKpJ,OAAO,CAACgC,YAAY,CAACuD,QAAQ,GAAG,eAAe,GAAG,EAAE;IAC7FvF,OAAO,CAACwD,MAAM,CAACiF,YAAY,GAAGhK,oCAAoC;IAClEuB,OAAO,CAACwD,MAAM,CAACiC,kBAAkB,GAAG;MAClCkE,aAAa,EAAE3J,OAAO,CAACgC,YAAY,CAACmD,MAAM;MAAE9B,eAAe,EAAErD,OAAO,CAACgC,YAAY,CAACqB,eAAe;MACjGpB,MAAM,EAAEjC,OAAO,CAACgC,YAAY,CAACC,MAAM;MAAEsD,QAAQ,EAAEvF,OAAO,CAACgC,YAAY,CAACuD,QAAQ;MAC5EiE,eAAe,EAAExJ,OAAO,CAAC6C;KAC1B;EACH;EAEQlC,iBAAiBA,CAACX,OAAsB;IAC9CA,OAAO,CAACwD,MAAM,CAACiF,YAAY,GAAGpK,+BAA+B;IAC7D2B,OAAO,CAACwD,MAAM,CAAC2F,WAAW,GAAIC,MAAM,IAAKpJ,OAAO,CAACgC,YAAY,CAACuD,QAAQ,GAAG,eAAe,GAAG,EAAE;IAC7F,IAAIvF,OAAO,CAACgC,YAAY,CAACqB,eAAe,EACtCrD,OAAO,CAACwD,MAAM,CAACW,eAAe,GAAGtF,qCAAqC;IAExEmB,OAAO,CAACwD,MAAM,CAACiC,kBAAkB,GAAG;MAClCkE,aAAa,EAAE3J,OAAO,CAACgC,YAAY,CAACmD,MAAM;MAC1C9B,eAAe,EAAErD,OAAO,CAACgC,YAAY,CAACqB,eAAe;MACrDpB,MAAM,EAAEjC,OAAO,CAACgC,YAAY,CAACC,MAAM;MACnCsD,QAAQ,EAAEvF,OAAO,CAACgC,YAAY,CAACuD,QAAQ;MACvCiE,eAAe,EAAExJ,OAAO,CAAC6C;KAC1B;EACH;EAEchC,cAAcA,CAACb,OAAsB;IAAA,IAAAoL,MAAA;IAAA,OAAAvJ,iBAAA;MACjD7B,OAAO,CAACqB,WAAW,CAACgK,kBAAkB,GAAGrM,mCAAmC;MAC5EgB,OAAO,CAACqB,WAAW,CAACiK,YAAY,GAAG,IAAI;MACvC,MAAMC,gBAAgB,GAAG;QACvBC,mBAAmB,EAAE,IAAI;QACzBC,aAAa,EAAE;UACbtD,SAAS,EAAE;SACZ;QACDuD,WAAW,EAAEC,QAAQ,CAACC,aAAa,CAAC,MAAM,CAAC;QAC3CC,OAAO,EAAE;UACPC,aAAa,EAAE9L,OAAO,CAACqB,WAAW,CAACwK,OAAO,EAAEC,aAAa;UACzDC,qBAAqB,EAAE/L,OAAO,CAACqB,WAAW,CAACwK,OAAO,EAAEE;;OAEvD;MACD,MAAM3J,UAAU,SACRgJ,MAAI,CAAClK,aAAa,CAAClB,OAAO,CAACgC,YAAY,CAAC6E,QAAQ,EACpD7G,OAAO,CAAC6C,YAAY,CAACiE,KAAK,EAC1ByE,gBAAgB,EAChB,KAAK,EACLvL,OAAO,CAACuB,gBAAgB,EACxBvB,OAAO,CAACwB,cAAc,EACtBxB,OAAO,CAACgC,YAAY,EACpB,IAAI,EACJhC,OAAO,CAAC2B,aAAa,CACtB;MAEH,IAAI,CAAC3B,OAAO,CAACqB,WAAW,CAAC2K,wBAAwB,EAAEC,OAAO,EAAE/G,MAAM,EAChElF,OAAO,CAACqB,WAAW,CAAC2K,wBAAwB,GAAG;QAAEC,OAAO,EAAE;MAAE,CAAE;MAEhEjM,OAAO,CAACqB,WAAW,CAAC2K,wBAAwB,CAACC,OAAO,CAACvJ,IAAI,CACvD,IAAIzD,uCAAuC,CACzCe,OAAO,CAACgC,YAAY,EACpBhC,OAAO,CAAC6C,YAAY,EACpB0I,gBAAgB,EAChBvL,OAAO,CAACwB,cAAc,EACtBY,UAAU,EACVpC,OAAO,CAACuB,gBAAgB,EACxBvB,OAAO,CAAC0I,SAAS,EACjB1I,OAAO,CAACqB,WAAW,CAACwK,OAAO,EAAEC,aAAa,EAC1C9L,OAAO,CAACqB,WAAW,CAACwK,OAAO,EAAEE,qBAAqB,CAAC,CAAC;IAAC;EAC3D;EAEQhL,aAAaA,CAACf,OAAsB;IAC1CA,OAAO,CAACwD,MAAM,CAACiF,YAAY,GAAG1J,gCAAgC;IAC9DiB,OAAO,CAACwD,MAAM,CAACiC,kBAAkB,GAAG;MAClCkE,aAAa,EAAE3J,OAAO,CAACgC,YAAY,CAACmD,MAAM;MAAE9B,eAAe,EAAErD,OAAO,CAACgC,YAAY,CAACqB,eAAe;MACjG6I,IAAI,EAAElM,OAAO,CAACgC,YAAY,CAACkK,IAAI;MAAEjK,MAAM,EAAEjC,OAAO,CAACgC,YAAY,CAACC,MAAM;MAAEsD,QAAQ,EAAEvF,OAAO,CAACgC,YAAY,CAACuD,QAAQ;MAC7GzD,gBAAgB,EAAE9B,OAAO,CAAC8B,gBAAgB;MAAE0H,eAAe,EAAExJ,OAAO,CAAC6C;KACtE;EACH;EAEQqI,kBAAkBA,CAACiB,WAAgB;IACzC,IAAIA,WAAW,KAAKC,SAAS,EAAE;MAC7B,OAAO,CAAC9B,KAAK,CAACW,MAAM,CAACkB,WAAW,CAAC,CAAC,GAAGlB,MAAM,CAACkB,WAAW,CAAC,GAAGA,WAAW;IACxE;EACF;EAEQlL,eAAeA,CAACjB,OAAsB;IAC5C,MAAMyF,kBAAkB,GAAG;MACzBxD,MAAM,EAAEjC,OAAO,CAACgC,YAAY,CAACC,MAAM;MACnCoB,eAAe,EAAErD,OAAO,CAACgC,YAAY,CAACqB,eAAe;MACrDkC,QAAQ,EAAEvF,OAAO,CAACgC,YAAY,CAACuD,QAAQ;MACvCiE,eAAe,EAAExJ,OAAO,CAAC6C;KAC1B;IAED7C,OAAO,CAACwD,MAAM,CAAC2F,WAAW,GAAIC,MAAM,IAAKpJ,OAAO,CAACgC,YAAY,CAACuD,QAAQ,GAAG,eAAe,GAAG,EAAE;IAC7FvF,OAAO,CAACwD,MAAM,CAACiF,YAAY,GAAGnJ,6BAA6B;IAC3DU,OAAO,CAACwD,MAAM,CAACiC,kBAAkB,GAAG;MAAE,GAAGA;IAAkB,CAAE;EAC/D;EAEQmE,+BAA+BA,CAAC5J,OAAsB;IAC5D,MAAMyF,kBAAkB,GAAG;MACzBxD,MAAM,EAAEjC,OAAO,CAACgC,YAAY,CAACC,MAAM;MACnCoB,eAAe,EAAErD,OAAO,CAACgC,YAAY,CAACqB,eAAe;MACrDkC,QAAQ,EAAEvF,OAAO,CAACgC,YAAY,CAACuD,QAAQ;MACvCiE,eAAe,EAAExJ,OAAO,CAAC6C;KAC1B;IAED,IAAI7C,OAAO,CAACgC,YAAY,CAACqB,eAAe,EAAE;MACxCrD,OAAO,CAACwD,MAAM,CAAC2F,WAAW,GAAIC,MAAM,IAAMpJ,OAAO,CAACgC,YAAY,CAACqB,eAAe,IAAIrD,OAAO,CAACgC,YAAY,CAACuD,QAAQ,GAAI,eAAe,GAAG,EAAE;MACvIvF,OAAO,CAACwD,MAAM,CAACiF,YAAY,GAAGjK,+BAA+B;MAC7DwB,OAAO,CAACwD,MAAM,CAACiC,kBAAkB,GAAGA,kBAAkB;MACtDzF,OAAO,CAACwD,MAAM,CAAC6I,SAAS,GAAG,EAAE;IAC/B,CAAC,MAAM;MACLrM,OAAO,CAACwD,MAAM,CAAC6I,SAAS,GAAG,sBAAsB;IACnD;EACF;;;uBAvqBW3M,wBAAwB,EAAA4M,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,eAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,qBAAA;IAAA;EAAA;;;aAAxBjN,wBAAwB;MAAAkN,OAAA,EAAxBlN,wBAAwB,CAAAmN,IAAA;MAAAC,UAAA,EAHvB;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}