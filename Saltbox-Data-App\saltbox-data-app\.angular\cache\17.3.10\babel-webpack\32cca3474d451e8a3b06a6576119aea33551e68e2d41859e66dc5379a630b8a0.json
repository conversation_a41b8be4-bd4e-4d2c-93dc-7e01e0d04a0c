{"ast": null, "code": "import _asyncToGenerator from \"C:/Projects/SaltboxActionableGrid/Saltbox-Data-App/saltbox-data-app/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { EventEmitter } from '@angular/core';\nimport { EmailSetup } from 'src/app/core/models/email-setup';\nimport { EmailAttachmentType } from 'src/app/core/enums/shared';\nimport { ButtonModule } from 'primeng/button';\nimport { SharedModule } from 'primeng/api';\nimport { DropdownModule } from 'primeng/dropdown';\nimport { InputTextareaModule } from 'primeng/inputtextarea';\nimport { InputTextModule } from 'primeng/inputtext';\nimport { FormsModule } from '@angular/forms';\nimport { DialogModule } from 'primeng/dialog';\nimport { getPlatformTokenKey } from 'src/app/shared/utilities/helper.functions';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/actionable-grid/services/actionable-grid.service\";\nimport * as i2 from \"src/app/core/services/notification.service\";\nimport * as i3 from \"src/app/core/services/authentication.service\";\nimport * as i4 from \"primeng/dialog\";\nimport * as i5 from \"primeng/api\";\nimport * as i6 from \"@angular/forms\";\nimport * as i7 from \"primeng/inputtext\";\nimport * as i8 from \"primeng/inputtextarea\";\nimport * as i9 from \"primeng/dropdown\";\nimport * as i10 from \"primeng/button\";\nfunction EmailGridComponent_ng_template_26_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-button\", 17);\n    i0.ɵɵlistener(\"onClick\", function EmailGridComponent_ng_template_26_Template_p_button_onClick_0_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.sendEmail());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(1, \"p-button\", 18);\n    i0.ɵɵlistener(\"onClick\", function EmailGridComponent_ng_template_26_Template_p_button_onClick_1_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onCancel());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"outlined\", true);\n  }\n}\nexport class EmailGridComponent {\n  constructor(actionableGridService, messageSvc, authenticationService) {\n    this.actionableGridService = actionableGridService;\n    this.messageSvc = messageSvc;\n    this.authenticationService = authenticationService;\n    this.displayChange = new EventEmitter();\n    this.emailSetup = new EmailSetup();\n    this.attachmentTypes = Object.keys(EmailAttachmentType).map(key => ({\n      label: key,\n      value: EmailAttachmentType[key]\n    }));\n    this.selectedAttachmentType = EmailAttachmentType.Excel;\n  }\n  onCancel() {\n    this.displayChange.emit(false);\n  }\n  sendEmail() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      _this.emailSetup.fromAddress = _this.authenticationService.decodeTokenIfNotExpired(getPlatformTokenKey())?.email;\n      if (_this.checkValues()) {\n        _this.emailSetup.attachmentType = EmailAttachmentType[_this.selectedAttachmentType];\n        _this.emailSetup.attachmentName = _this.reportInfo.reportName.concat(_this.selectedAttachmentType);\n        _this.emailSetup.attachment = EmailAttachmentType.Excel ? yield _this.exportAsExcelFile() : _this.exportAsCsvFile();\n        _this.actionableGridService.sendEmail(_this.reportInfo.reportId, _this.reportInfo.emailLimit, _this.emailSetup).subscribe({\n          next: function () {\n            var _ref = _asyncToGenerator(function* (response) {\n              if (response === 'true') {\n                _this.messageSvc.showSuccess('Email sent successfully.');\n              } else {\n                _this.messageSvc.showError('Unable to send the email.');\n              }\n              _this.onCancel();\n            });\n            return function next(_x) {\n              return _ref.apply(this, arguments);\n            };\n          }(),\n          error: err => {\n            _this.messageSvc.showError(err.message);\n            _this.onCancel();\n          }\n        });\n      }\n    })();\n  }\n  checkValues() {\n    if (!this.emailSetup.fromAddress) {\n      this.messageSvc.showWarning('error', 'Could not find logged in user email address');\n      return false;\n    }\n    if (!this.validateEmailList()) {\n      this.messageSvc.showWarning('error', 'Please enter valid email(s) to send');\n      return false;\n    }\n    if (!this.emailSetup.message) {\n      this.emailSetup.message = '';\n    }\n    return true;\n  }\n  exportAsExcelFile() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      const result = _this2.gridApi.getDataAsExcel({\n        processCellCallback: params => {\n          return params.formatValue(params.value);\n        },\n        processRowGroupCallback: params => {\n          return params.node.key;\n        },\n        columnKeys: _this2.actionableGridService.getGridDisplayedColumns(_this2.gridApi),\n        fileName: _this2.reportInfo.reportName,\n        sheetName: _this2.reportInfo.reportName,\n        prependContent: []\n      });\n      return yield _this2.blobToBase64(result);\n    })();\n  }\n  exportAsCsvFile() {\n    const result = this.gridApi.getDataAsCsv({\n      processCellCallback: params => {\n        return params.formatValue(params.value);\n      },\n      processRowGroupCallback: params => {\n        return params.node.key;\n      },\n      columnKeys: this.actionableGridService.getGridDisplayedColumns(this.gridApi),\n      fileName: this.reportInfo.reportName\n    });\n    return btoa(result);\n  }\n  validateEmailList() {\n    const emails = this.emailSetup.toAddress?.split(',');\n    return emails?.length ? emails.every(email => this.validateEmail(email)) : false;\n  }\n  validateEmail(email) {\n    const regularExpression = /^(([^<>()[\\]\\\\.,;:\\s@\"]+(\\.[^<>()[\\]\\\\.,;:\\s@\"]+)*)|(\".+\"))@((\\[[0-9]{1,3}\\.[0-9]{1,3}\\.[0-9]{1,3}\\.[0-9]{1,3}\\])|(([a-zA-Z\\-0-9]+\\.)+[a-zA-Z]{2,}))$/;\n    return regularExpression.test(email.toLowerCase().trim());\n  }\n  blobToBase64(blob) {\n    return _asyncToGenerator(function* () {\n      return new Promise((resolve, _) => {\n        const reader = new FileReader();\n        reader.onloadend = () => resolve(reader.result.split(',')[1]);\n        reader.readAsDataURL(blob);\n      });\n    })();\n  }\n  static {\n    this.ɵfac = function EmailGridComponent_Factory(t) {\n      return new (t || EmailGridComponent)(i0.ɵɵdirectiveInject(i1.ActionableGridService), i0.ɵɵdirectiveInject(i2.NotificationService), i0.ɵɵdirectiveInject(i3.AuthenticationService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: EmailGridComponent,\n      selectors: [[\"app-email-grid\"]],\n      inputs: {\n        reportInfo: \"reportInfo\",\n        gridApi: \"gridApi\"\n      },\n      outputs: {\n        displayChange: \"displayChange\"\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 27,\n      vars: 9,\n      consts: [[\"id\", \"header\", \"name\", \"header\", \"header\", \"Email Grid\", 3, \"visibleChange\", \"visible\", \"modal\"], [1, \"card\"], [1, \"p-fluid\", \"grid\", \"field\"], [1, \"col-12\", \"p-float-label\"], [\"id\", \"toMail\", \"type\", \"text\", \"pInputText\", \"\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"toMail\"], [\"id\", \"toMail-help\"], [\"id\", \"subject\", \"type\", \"text\", \"pInputText\", \"\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"subject\"], [\"id\", \"message\", \"type\", \"text\", \"rows\", \"4\", \"pInputText\", \"\", \"pInputTextarea\", \"\", 1, \"col-12\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"message\"], [1, \"col-12\", \"p-1\"], [\"for\", \"attachment\"], [1, \"p-text-left\", \"p-text-lowercase\"], [\"inputId\", \"attatchmentType\", \"appendTo\", \"body\", \"placeholder\", \"Select Attachment Type\", \"optionLabel\", \"label\", \"optionValue\", \"value\", 3, \"ngModelChange\", \"options\", \"ngModel\"], [\"for\", \"attatchmentType\"], [\"pTemplate\", \"footer\"], [\"type\", \"button\", \"icon\", \"pi pi-send\", \"label\", \"Send\", 3, \"onClick\"], [\"type\", \"button\", \"icon\", \"pi pi-ban\", \"severity\", \"secondary\", \"label\", \"Cancel\", 3, \"onClick\", \"outlined\"]],\n      template: function EmailGridComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"p-dialog\", 0);\n          i0.ɵɵlistener(\"visibleChange\", function EmailGridComponent_Template_p_dialog_visibleChange_0_listener() {\n            return ctx.onCancel();\n          });\n          i0.ɵɵelementStart(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"input\", 4);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function EmailGridComponent_Template_input_ngModelChange_4_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.emailSetup.toAddress, $event) || (ctx.emailSetup.toAddress = $event);\n            return $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"label\", 5);\n          i0.ɵɵtext(6, \"To\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"small\", 6);\n          i0.ɵɵtext(8, \"Enter multiple email address using commas(',').\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(9, \"div\", 3)(10, \"input\", 7);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function EmailGridComponent_Template_input_ngModelChange_10_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.emailSetup.subject, $event) || (ctx.emailSetup.subject = $event);\n            return $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"label\", 8);\n          i0.ɵɵtext(12, \"Subject\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(13, \"div\", 3)(14, \"textarea\", 9);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function EmailGridComponent_Template_textarea_ngModelChange_14_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.emailSetup.message, $event) || (ctx.emailSetup.message = $event);\n            return $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(15, \"label\", 10);\n          i0.ɵɵtext(16, \"Message\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(17, \"div\", 11)(18, \"label\", 12);\n          i0.ɵɵtext(19, \"Attachment\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(20, \"div\", 13);\n          i0.ɵɵtext(21);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(22, \"div\", 3)(23, \"p-dropdown\", 14);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function EmailGridComponent_Template_p_dropdown_ngModelChange_23_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.selectedAttachmentType, $event) || (ctx.selectedAttachmentType = $event);\n            return $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(24, \"label\", 15);\n          i0.ɵɵtext(25, \"Attachment Type\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵtemplate(26, EmailGridComponent_ng_template_26_Template, 2, 1, \"ng-template\", 16);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"visible\", true)(\"modal\", true);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.emailSetup.toAddress);\n          i0.ɵɵadvance(6);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.emailSetup.subject);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.emailSetup.message);\n          i0.ɵɵadvance(7);\n          i0.ɵɵtextInterpolate2(\"\", ctx.reportInfo == null ? null : ctx.reportInfo.reportName, \"\", ctx.selectedAttachmentType, \"\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"options\", ctx.attachmentTypes);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedAttachmentType);\n        }\n      },\n      dependencies: [DialogModule, i4.Dialog, i5.PrimeTemplate, FormsModule, i6.DefaultValueAccessor, i6.NgControlStatus, i6.NgModel, InputTextModule, i7.InputText, InputTextareaModule, i8.InputTextarea, DropdownModule, i9.Dropdown, SharedModule, ButtonModule, i10.Button],\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["EventEmitter", "EmailSetup", "EmailAttachmentType", "ButtonModule", "SharedModule", "DropdownModule", "InputTextareaModule", "InputTextModule", "FormsModule", "DialogModule", "getPlatformTokenKey", "i0", "ɵɵelementStart", "ɵɵlistener", "EmailGridComponent_ng_template_26_Template_p_button_onClick_0_listener", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "sendEmail", "ɵɵelementEnd", "EmailGridComponent_ng_template_26_Template_p_button_onClick_1_listener", "onCancel", "ɵɵadvance", "ɵɵproperty", "EmailGridComponent", "constructor", "actionableGridService", "messageSvc", "authenticationService", "displayChange", "emailSetup", "attachmentTypes", "Object", "keys", "map", "key", "label", "value", "selectedAttachmentType", "Excel", "emit", "_this", "_asyncToGenerator", "fromAddress", "decodeTokenIfNotExpired", "email", "checkValues", "attachmentType", "attachmentName", "reportInfo", "reportName", "concat", "attachment", "exportAsExcelFile", "exportAsCsvFile", "reportId", "emailLimit", "subscribe", "next", "_ref", "response", "showSuccess", "showError", "_x", "apply", "arguments", "error", "err", "message", "showWarning", "validateEmailList", "_this2", "result", "gridApi", "getDataAsExcel", "processCellCallback", "params", "formatValue", "processRowGroupCallback", "node", "columnKeys", "getGridDisplayedColumns", "fileName", "sheetName", "prependContent", "blobToBase64", "getDataAsCsv", "btoa", "emails", "to<PERSON><PERSON><PERSON>", "split", "length", "every", "validateEmail", "regularExpression", "test", "toLowerCase", "trim", "blob", "Promise", "resolve", "_", "reader", "FileReader", "onloadend", "readAsDataURL", "ɵɵdirectiveInject", "i1", "ActionableGridService", "i2", "NotificationService", "i3", "AuthenticationService", "selectors", "inputs", "outputs", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "EmailGridComponent_Template", "rf", "ctx", "EmailGridComponent_Template_p_dialog_visibleChange_0_listener", "ɵɵtwoWayListener", "EmailGridComponent_Template_input_ngModelChange_4_listener", "$event", "ɵɵtwoWayBindingSet", "ɵɵtext", "EmailGridComponent_Template_input_ngModelChange_10_listener", "subject", "EmailGridComponent_Template_textarea_ngModelChange_14_listener", "EmailGridComponent_Template_p_dropdown_ngModelChange_23_listener", "ɵɵtemplate", "EmailGridComponent_ng_template_26_Template", "ɵɵtwoWayProperty", "ɵɵtextInterpolate2", "i4", "Dialog", "i5", "PrimeTemplate", "i6", "DefaultValueAccessor", "NgControlStatus", "NgModel", "i7", "InputText", "i8", "InputTextarea", "i9", "Dropdown", "i10", "<PERSON><PERSON>", "styles"], "sources": ["C:\\Projects\\SaltboxActionableGrid\\Saltbox-Data-App\\saltbox-data-app\\src\\app\\actionable-grid\\email-grid\\email-grid.component.ts", "C:\\Projects\\SaltboxActionableGrid\\Saltbox-Data-App\\saltbox-data-app\\src\\app\\actionable-grid\\email-grid\\email-grid.component.html"], "sourcesContent": ["import { Component, EventEmitter, Input, Output } from '@angular/core';\r\nimport { GridApi } from 'ag-grid-community';\r\nimport { ReportInfo } from 'src/app/core/models/report-info';\r\nimport { NotificationService } from 'src/app/core/services/notification.service';\r\nimport { ActionableGridService } from 'src/app/actionable-grid/services/actionable-grid.service';\r\nimport { EmailSetup } from 'src/app/core/models/email-setup';\r\nimport { AuthenticationService } from 'src/app/core/services/authentication.service';\r\nimport { EmailAttachmentType } from 'src/app/core/enums/shared';\r\nimport { ButtonModule } from 'primeng/button';\r\nimport { SharedModule } from 'primeng/api';\r\nimport { DropdownModule } from 'primeng/dropdown';\r\nimport { InputTextareaModule } from 'primeng/inputtextarea';\r\nimport { InputTextModule } from 'primeng/inputtext';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { DialogModule } from 'primeng/dialog';\r\nimport { getPlatformTokenKey } from 'src/app/shared/utilities/helper.functions';\r\n\r\n@Component({\r\n    selector: 'app-email-grid',\r\n    templateUrl: './email-grid.component.html',\r\n    styleUrls: ['./email-grid.component.scss'],\r\n    standalone: true,\r\n    imports: [DialogModule, FormsModule, InputTextModule, InputTextareaModule, DropdownModule, SharedModule, ButtonModule]\r\n})\r\nexport class EmailGridComponent {\r\n\r\n  constructor(\r\n    private actionableGridService: ActionableGridService,\r\n    private messageSvc: NotificationService,\r\n    private authenticationService: AuthenticationService) { }\r\n\r\n  @Input() reportInfo: ReportInfo;\r\n  @Input() gridApi: GridApi;\r\n  @Output() displayChange = new EventEmitter();\r\n\r\n  emailSetup: EmailSetup = new EmailSetup();\r\n  attachmentTypes = Object.keys(EmailAttachmentType).map(key => ({ label: key, value: EmailAttachmentType[key] }));\r\n  selectedAttachmentType = EmailAttachmentType.Excel;\r\n\r\n  onCancel(): void {\r\n    this.displayChange.emit(false);\r\n  }\r\n\r\n  async sendEmail() {\r\n    this.emailSetup.fromAddress = this.authenticationService.decodeTokenIfNotExpired(getPlatformTokenKey())?.email;\r\n    if (this.checkValues()) {\r\n      this.emailSetup.attachmentType = EmailAttachmentType[this.selectedAttachmentType];\r\n      this.emailSetup.attachmentName = this.reportInfo.reportName.concat(this.selectedAttachmentType);\r\n      this.emailSetup.attachment = EmailAttachmentType.Excel ? await this.exportAsExcelFile() : this.exportAsCsvFile();\r\n\r\n      this.actionableGridService.sendEmail(this.reportInfo.reportId, this.reportInfo.emailLimit, this.emailSetup).subscribe({\r\n        next: async (response: string) => {\r\n          if (response === 'true') {\r\n            this.messageSvc.showSuccess('Email sent successfully.');\r\n          }\r\n          else {\r\n            this.messageSvc.showError('Unable to send the email.');\r\n          }\r\n          this.onCancel();\r\n        },\r\n        error: err => {\r\n          this.messageSvc.showError(err.message);\r\n          this.onCancel();\r\n        }\r\n      });\r\n    }\r\n  }\r\n\r\n  checkValues(): boolean {\r\n    if (!this.emailSetup.fromAddress) {\r\n      this.messageSvc.showWarning('error', 'Could not find logged in user email address');\r\n      return false;\r\n    }\r\n    if (!this.validateEmailList()) {\r\n      this.messageSvc.showWarning('error', 'Please enter valid email(s) to send');\r\n      return false;\r\n    }\r\n    if (!this.emailSetup.message) {\r\n      this.emailSetup.message = '';\r\n    }\r\n    return true;\r\n  }\r\n\r\n  async exportAsExcelFile(): Promise<string> {\r\n    const result = this.gridApi.getDataAsExcel({\r\n      processCellCallback: (params) => {\r\n        return params.formatValue(params.value);\r\n      },\r\n      processRowGroupCallback: (params) => {\r\n        return params.node.key;\r\n      },\r\n      columnKeys: this.actionableGridService.getGridDisplayedColumns(this.gridApi), \r\n      fileName: this.reportInfo.reportName, sheetName: this.reportInfo.reportName, prependContent: []\r\n    });\r\n\r\n    return await this.blobToBase64(result);\r\n  }\r\n\r\n  exportAsCsvFile() {\r\n    const result = this.gridApi.getDataAsCsv({\r\n      processCellCallback: (params) => {\r\n        return params.formatValue(params.value);\r\n      },\r\n      processRowGroupCallback: (params) => {\r\n        return params.node.key;\r\n      },\r\n      columnKeys: this.actionableGridService.getGridDisplayedColumns(this.gridApi), \r\n      fileName: this.reportInfo.reportName\r\n    });\r\n\r\n    return btoa(result);\r\n  }\r\n\r\n  validateEmailList(): boolean {\r\n    const emails = this.emailSetup.toAddress?.split(',');\r\n    return emails?.length ? emails.every(email => this.validateEmail(email)) : false;\r\n  }\r\n\r\n  validateEmail(email: string): boolean {\r\n    const regularExpression = /^(([^<>()[\\]\\\\.,;:\\s@\"]+(\\.[^<>()[\\]\\\\.,;:\\s@\"]+)*)|(\".+\"))@((\\[[0-9]{1,3}\\.[0-9]{1,3}\\.[0-9]{1,3}\\.[0-9]{1,3}\\])|(([a-zA-Z\\-0-9]+\\.)+[a-zA-Z]{2,}))$/;\r\n    return regularExpression.test(email.toLowerCase().trim());\r\n  }\r\n\r\n  async blobToBase64(blob): Promise<string> {\r\n    return new Promise((resolve, _) => {\r\n      const reader = new FileReader();\r\n      reader.onloadend = () => resolve((reader.result as string).split(',')[1]);\r\n      reader.readAsDataURL(blob);\r\n    });\r\n  }\r\n\r\n}\r\n", "<p-dialog id=header name=\"header\" header=\"Email Grid\" [visible]=\"true\" [modal]=\"true\"\r\n    (visibleChange)=\"onCancel()\">\r\n    <div class=\"card\">\r\n        <div class=\"p-fluid grid field\">\r\n            <div class=\"col-12 p-float-label\">\r\n                <input id=\"toMail\" type=\"text\" pInputText [(ngModel)]=\"emailSetup.toAddress\" />\r\n                <label for=\"toMail\">To</label>\r\n                <small id=\"toMail-help\">Enter multiple email address using commas(',').</small>\r\n            </div>\r\n            <div class=\"col-12 p-float-label\">\r\n                <input id=\"subject\" type=\"text\" pInputText [(ngModel)]=\"emailSetup.subject\" />\r\n                <label for=\"subject\">Subject</label>\r\n            </div>\r\n            <div class=\"col-12 p-float-label\">\r\n                <textarea id=\"message\" type=\"text\" class=\"col-12\" rows=\"4\" pInputText [(ngModel)]=\"emailSetup.message\" pInputTextarea></textarea>\r\n                <label for=\"message\">Message</label>\r\n            </div>\r\n            <div class=\"col-12 p-1\">\r\n                <label for=\"attachment\">Attachment</label>\r\n                <div class=\"p-text-left p-text-lowercase\">{{reportInfo?.reportName}}{{selectedAttachmentType}}</div>\r\n            </div>\r\n            <div class=\"col-12 p-float-label\">\r\n                    <p-dropdown inputId=\"attatchmentType\" appendTo=\"body\" [options]=\"attachmentTypes\"\r\n                        [(ngModel)]=\"selectedAttachmentType\" placeholder=\"Select Attachment Type\" optionLabel=\"label\"\r\n                        optionValue=\"value\">\r\n                    </p-dropdown>\r\n                    <label for=\"attatchmentType\">Attachment Type</label>\r\n            </div>\r\n        </div>\r\n    </div>\r\n    <ng-template pTemplate=\"footer\">\r\n        <p-button type=\"button\"icon=\"pi pi-send\" label=\"Send\" (onClick)=\"sendEmail()\"></p-button>\r\n        <p-button type=\"button\"icon=\"pi pi-ban\" [outlined]=\"true\" severity=\"secondary\" label=\"Cancel\" (onClick)=\"onCancel()\"></p-button>\r\n    </ng-template>\r\n</p-dialog>"], "mappings": ";AAAA,SAAoBA,YAAY,QAAuB,eAAe;AAKtE,SAASC,UAAU,QAAQ,iCAAiC;AAE5D,SAASC,mBAAmB,QAAQ,2BAA2B;AAC/D,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,YAAY,QAAQ,aAAa;AAC1C,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,mBAAmB,QAAQ,uBAAuB;AAC3D,SAASC,eAAe,QAAQ,mBAAmB;AACnD,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,mBAAmB,QAAQ,2CAA2C;;;;;;;;;;;;;;;ICgBvEC,EAAA,CAAAC,cAAA,mBAA8E;IAAxBD,EAAA,CAAAE,UAAA,qBAAAC,uEAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAWF,MAAA,CAAAG,SAAA,EAAW;IAAA,EAAC;IAACT,EAAA,CAAAU,YAAA,EAAW;IACzFV,EAAA,CAAAC,cAAA,mBAAqH;IAAvBD,EAAA,CAAAE,UAAA,qBAAAS,uEAAA;MAAAX,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAWF,MAAA,CAAAM,QAAA,EAAU;IAAA,EAAC;IAACZ,EAAA,CAAAU,YAAA,EAAW;;;IAAxFV,EAAA,CAAAa,SAAA,EAAiB;IAAjBb,EAAA,CAAAc,UAAA,kBAAiB;;;ADRjE,OAAM,MAAOC,kBAAkB;EAE7BC,YACUC,qBAA4C,EAC5CC,UAA+B,EAC/BC,qBAA4C;IAF5C,KAAAF,qBAAqB,GAArBA,qBAAqB;IACrB,KAAAC,UAAU,GAAVA,UAAU;IACV,KAAAC,qBAAqB,GAArBA,qBAAqB;IAIrB,KAAAC,aAAa,GAAG,IAAI/B,YAAY,EAAE;IAE5C,KAAAgC,UAAU,GAAe,IAAI/B,UAAU,EAAE;IACzC,KAAAgC,eAAe,GAAGC,MAAM,CAACC,IAAI,CAACjC,mBAAmB,CAAC,CAACkC,GAAG,CAACC,GAAG,KAAK;MAAEC,KAAK,EAAED,GAAG;MAAEE,KAAK,EAAErC,mBAAmB,CAACmC,GAAG;IAAC,CAAE,CAAC,CAAC;IAChH,KAAAG,sBAAsB,GAAGtC,mBAAmB,CAACuC,KAAK;EARQ;EAU1DlB,QAAQA,CAAA;IACN,IAAI,CAACQ,aAAa,CAACW,IAAI,CAAC,KAAK,CAAC;EAChC;EAEMtB,SAASA,CAAA;IAAA,IAAAuB,KAAA;IAAA,OAAAC,iBAAA;MACbD,KAAI,CAACX,UAAU,CAACa,WAAW,GAAGF,KAAI,CAACb,qBAAqB,CAACgB,uBAAuB,CAACpC,mBAAmB,EAAE,CAAC,EAAEqC,KAAK;MAC9G,IAAIJ,KAAI,CAACK,WAAW,EAAE,EAAE;QACtBL,KAAI,CAACX,UAAU,CAACiB,cAAc,GAAG/C,mBAAmB,CAACyC,KAAI,CAACH,sBAAsB,CAAC;QACjFG,KAAI,CAACX,UAAU,CAACkB,cAAc,GAAGP,KAAI,CAACQ,UAAU,CAACC,UAAU,CAACC,MAAM,CAACV,KAAI,CAACH,sBAAsB,CAAC;QAC/FG,KAAI,CAACX,UAAU,CAACsB,UAAU,GAAGpD,mBAAmB,CAACuC,KAAK,SAASE,KAAI,CAACY,iBAAiB,EAAE,GAAGZ,KAAI,CAACa,eAAe,EAAE;QAEhHb,KAAI,CAACf,qBAAqB,CAACR,SAAS,CAACuB,KAAI,CAACQ,UAAU,CAACM,QAAQ,EAAEd,KAAI,CAACQ,UAAU,CAACO,UAAU,EAAEf,KAAI,CAACX,UAAU,CAAC,CAAC2B,SAAS,CAAC;UACpHC,IAAI;YAAA,IAAAC,IAAA,GAAAjB,iBAAA,CAAE,WAAOkB,QAAgB,EAAI;cAC/B,IAAIA,QAAQ,KAAK,MAAM,EAAE;gBACvBnB,KAAI,CAACd,UAAU,CAACkC,WAAW,CAAC,0BAA0B,CAAC;cACzD,CAAC,MACI;gBACHpB,KAAI,CAACd,UAAU,CAACmC,SAAS,CAAC,2BAA2B,CAAC;cACxD;cACArB,KAAI,CAACpB,QAAQ,EAAE;YACjB,CAAC;YAAA,gBARDqC,IAAIA,CAAAK,EAAA;cAAA,OAAAJ,IAAA,CAAAK,KAAA,OAAAC,SAAA;YAAA;UAAA,GAQH;UACDC,KAAK,EAAEC,GAAG,IAAG;YACX1B,KAAI,CAACd,UAAU,CAACmC,SAAS,CAACK,GAAG,CAACC,OAAO,CAAC;YACtC3B,KAAI,CAACpB,QAAQ,EAAE;UACjB;SACD,CAAC;MACJ;IAAC;EACH;EAEAyB,WAAWA,CAAA;IACT,IAAI,CAAC,IAAI,CAAChB,UAAU,CAACa,WAAW,EAAE;MAChC,IAAI,CAAChB,UAAU,CAAC0C,WAAW,CAAC,OAAO,EAAE,6CAA6C,CAAC;MACnF,OAAO,KAAK;IACd;IACA,IAAI,CAAC,IAAI,CAACC,iBAAiB,EAAE,EAAE;MAC7B,IAAI,CAAC3C,UAAU,CAAC0C,WAAW,CAAC,OAAO,EAAE,qCAAqC,CAAC;MAC3E,OAAO,KAAK;IACd;IACA,IAAI,CAAC,IAAI,CAACvC,UAAU,CAACsC,OAAO,EAAE;MAC5B,IAAI,CAACtC,UAAU,CAACsC,OAAO,GAAG,EAAE;IAC9B;IACA,OAAO,IAAI;EACb;EAEMf,iBAAiBA,CAAA;IAAA,IAAAkB,MAAA;IAAA,OAAA7B,iBAAA;MACrB,MAAM8B,MAAM,GAAGD,MAAI,CAACE,OAAO,CAACC,cAAc,CAAC;QACzCC,mBAAmB,EAAGC,MAAM,IAAI;UAC9B,OAAOA,MAAM,CAACC,WAAW,CAACD,MAAM,CAACvC,KAAK,CAAC;QACzC,CAAC;QACDyC,uBAAuB,EAAGF,MAAM,IAAI;UAClC,OAAOA,MAAM,CAACG,IAAI,CAAC5C,GAAG;QACxB,CAAC;QACD6C,UAAU,EAAET,MAAI,CAAC7C,qBAAqB,CAACuD,uBAAuB,CAACV,MAAI,CAACE,OAAO,CAAC;QAC5ES,QAAQ,EAAEX,MAAI,CAACtB,UAAU,CAACC,UAAU;QAAEiC,SAAS,EAAEZ,MAAI,CAACtB,UAAU,CAACC,UAAU;QAAEkC,cAAc,EAAE;OAC9F,CAAC;MAEF,aAAab,MAAI,CAACc,YAAY,CAACb,MAAM,CAAC;IAAC;EACzC;EAEAlB,eAAeA,CAAA;IACb,MAAMkB,MAAM,GAAG,IAAI,CAACC,OAAO,CAACa,YAAY,CAAC;MACvCX,mBAAmB,EAAGC,MAAM,IAAI;QAC9B,OAAOA,MAAM,CAACC,WAAW,CAACD,MAAM,CAACvC,KAAK,CAAC;MACzC,CAAC;MACDyC,uBAAuB,EAAGF,MAAM,IAAI;QAClC,OAAOA,MAAM,CAACG,IAAI,CAAC5C,GAAG;MACxB,CAAC;MACD6C,UAAU,EAAE,IAAI,CAACtD,qBAAqB,CAACuD,uBAAuB,CAAC,IAAI,CAACR,OAAO,CAAC;MAC5ES,QAAQ,EAAE,IAAI,CAACjC,UAAU,CAACC;KAC3B,CAAC;IAEF,OAAOqC,IAAI,CAACf,MAAM,CAAC;EACrB;EAEAF,iBAAiBA,CAAA;IACf,MAAMkB,MAAM,GAAG,IAAI,CAAC1D,UAAU,CAAC2D,SAAS,EAAEC,KAAK,CAAC,GAAG,CAAC;IACpD,OAAOF,MAAM,EAAEG,MAAM,GAAGH,MAAM,CAACI,KAAK,CAAC/C,KAAK,IAAI,IAAI,CAACgD,aAAa,CAAChD,KAAK,CAAC,CAAC,GAAG,KAAK;EAClF;EAEAgD,aAAaA,CAAChD,KAAa;IACzB,MAAMiD,iBAAiB,GAAG,uJAAuJ;IACjL,OAAOA,iBAAiB,CAACC,IAAI,CAAClD,KAAK,CAACmD,WAAW,EAAE,CAACC,IAAI,EAAE,CAAC;EAC3D;EAEMZ,YAAYA,CAACa,IAAI;IAAA,OAAAxD,iBAAA;MACrB,OAAO,IAAIyD,OAAO,CAAC,CAACC,OAAO,EAAEC,CAAC,KAAI;QAChC,MAAMC,MAAM,GAAG,IAAIC,UAAU,EAAE;QAC/BD,MAAM,CAACE,SAAS,GAAG,MAAMJ,OAAO,CAAEE,MAAM,CAAC9B,MAAiB,CAACkB,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QACzEY,MAAM,CAACG,aAAa,CAACP,IAAI,CAAC;MAC5B,CAAC,CAAC;IAAC;EACL;;;uBAzGW1E,kBAAkB,EAAAf,EAAA,CAAAiG,iBAAA,CAAAC,EAAA,CAAAC,qBAAA,GAAAnG,EAAA,CAAAiG,iBAAA,CAAAG,EAAA,CAAAC,mBAAA,GAAArG,EAAA,CAAAiG,iBAAA,CAAAK,EAAA,CAAAC,qBAAA;IAAA;EAAA;;;YAAlBxF,kBAAkB;MAAAyF,SAAA;MAAAC,MAAA;QAAAjE,UAAA;QAAAwB,OAAA;MAAA;MAAA0C,OAAA;QAAAtF,aAAA;MAAA;MAAAuF,UAAA;MAAAC,QAAA,GAAA5G,EAAA,CAAA6G,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,4BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCxB/BnH,EAAA,CAAAC,cAAA,kBACiC;UAA7BD,EAAA,CAAAE,UAAA,2BAAAmH,8DAAA;YAAA,OAAiBD,GAAA,CAAAxG,QAAA,EAAU;UAAA,EAAC;UAIhBZ,EAHZ,CAAAC,cAAA,aAAkB,aACkB,aACM,eACiD;UAArCD,EAAA,CAAAsH,gBAAA,2BAAAC,2DAAAC,MAAA;YAAAxH,EAAA,CAAAyH,kBAAA,CAAAL,GAAA,CAAA/F,UAAA,CAAA2D,SAAA,EAAAwC,MAAA,MAAAJ,GAAA,CAAA/F,UAAA,CAAA2D,SAAA,GAAAwC,MAAA;YAAA,OAAAA,MAAA;UAAA,EAAkC;UAA5ExH,EAAA,CAAAU,YAAA,EAA+E;UAC/EV,EAAA,CAAAC,cAAA,eAAoB;UAAAD,EAAA,CAAA0H,MAAA,SAAE;UAAA1H,EAAA,CAAAU,YAAA,EAAQ;UAC9BV,EAAA,CAAAC,cAAA,eAAwB;UAAAD,EAAA,CAAA0H,MAAA,sDAA+C;UAC3E1H,EAD2E,CAAAU,YAAA,EAAQ,EAC7E;UAEFV,EADJ,CAAAC,cAAA,aAAkC,gBACgD;UAAnCD,EAAA,CAAAsH,gBAAA,2BAAAK,4DAAAH,MAAA;YAAAxH,EAAA,CAAAyH,kBAAA,CAAAL,GAAA,CAAA/F,UAAA,CAAAuG,OAAA,EAAAJ,MAAA,MAAAJ,GAAA,CAAA/F,UAAA,CAAAuG,OAAA,GAAAJ,MAAA;YAAA,OAAAA,MAAA;UAAA,EAAgC;UAA3ExH,EAAA,CAAAU,YAAA,EAA8E;UAC9EV,EAAA,CAAAC,cAAA,gBAAqB;UAAAD,EAAA,CAAA0H,MAAA,eAAO;UAChC1H,EADgC,CAAAU,YAAA,EAAQ,EAClC;UAEFV,EADJ,CAAAC,cAAA,cAAkC,mBACwF;UAAhDD,EAAA,CAAAsH,gBAAA,2BAAAO,+DAAAL,MAAA;YAAAxH,EAAA,CAAAyH,kBAAA,CAAAL,GAAA,CAAA/F,UAAA,CAAAsC,OAAA,EAAA6D,MAAA,MAAAJ,GAAA,CAAA/F,UAAA,CAAAsC,OAAA,GAAA6D,MAAA;YAAA,OAAAA,MAAA;UAAA,EAAgC;UAAgBxH,EAAA,CAAAU,YAAA,EAAW;UACjIV,EAAA,CAAAC,cAAA,iBAAqB;UAAAD,EAAA,CAAA0H,MAAA,eAAO;UAChC1H,EADgC,CAAAU,YAAA,EAAQ,EAClC;UAEFV,EADJ,CAAAC,cAAA,eAAwB,iBACI;UAAAD,EAAA,CAAA0H,MAAA,kBAAU;UAAA1H,EAAA,CAAAU,YAAA,EAAQ;UAC1CV,EAAA,CAAAC,cAAA,eAA0C;UAAAD,EAAA,CAAA0H,MAAA,IAAoD;UAClG1H,EADkG,CAAAU,YAAA,EAAM,EAClG;UAEEV,EADR,CAAAC,cAAA,cAAkC,sBAGF;UADpBD,EAAA,CAAAsH,gBAAA,2BAAAQ,iEAAAN,MAAA;YAAAxH,EAAA,CAAAyH,kBAAA,CAAAL,GAAA,CAAAvF,sBAAA,EAAA2F,MAAA,MAAAJ,GAAA,CAAAvF,sBAAA,GAAA2F,MAAA;YAAA,OAAAA,MAAA;UAAA,EAAoC;UAExCxH,EAAA,CAAAU,YAAA,EAAa;UACbV,EAAA,CAAAC,cAAA,iBAA6B;UAAAD,EAAA,CAAA0H,MAAA,uBAAe;UAG5D1H,EAH4D,CAAAU,YAAA,EAAQ,EACtD,EACJ,EACJ;UACNV,EAAA,CAAA+H,UAAA,KAAAC,0CAAA,0BAAgC;UAIpChI,EAAA,CAAAU,YAAA,EAAW;;;UAlC4DV,EAAjB,CAAAc,UAAA,iBAAgB,eAAe;UAK3Bd,EAAA,CAAAa,SAAA,GAAkC;UAAlCb,EAAA,CAAAiI,gBAAA,YAAAb,GAAA,CAAA/F,UAAA,CAAA2D,SAAA,CAAkC;UAKjChF,EAAA,CAAAa,SAAA,GAAgC;UAAhCb,EAAA,CAAAiI,gBAAA,YAAAb,GAAA,CAAA/F,UAAA,CAAAuG,OAAA,CAAgC;UAIL5H,EAAA,CAAAa,SAAA,GAAgC;UAAhCb,EAAA,CAAAiI,gBAAA,YAAAb,GAAA,CAAA/F,UAAA,CAAAsC,OAAA,CAAgC;UAK5D3D,EAAA,CAAAa,SAAA,GAAoD;UAApDb,EAAA,CAAAkI,kBAAA,KAAAd,GAAA,CAAA5E,UAAA,kBAAA4E,GAAA,CAAA5E,UAAA,CAAAC,UAAA,MAAA2E,GAAA,CAAAvF,sBAAA,KAAoD;UAGpC7B,EAAA,CAAAa,SAAA,GAA2B;UAA3Bb,EAAA,CAAAc,UAAA,YAAAsG,GAAA,CAAA9F,eAAA,CAA2B;UAC7EtB,EAAA,CAAAiI,gBAAA,YAAAb,GAAA,CAAAvF,sBAAA,CAAoC;;;qBDD9C/B,YAAY,EAAAqI,EAAA,CAAAC,MAAA,EAAAC,EAAA,CAAAC,aAAA,EAAEzI,WAAW,EAAA0I,EAAA,CAAAC,oBAAA,EAAAD,EAAA,CAAAE,eAAA,EAAAF,EAAA,CAAAG,OAAA,EAAE9I,eAAe,EAAA+I,EAAA,CAAAC,SAAA,EAAEjJ,mBAAmB,EAAAkJ,EAAA,CAAAC,aAAA,EAAEpJ,cAAc,EAAAqJ,EAAA,CAAAC,QAAA,EAAEvJ,YAAY,EAAED,YAAY,EAAAyJ,GAAA,CAAAC,MAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}