import { Component, ViewChild } from '@angular/core';
import { ICustomCellRendererParams } from './ag-grid-custom-cell-renderer-params';
import { CustomCellRenderer } from './ag-grid-custom-cell-renderer';
import { NotificationService } from '../../services/notification.service';
import { JsonDataTypes } from 'src/app/shared/enums/json-schema.enum';
import { ChangeHistoryService } from 'src/app/actionable-grid/services/change-history.service';
import { AGGridService } from '../../services/ag-grid.service';
import { FormsModule } from '@angular/forms';
import { CheckboxModule } from 'primeng/checkbox';

/* for format config driven checkbox only*/
@Component({
    selector: 'app-checkbox-renderer',
    templateUrl: './ag-grid-checkbox-renderer.component.html',
    standalone: true,
    imports: [CheckboxModule, FormsModule]
})
export class AgGridCheckBoxRendererComponent extends CustomCellRenderer {
  @ViewChild('targetElement') elementRef;
  constructor(
    changeHistoryService: ChangeHistoryService,
    notificationService: NotificationService,
    aGGridService: AGGridService) {
    super(changeHistoryService, notificationService, aGGridService);
  }

  checkedValue: any;
  uncheckedValue: any;
  checked = false;

  agInit(cellRendererParams: ICustomCellRendererParams): void {
    super.agInit(cellRendererParams);

    const matchResult = this.cellRendererParams.value;

    if (this.cellRendererParams.format.baseType === JsonDataTypes.String) {
      const resultIndex = this.cellRendererParams.allowedValues.findIndex(x => x.value.toString().toLowerCase()
        === matchResult?.toString().toLowerCase());
      this.checked = (resultIndex > -1)
        ? this.cellRendererParams.allowedValues[resultIndex].key.toString().toLowerCase() === 'checked'
        : this.cellRendererParams.allowedValues.find(x => x.isDefault === true).key.toString().toLowerCase() === 'checked';
    }
    else {
      this.checked = matchResult === true || matchResult === 'true';
    }
  }

  onCheckboxChange(event: any) {
    let value;

    if (this.cellRendererParams.format.baseType === JsonDataTypes.String) {
      value = this.cellRendererParams.allowedValues
        .find(x => x.key.toString().toLowerCase() === (event.checked ? 'checked' : 'unchecked'))?.value;
    }
    else {
      value = event.checked;
    }

    if (value !== undefined) {
      this.cellRendererParams.value = value;
      super.setData();
    }
  }

  focusElement(): void {
    this.elementRef?.inputViewChild?.nativeElement.focus();
  }
}
