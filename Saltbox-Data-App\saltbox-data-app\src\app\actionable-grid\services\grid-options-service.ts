import { Injectable } from "@angular/core";
import { <PERSON><PERSON><PERSON>, <PERSON>rid<PERSON><PERSON>, GridOptions } from "ag-grid-community";
import { ActionableGridColumnConfig } from "src/app/core/models/actionable-grid-column-config";
import { ConditionalFormattingConfig } from "src/app/core/models/conditional-formatting-config";
import { ChangeTrackingService } from "src/app/core/services/change-tracking.service";
import { DB_PRIMARY_KEY } from "src/app/shared/constants/record.const";
import { ConditionalEvaluationService } from "./conditional-evaluation.service";
import { ProfileProperty } from "src/app/core/models/profile-property";

@Injectable({
    providedIn: 'root'
})

export class GridOptionsService {
    constructor(private changeTrackingService: ChangeTrackingService, private conditionalEvaluationService: ConditionalEvaluationService) { }

    setRowClassRules(gridOptions: GridOptions, requiredColumns: ActionableGridColumnConfig[]) {
        gridOptions.rowClassRules = {
            'initial-row-pointer-events-auto': () => true,
            'updated-row-pointer-events-none': params => {
                if (params.data) {
                    const modifiedRecord = this.changeTrackingService.batchChange?.modifiedRecords
                        .find(r => r.id === params.data[DB_PRIMARY_KEY] || r.recordPath?.includes(params.data[DB_PRIMARY_KEY]));
                    return modifiedRecord ? modifiedRecord.saveApplied : false;
                }
            },
            'updated-row-pointer-events-auto': params => {
                if (params.data) {
                    const modifiedRecord = this.changeTrackingService.batchChange?.modifiedRecords
                        .find(r => r.id === params.data[DB_PRIMARY_KEY] || r.recordPath?.includes(params.data[DB_PRIMARY_KEY]));
                    return modifiedRecord ? !modifiedRecord.saveApplied : false;
                }
            },
            'has-errors': params => {
                if (!params.data || !requiredColumns?.length) {
                    return false;
                }

                return this.changeTrackingService.batchChange?.modifiedRecords
                    ?.find(r => r.id === params.data[DB_PRIMARY_KEY] || r.recordPath?.includes(params.data[DB_PRIMARY_KEY]))?.data?.properties
                    ?.filter(changeDetail => requiredColumns.some(colDef => colDef.column === changeDetail.property))
                    ?.some(changeDetail => changeDetail.newValue == null || changeDetail.newValue === '') ?? false;
            }
        }
    }

    getRowStyle(data: any, conditionalFormattingConfig: ConditionalFormattingConfig[], profileProperties: ProfileProperty[]) {
        const filteredConfigs = conditionalFormattingConfig?.filter(c => !c.applyToCells);
        if (!data || !filteredConfigs?.length)
            return undefined;

        const config = this.conditionalEvaluationService.evaluateConditions(data, filteredConfigs, profileProperties);
        return this.getCSSStyle(config);
    }

    setCellStyles(columnDefs: ColDef[], conditionalFormattingConfig: ConditionalFormattingConfig[], profileProperties: ProfileProperty[]) {
        const filteredConfigs = conditionalFormattingConfig?.filter(c =>
            c.applyToCells ||
            (!c.applyToCells && (c.fontStyle != 'Normal' || c.fontWeight != 'Normal' || c.textDecoration != 'None')));

        if (!filteredConfigs?.length)
            return;


        columnDefs.forEach(colDef => {
            const filteredColConfigs = filteredConfigs.filter(c => (c.applyToCells && c.columns?.some(cell => cell === colDef.field)) || !c.applyToCells);
            if (!filteredColConfigs.length)
                return;

            colDef.cellStyle = (params) => {
                const config = this.conditionalEvaluationService.evaluateConditions(params.data, filteredColConfigs, profileProperties);
                return this.getCSSStyle(config, !config?.applyToCells);
            };
        });
    }

    getCSSStyle(config: ConditionalFormattingConfig, justFontStyles = false) {
        if (!config)
            return undefined;

        const style = {
            fontWeight: `${config.fontWeight} !important`,
            fontStyle: `${config.fontStyle} !important`,
            textDecoration: `${config.textDecoration} !important`,
        };

        if (justFontStyles) {
            return style;
        }

        style['backgroundColor'] = `${config.backgroundColor} !important`;
        style['color'] = `${config.fontColor} !important`;

        return style;
    }

    enableColumnsOptions(gridOptions: GridOptions) {
        gridOptions.defaultColDef = {
            ...gridOptions.defaultColDef,
            enableValue: true,
            enableRowGroup: true,
            enablePivot: true,
        };

        gridOptions.pivotMode = false;
        gridOptions.pivotPanelShow = "always";
        gridOptions.pivotDefaultExpanded = -1;
        gridOptions.rowGroupPanelShow = "onlyWhenGrouping";
        gridOptions.sideBar = "";
    }

    toggleColumnsOptions(gridApi: GridApi, visible: boolean) {
        gridApi.setGridOption('sideBar', visible ? "columns" : null);
    }

    toggleFloatingFilters(gridApi: GridApi, enable: boolean) {
        const updatedDefs = gridApi.getColumnDefs().forEach(colDef => {
            colDef.floatingFilter = enable;
        });

        gridApi.setGridOption('columnDefs', updatedDefs);
    }
}
