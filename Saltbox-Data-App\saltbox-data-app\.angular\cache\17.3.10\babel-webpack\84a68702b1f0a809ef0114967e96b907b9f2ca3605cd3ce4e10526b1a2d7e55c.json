{"ast": null, "code": "import { CustomCellRenderer } from './ag-grid-custom-cell-renderer';\nimport { UserPermissionLevels } from '../../enums/shared';\nimport { SBFromlyRendererDialogComponent } from '../../../sb-formly-renderer/sb-formly-renderer-dialog/sb-formly-renderer-dialog.component';\nimport { TooltipModule } from 'primeng/tooltip';\nimport { ButtonModule } from 'primeng/button';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/actionable-grid/services/change-history.service\";\nimport * as i2 from \"../../services/notification.service\";\nimport * as i3 from \"../../services/ag-grid.service\";\nimport * as i4 from \"src/app/sb-formly-renderer/services/sb-formly.services\";\nimport * as i5 from \"primeng/button\";\nimport * as i6 from \"primeng/tooltip\";\nconst _c0 = [\"targetElement\"];\nfunction AgGridButtonRendererComponent_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 1)(1, \"p-button\", 3, 0);\n    i0.ɵɵlistener(\"onClick\", function AgGridButtonRendererComponent_Conditional_0_Template_p_button_onClick_1_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.buttonClick());\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"outlined\", true)(\"icon\", ctx_r1.btnIcon);\n  }\n}\nfunction AgGridButtonRendererComponent_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"app-sb-formly-renderer-dialog\", 4);\n    i0.ɵɵtwoWayListener(\"visibleChange\", function AgGridButtonRendererComponent_Conditional_1_Template_app_sb_formly_renderer_dialog_visibleChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.showObjectFieldDialog, $event) || (ctx_r1.showObjectFieldDialog = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵtwoWayProperty(\"visible\", ctx_r1.showObjectFieldDialog);\n    i0.ɵɵproperty(\"baseSchema\", ctx_r1.cellRendererInstance == null ? null : ctx_r1.cellRendererInstance.fieldBaseSchema)(\"fieldsConfig\", ctx_r1.objectFieldConfig)(\"data\", ctx_r1.cellRendererInstance == null ? null : ctx_r1.cellRendererInstance.cellRendererParams == null ? null : ctx_r1.cellRendererInstance.cellRendererParams.value)(\"readonly\", true)(\"headerText\", ctx_r1.cellRendererInstance.cellRendererParams.colDef == null ? null : ctx_r1.cellRendererInstance.cellRendererParams.colDef.headerName);\n  }\n}\n/* for format config driven checkbox only*/\nexport class AgGridButtonRendererComponent extends CustomCellRenderer {\n  constructor(changeHistoryService, notificationService, aGGridService, sBFormlyService) {\n    super(changeHistoryService, notificationService, aGGridService);\n    this.sBFormlyService = sBFormlyService;\n    // Action button variables\n    this.showObjectFieldDialog = false;\n    this.btnIcon = 'pi pi-pencil';\n  }\n  agInit(cellRendererParams) {\n    super.agInit(cellRendererParams);\n    if (!this.paramsContext.userPermissionLevel || [UserPermissionLevels.View, UserPermissionLevels.None].includes(this.paramsContext.userPermissionLevel)) this.btnIcon = 'pi pi-ellipsis-h';else if (this.paramsContext.buttonIcon) this.btnIcon = this.paramsContext.buttonIcon;\n  }\n  buttonClick() {\n    if (this.paramsContext?.buttonClick) this.paramsContext?.buttonClick(this);else this.handleActionButtonClick();\n  }\n  // Not this is just to open the object forms (default form) just to view the data, \n  // for custom action or edit the data please implement the buttonClick\n  handleActionButtonClick() {\n    if (!this.cellRendererInstance?.fieldBaseSchema) return;\n    this.objectFieldConfig = this.sBFormlyService.getDefaultFormlyFieldsBySchema(this.cellRendererInstance.fieldBaseSchema);\n    if (this.objectFieldConfig) this.showObjectFieldDialog = true;\n  }\n  get paramsContext() {\n    if (!this.cellRendererParams?.context) {\n      return undefined;\n    }\n    return this.cellRendererParams?.context;\n  }\n  focusElement() {\n    this.elementRef?.nativeElement?.focus();\n  }\n  static {\n    this.ɵfac = function AgGridButtonRendererComponent_Factory(t) {\n      return new (t || AgGridButtonRendererComponent)(i0.ɵɵdirectiveInject(i1.ChangeHistoryService), i0.ɵɵdirectiveInject(i2.NotificationService), i0.ɵɵdirectiveInject(i3.AGGridService), i0.ɵɵdirectiveInject(i4.SBFormlyService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AgGridButtonRendererComponent,\n      selectors: [[\"app-button-renderer\"]],\n      viewQuery: function AgGridButtonRendererComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.elementRef = _t.first);\n        }\n      },\n      standalone: true,\n      features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n      decls: 2,\n      vars: 2,\n      consts: [[\"targetElement\", \"\"], [1, \"actionable-grid-renderer\", \"w-full\", \"flex\", \"justify-content-start\", \"mt-1\", \"align-items-center\"], [3, \"visible\", \"baseSchema\", \"fieldsConfig\", \"data\", \"readonly\", \"headerText\"], [\"pTooltip\", \"\", 1, \"line-height-1\", 3, \"onClick\", \"outlined\", \"icon\"], [3, \"visibleChange\", \"visible\", \"baseSchema\", \"fieldsConfig\", \"data\", \"readonly\", \"headerText\"]],\n      template: function AgGridButtonRendererComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, AgGridButtonRendererComponent_Conditional_0_Template, 3, 2, \"div\", 1)(1, AgGridButtonRendererComponent_Conditional_1_Template, 1, 6, \"app-sb-formly-renderer-dialog\", 2);\n        }\n        if (rf & 2) {\n          i0.ɵɵconditional(0, ctx.showEditor && (!(ctx.paramsContext == null ? null : ctx.paramsContext.hasDataToShowButton) || (ctx.paramsContext == null ? null : ctx.paramsContext.hasDataToShowButton) && (ctx.cellRendererParams == null ? null : ctx.cellRendererParams.value)) ? 0 : -1);\n          i0.ɵɵadvance();\n          i0.ɵɵconditional(1, ctx.showObjectFieldDialog ? 1 : -1);\n        }\n      },\n      dependencies: [ButtonModule, i5.Button, TooltipModule, i6.Tooltip, SBFromlyRendererDialogComponent],\n      encapsulation: 2\n    });\n  }\n}", "map": {"version": 3, "names": ["CustomCellRenderer", "UserPermissionLevels", "SBFromlyRendererDialogComponent", "TooltipModule", "ButtonModule", "i0", "ɵɵelementStart", "ɵɵlistener", "AgGridButtonRendererComponent_Conditional_0_Template_p_button_onClick_1_listener", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "buttonClick", "ɵɵelementEnd", "ɵɵadvance", "ɵɵproperty", "btnIcon", "ɵɵtwoWayListener", "AgGridButtonRendererComponent_Conditional_1_Template_app_sb_formly_renderer_dialog_visibleChange_0_listener", "$event", "_r3", "ɵɵtwoWayBindingSet", "showObjectFieldDialog", "ɵɵtwoWayProperty", "cellRendererInstance", "fieldBaseSchema", "objectFieldConfig", "cellRendererParams", "value", "colDef", "headerName", "AgGridButtonRendererComponent", "constructor", "changeHistoryService", "notificationService", "aGGridService", "sBFormlyService", "agInit", "paramsContext", "userPermissionLevel", "View", "None", "includes", "buttonIcon", "handleActionButtonClick", "getDefaultFormlyFieldsBySchema", "context", "undefined", "focusElement", "elementRef", "nativeElement", "focus", "ɵɵdirectiveInject", "i1", "ChangeHistoryService", "i2", "NotificationService", "i3", "AGGridService", "i4", "SBFormlyService", "selectors", "viewQuery", "AgGridButtonRendererComponent_Query", "rf", "ctx", "ɵɵtemplate", "AgGridButtonRendererComponent_Conditional_0_Template", "AgGridButtonRendererComponent_Conditional_1_Template", "ɵɵconditional", "showEditor", "hasDataToShowButton", "i5", "<PERSON><PERSON>", "i6", "<PERSON><PERSON><PERSON>", "encapsulation"], "sources": ["C:\\Projects\\SaltboxActionableGrid\\Saltbox-Data-App\\saltbox-data-app\\src\\app\\core\\ag-grid\\renderers\\ag-grid-button-renderer.component.ts", "C:\\Projects\\SaltboxActionableGrid\\Saltbox-Data-App\\saltbox-data-app\\src\\app\\core\\ag-grid\\renderers\\ag-grid-button-renderer.component.html"], "sourcesContent": ["import { Component, ElementRef, ViewChild } from '@angular/core';\r\nimport { ICustomCellRendererParams } from './ag-grid-custom-cell-renderer-params';\r\nimport { CustomCellRenderer } from './ag-grid-custom-cell-renderer';\r\nimport { NotificationService } from '../../services/notification.service';\r\nimport { ChangeHistoryService } from 'src/app/actionable-grid/services/change-history.service';\r\nimport { IRowActionsParamsContext } from '../../models/row-actions-params-context';\r\nimport { AGGridService } from '../../services/ag-grid.service';\r\nimport { FormlyFieldConfig } from '@ngx-formly/core';\r\nimport { SBFormlyService } from 'src/app/sb-formly-renderer/services/sb-formly.services';\r\nimport { UserPermissionLevels } from '../../enums/shared';\r\nimport { SBFromlyRendererDialogComponent } from '../../../sb-formly-renderer/sb-formly-renderer-dialog/sb-formly-renderer-dialog.component';\r\nimport { TooltipModule } from 'primeng/tooltip';\r\nimport { ButtonModule } from 'primeng/button';\r\nimport { NgIf } from '@angular/common';\r\n\r\n/* for format config driven checkbox only*/\r\n@Component({\r\n    selector: 'app-button-renderer',\r\n    templateUrl: './ag-grid-button-renderer.component.html',\r\n    standalone: true,\r\n    imports: [NgIf, ButtonModule, TooltipModule, SBFromlyRendererDialogComponent]\r\n})\r\nexport class AgGridButtonRendererComponent extends CustomCellRenderer {\r\n    @ViewChild('targetElement', { static: false }) elementRef: ElementRef;\r\n\r\n    // Action button variables\r\n    showObjectFieldDialog = false;\r\n    objectFieldConfig: FormlyFieldConfig[];\r\n    btnIcon = 'pi pi-pencil';\r\n\r\n    constructor(\r\n        changeHistoryService: ChangeHistoryService,\r\n        notificationService: NotificationService,\r\n        aGGridService: AGGridService,\r\n        private sBFormlyService: SBFormlyService) {\r\n        super(changeHistoryService, notificationService, aGGridService);\r\n    }\r\n\r\n    agInit(cellRendererParams: ICustomCellRendererParams): void {\r\n        super.agInit(cellRendererParams);\r\n\r\n        if (!this.paramsContext.userPermissionLevel || [UserPermissionLevels.View, UserPermissionLevels.None].includes(this.paramsContext.userPermissionLevel))\r\n            this.btnIcon = 'pi pi-ellipsis-h';\r\n        else if (this.paramsContext.buttonIcon)\r\n            this.btnIcon = this.paramsContext.buttonIcon\r\n    }\r\n\r\n    buttonClick(): void {\r\n        if (this.paramsContext?.buttonClick)\r\n            this.paramsContext?.buttonClick(this);\r\n        else\r\n            this.handleActionButtonClick();\r\n    }\r\n\r\n    // Not this is just to open the object forms (default form) just to view the data, \r\n    // for custom action or edit the data please implement the buttonClick\r\n    handleActionButtonClick(): void {\r\n        if (!this.cellRendererInstance?.fieldBaseSchema)\r\n            return;\r\n\r\n        this.objectFieldConfig = this.sBFormlyService.getDefaultFormlyFieldsBySchema(this.cellRendererInstance.fieldBaseSchema);\r\n\r\n        if (this.objectFieldConfig)\r\n            this.showObjectFieldDialog = true;\r\n    }\r\n\r\n    get paramsContext(): IRowActionsParamsContext {\r\n        if (!this.cellRendererParams?.context) {\r\n            return undefined;\r\n        }\r\n\r\n        return this.cellRendererParams?.context as IRowActionsParamsContext;\r\n    }\r\n\r\n    focusElement(): void {\r\n        this.elementRef?.nativeElement?.focus();\r\n    }\r\n}\r\n", "@if (showEditor && (!paramsContext?.hasDataToShowButton || (paramsContext?.hasDataToShowButton && cellRendererParams?.value))) {\r\n    <div class=\"actionable-grid-renderer w-full flex justify-content-start mt-1 align-items-center\">\r\n        <p-button #targetElement [outlined]=\"true\" class=\"line-height-1\" [icon]=\"btnIcon\" (onClick)=\"buttonClick()\"\r\n            pTooltip=\"\"></p-button>\r\n    </div>\r\n}\r\n@if(showObjectFieldDialog) {\r\n    <app-sb-formly-renderer-dialog [(visible)]=\"showObjectFieldDialog\"\r\n        [baseSchema]=\"cellRendererInstance?.fieldBaseSchema\" [fieldsConfig]=\"this.objectFieldConfig\"\r\n        [data]=\"cellRendererInstance?.cellRendererParams?.value\" [readonly]=\"true\"\r\n        [headerText]=\"cellRendererInstance.cellRendererParams.colDef?.headerName\">\r\n    </app-sb-formly-renderer-dialog>\r\n}"], "mappings": "AAEA,SAASA,kBAAkB,QAAQ,gCAAgC;AAOnE,SAASC,oBAAoB,QAAQ,oBAAoB;AACzD,SAASC,+BAA+B,QAAQ,2FAA2F;AAC3I,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,YAAY,QAAQ,gBAAgB;;;;;;;;;;;;ICVrCC,EADJ,CAAAC,cAAA,aAAgG,qBAE5E;IADkED,EAAA,CAAAE,UAAA,qBAAAC,iFAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAWF,MAAA,CAAAG,WAAA,EAAa;IAAA,EAAC;IAE/GT,EADoB,CAAAU,YAAA,EAAW,EACzB;;;;IAFuBV,EAAA,CAAAW,SAAA,EAAiB;IAAuBX,EAAxC,CAAAY,UAAA,kBAAiB,SAAAN,MAAA,CAAAO,OAAA,CAAuC;;;;;;IAKrFb,EAAA,CAAAC,cAAA,uCAG8E;IAH/CD,EAAA,CAAAc,gBAAA,2BAAAC,4GAAAC,MAAA;MAAAhB,EAAA,CAAAI,aAAA,CAAAa,GAAA;MAAA,MAAAX,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAAkB,kBAAA,CAAAZ,MAAA,CAAAa,qBAAA,EAAAH,MAAA,MAAAV,MAAA,CAAAa,qBAAA,GAAAH,MAAA;MAAA,OAAAhB,EAAA,CAAAQ,WAAA,CAAAQ,MAAA;IAAA,EAAmC;IAIlEhB,EAAA,CAAAU,YAAA,EAAgC;;;;IAJDV,EAAA,CAAAoB,gBAAA,YAAAd,MAAA,CAAAa,qBAAA,CAAmC;IAG9DnB,EAFA,CAAAY,UAAA,eAAAN,MAAA,CAAAe,oBAAA,kBAAAf,MAAA,CAAAe,oBAAA,CAAAC,eAAA,CAAoD,iBAAAhB,MAAA,CAAAiB,iBAAA,CAAwC,SAAAjB,MAAA,CAAAe,oBAAA,kBAAAf,MAAA,CAAAe,oBAAA,CAAAG,kBAAA,kBAAAlB,MAAA,CAAAe,oBAAA,CAAAG,kBAAA,CAAAC,KAAA,CACpC,kBAAkB,eAAAnB,MAAA,CAAAe,oBAAA,CAAAG,kBAAA,CAAAE,MAAA,kBAAApB,MAAA,CAAAe,oBAAA,CAAAG,kBAAA,CAAAE,MAAA,CAAAC,UAAA,CACD;;;ADKjF;AAOA,OAAM,MAAOC,6BAA8B,SAAQjC,kBAAkB;EAQjEkC,YACIC,oBAA0C,EAC1CC,mBAAwC,EACxCC,aAA4B,EACpBC,eAAgC;IACxC,KAAK,CAACH,oBAAoB,EAAEC,mBAAmB,EAAEC,aAAa,CAAC;IADvD,KAAAC,eAAe,GAAfA,eAAe;IAT3B;IACA,KAAAd,qBAAqB,GAAG,KAAK;IAE7B,KAAAN,OAAO,GAAG,cAAc;EAQxB;EAEAqB,MAAMA,CAACV,kBAA6C;IAChD,KAAK,CAACU,MAAM,CAACV,kBAAkB,CAAC;IAEhC,IAAI,CAAC,IAAI,CAACW,aAAa,CAACC,mBAAmB,IAAI,CAACxC,oBAAoB,CAACyC,IAAI,EAAEzC,oBAAoB,CAAC0C,IAAI,CAAC,CAACC,QAAQ,CAAC,IAAI,CAACJ,aAAa,CAACC,mBAAmB,CAAC,EAClJ,IAAI,CAACvB,OAAO,GAAG,kBAAkB,CAAC,KACjC,IAAI,IAAI,CAACsB,aAAa,CAACK,UAAU,EAClC,IAAI,CAAC3B,OAAO,GAAG,IAAI,CAACsB,aAAa,CAACK,UAAU;EACpD;EAEA/B,WAAWA,CAAA;IACP,IAAI,IAAI,CAAC0B,aAAa,EAAE1B,WAAW,EAC/B,IAAI,CAAC0B,aAAa,EAAE1B,WAAW,CAAC,IAAI,CAAC,CAAC,KAEtC,IAAI,CAACgC,uBAAuB,EAAE;EACtC;EAEA;EACA;EACAA,uBAAuBA,CAAA;IACnB,IAAI,CAAC,IAAI,CAACpB,oBAAoB,EAAEC,eAAe,EAC3C;IAEJ,IAAI,CAACC,iBAAiB,GAAG,IAAI,CAACU,eAAe,CAACS,8BAA8B,CAAC,IAAI,CAACrB,oBAAoB,CAACC,eAAe,CAAC;IAEvH,IAAI,IAAI,CAACC,iBAAiB,EACtB,IAAI,CAACJ,qBAAqB,GAAG,IAAI;EACzC;EAEA,IAAIgB,aAAaA,CAAA;IACb,IAAI,CAAC,IAAI,CAACX,kBAAkB,EAAEmB,OAAO,EAAE;MACnC,OAAOC,SAAS;IACpB;IAEA,OAAO,IAAI,CAACpB,kBAAkB,EAAEmB,OAAmC;EACvE;EAEAE,YAAYA,CAAA;IACR,IAAI,CAACC,UAAU,EAAEC,aAAa,EAAEC,KAAK,EAAE;EAC3C;;;uBAtDSpB,6BAA6B,EAAA5B,EAAA,CAAAiD,iBAAA,CAAAC,EAAA,CAAAC,oBAAA,GAAAnD,EAAA,CAAAiD,iBAAA,CAAAG,EAAA,CAAAC,mBAAA,GAAArD,EAAA,CAAAiD,iBAAA,CAAAK,EAAA,CAAAC,aAAA,GAAAvD,EAAA,CAAAiD,iBAAA,CAAAO,EAAA,CAAAC,eAAA;IAAA;EAAA;;;YAA7B7B,6BAA6B;MAAA8B,SAAA;MAAAC,SAAA,WAAAC,oCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;;;UChB1C7D,EANA,CAAA+D,UAAA,IAAAC,oDAAA,iBAAgI,IAAAC,oDAAA,2CAMpG;;;UAN5BjE,EAAA,CAAAkE,aAAA,IAAAJ,GAAA,CAAAK,UAAA,OAAAL,GAAA,CAAA3B,aAAA,kBAAA2B,GAAA,CAAA3B,aAAA,CAAAiC,mBAAA,MAAAN,GAAA,CAAA3B,aAAA,kBAAA2B,GAAA,CAAA3B,aAAA,CAAAiC,mBAAA,MAAAN,GAAA,CAAAtC,kBAAA,kBAAAsC,GAAA,CAAAtC,kBAAA,CAAAC,KAAA,YAKC;UACDzB,EAAA,CAAAW,SAAA,EAMC;UANDX,EAAA,CAAAkE,aAAA,IAAAJ,GAAA,CAAA3C,qBAAA,UAMC;;;qBDQmBpB,YAAY,EAAAsE,EAAA,CAAAC,MAAA,EAAExE,aAAa,EAAAyE,EAAA,CAAAC,OAAA,EAAE3E,+BAA+B;MAAA4E,aAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}