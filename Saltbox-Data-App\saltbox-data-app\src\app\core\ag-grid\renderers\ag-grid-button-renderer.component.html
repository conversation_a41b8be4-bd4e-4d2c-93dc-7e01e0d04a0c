@if (showEditor && (!paramsContext?.hasDataToShowButton || (paramsContext?.hasDataToShowButton && cellRendererParams?.value))) {
    <div class="actionable-grid-renderer w-full flex justify-content-start mt-1 align-items-center">
        <p-button #targetElement [outlined]="true" class="line-height-1" [icon]="btnIcon" (onClick)="buttonClick()"
            pTooltip=""></p-button>
    </div>
}
@if(showObjectFieldDialog) {
    <app-sb-formly-renderer-dialog [(visible)]="showObjectFieldDialog"
        [baseSchema]="cellRendererInstance?.fieldBaseSchema" [fieldsConfig]="this.objectFieldConfig"
        [data]="cellRendererInstance?.cellRendererParams?.value" [readonly]="true"
        [headerText]="cellRendererInstance.cellRendererParams.colDef?.headerName">
    </app-sb-formly-renderer-dialog>
}