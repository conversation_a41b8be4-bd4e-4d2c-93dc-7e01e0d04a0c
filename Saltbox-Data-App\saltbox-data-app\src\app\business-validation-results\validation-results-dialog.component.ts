import { Component, Input, EventEmitter, Output, OnInit } from '@angular/core';
import { ColDef, GridApi, GridOptions } from 'ag-grid-community';
import { SaveRecordsResponse } from '../core/models/save-records-response';
import { ValidationLevels, ValidationRuleTypes } from '../core/enums/business-validations.enum';
import { ScriptValidationResult } from '../core/models/script-validation-result';
import { DB_PRIMARY_KEY } from '../shared/constants/record.const';
import { getEnumKeyByValue } from '../shared/utilities/helper.functions';
import { Datastore } from '../shared/models/datastore';
import { ExtendedJsonSchema } from '../shared/models/extended-json-schema';
import { JsonDataTypes } from '../shared/enums/json-schema.enum';
import { AgGridIconRendererComponent } from '../core/ag-grid/renderers/ag-grid-icon.renderer.component';
import { MergedValidationResult } from '../core/models/merged-validation-result';
import { ButtonModule } from 'primeng/button';
import { AgGridModule } from 'ag-grid-angular';
import { NgClass, NgStyle, NgIf } from '@angular/common';
import { SharedModule } from 'primeng/api';
import { SidebarModule } from 'primeng/sidebar';

@Component({
    selector: 'app-validation-results-dialog',
    templateUrl: './validation-results-dialog.component.html',
    standalone: true,
    imports: [SidebarModule, SharedModule, NgClass, NgStyle, NgIf, AgGridModule, ButtonModule]
})
export class ValidationResultsDialogComponent implements OnInit {
    @Input() visible = false;
    @Input() saveRecordsResponse: SaveRecordsResponse;
    @Input() data: any[];
    @Input() datastore: Datastore;
    @Input() singleRecordMode = false;
    @Input() masterKeyColumn: string;

    @Output() hideDialog = new EventEmitter<void>();
    @Output() visibleChange = new EventEmitter<boolean>();

    validationResult: MergedValidationResult[] = [];
    protected masterKeyColumnName: string;
    private gridApi: GridApi;

    columnDefs: ColDef[];

    gridOptions: GridOptions = {
        onGridReady: this.onGridReady.bind(this),
        defaultColDef: {
            resizable: true,
            sortable: true,
            suppressHeaderMenuButton: true,
        }
    };

    ngOnInit(): void {
        this.columnDefs = [
            { field: 'recordId', headerName: 'Record Id', hide: true },
            { field: 'masterPK', headerName: 'Master PK', rowGroup: !this.singleRecordMode, hide: true },
            { field: 'keyColValue', headerName: 'Key', hide: !this.singleRecordMode },
            { field: 'isValid', headerName: 'Is Valid', hide: true },
            { field: 'hasFailures', headerName: 'Has Failures', hide: true },
            { field: 'hasWarnings', headerName: 'Has Warnings', hide: true },
            // Fields from ScriptRuleResult
            { field: 'ruleId', headerName: 'Rule ID', hide: true },
            { field: 'ruleName', headerName: 'Rule Name' },
            { field: 'level', headerName: 'Severity' },
            { field: 'message', headerName: 'Message' },
            { field: 'fieldLevel', headerName: 'Level' },
            { field: 'fieldName', headerName: 'Field Name' },
            { field: 'fieldJsonPath', headerName: 'Field JSON Path', hide: true },
            {
                field: 'log', headerName: 'Log',
                cellRenderer: AgGridIconRendererComponent,
                cellRendererParams: (params) => {
                    return {
                        iconClass: params.value ? 'pi-info-circle' : '',
                        tooltip: params.value
                    };
                }
            },
            { field: 'scriptFailed', headerName: 'Script Failed' },
            { field: 'errorMessage', headerName: 'Script Error Message' },
        ];

        // if there is no key column defined we skip the first column as it is _id (datastore PK) and take the second column as the primary key
        if (!this.masterKeyColumn)
            this.masterKeyColumn = this.data?.length ? this.getSecondPropertyOfObject(this.data[0])?.column : undefined;

        const pkDisplayName = this.datastore?.baseSchema?.properties[this.masterKeyColumn]?.presentationProperties?.displayName;
        this.masterKeyColumnName = pkDisplayName != null && pkDisplayName?.trim() != '' ? pkDisplayName : this.masterKeyColumn;

        if (!this.singleRecordMode) {
            this.gridOptions.autoGroupColumnDef = {
                headerName: this.masterKeyColumnName,
                field: 'keyColValue'
            };
        }

        this.validationResult = this.getMergedValidationResults(this.saveRecordsResponse.failedValidations);
    }

    onGridReady(params) {
        this.gridApi = params.api;

        // Expand all rows if rowData is already set
        this.gridApi?.expandAll();
        this.gridApi?.autoSizeAllColumns();
    }

    getMergedValidationResults(results: ScriptValidationResult[]): MergedValidationResult[] {
        return results.reduce((acc, result) => {
            // transform each validation failure and concatenate the result
            const failures = result.validationFailures.map(failure => ({
                ...result,
                ...failure,
                fieldName: this.getFieldDisplayNameByRecordPath(failure.fieldJsonPath, failure.fieldName),
                masterPK: this.getMasterKeyColValue(result.recordId),
                keyColValue: failure.ruleType === ValidationRuleTypes.Field && failure.fieldJsonPath
                    ? this.getNestedObjectKeys(failure.fieldJsonPath, result.recordId, failure.rowId)
                    : this.getMasterKeyColValue(result.recordId, true),
                fieldLevel: getEnumKeyByValue(ValidationRuleTypes, failure.ruleType),
            }));
            return acc.concat(failures);
        }, []);
    }

    getFieldDisplayNameByRecordPath(fieldJsonPath: string, fieldName): any {
        if (!fieldName)
            return undefined;

        // Split the path into parts (field names and indexes)
        const parts = fieldJsonPath.split(/\.|\[|\]/).filter(Boolean);
        let currentProperty = this.datastore?.baseSchema;

        for (let i = 0; i < parts.length; i = i + 2) {
            // Even parts are field names
            if (currentProperty?.type === JsonDataTypes.Object)
                currentProperty = currentProperty.properties[parts[i]];
            else if (currentProperty?.type === JsonDataTypes.Collection)
                currentProperty = currentProperty.items.properties[parts[i]];
        }

        const displayName = currentProperty?.type === JsonDataTypes.Collection
            ? currentProperty?.items?.properties[fieldName]?.presentationProperties?.displayName
            : currentProperty?.properties[fieldName]?.presentationProperties?.displayName;

        return displayName && displayName.trim() !== '' ? displayName : fieldName;
    }

    getMasterKeyColValue(recordId: string, withColumnName = false): any {
        const record = this.singleRecordMode ? this.data[0] : this.data?.find(x => x[DB_PRIMARY_KEY] === recordId);
        if (!record)
            return undefined;

        const masterKeyColumnValue = record[this.masterKeyColumn];
        return withColumnName ? `${this.masterKeyColumnName}: ${masterKeyColumnValue}` : masterKeyColumnValue;
    }

    getNestedObjectKeys(path: string, recordId, rowId): string {
        let record = this.data.find(x => x[DB_PRIMARY_KEY] === recordId);
        if (!record)
            return undefined;

        // Split the path into parts (field names and indexes)
        const parts = path.split(/\.|\[|\]/).filter(Boolean);

        let currentProperty: ExtendedJsonSchema = this.datastore?.baseSchema;
        const objectKeys: string[] = [];

        for (let i = 0; i < parts.length; i++) {
            if (record === undefined) {
                return undefined;
            }

            if (i % 2 === 0) {
                // Even parts are field names
                record = record[parts[i]];
                if (currentProperty?.type === JsonDataTypes.Object)
                    currentProperty = currentProperty.properties[parts[i]];
                else if (currentProperty?.type === JsonDataTypes.Collection)
                    currentProperty = currentProperty.items.properties[parts[i]];
            } else {
                // Odd parts are array indexes
                const index = Number(parts[i]);
                record = Array.isArray(record) ? record[index] : undefined;

                const secondCol = this.getSecondPropertyOfObject(record);

                const displayName = currentProperty?.type === JsonDataTypes.Collection
                    ? currentProperty?.items?.properties[secondCol.column]?.presentationProperties?.displayName
                    : currentProperty?.properties[secondCol.column]?.presentationProperties?.displayName;

                objectKeys.push(`${displayName ?? secondCol?.column}: ${secondCol.value}`);
            }
        }

        if (!record || record[DB_PRIMARY_KEY] !== rowId) {
            return undefined;
        }

        return objectKeys.join(', ');
    }

    getSecondPropertyOfObject(obj): { column: string, value: any } {
        if (!obj)
            return undefined;

        const columns = Object.keys(obj);
        if (columns?.length <= 1)
            return undefined;

        return { column: columns[1], value: obj[columns[1]] };
    }

    getRowStyle = (params) => {
        if (params?.data?.level === ValidationLevels.Failure) {
            return { backgroundColor: '#fcebea' };
        }
    }

    closeDialog(byXButton: boolean) {
        this.visible = false;
        this.visibleChange.emit(false);

        if (byXButton)
            this.hideDialog.emit();
    }

    get failureErrorCount() {
        return this.saveRecordsResponse?.failedValidations?.filter(x => x.hasFailures)?.length;
    }
}
