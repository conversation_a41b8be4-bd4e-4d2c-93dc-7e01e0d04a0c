import { Component, ViewChild } from '@angular/core';
import { ICustomCellRendererParams } from './ag-grid-custom-cell-renderer-params';
import { CustomCellRenderer } from './ag-grid-custom-cell-renderer';
import { NotificationService } from '../../services/notification.service';
import { ChangeHistoryService } from 'src/app/actionable-grid/services/change-history.service';
import { AGGridService } from '../../services/ag-grid.service';
import { FormsModule } from '@angular/forms';
import { DropdownModule } from 'primeng/dropdown';

@Component({
    selector: 'app-dropdown-renderer',
    templateUrl: './ag-grid-dropdown-renderer.component.html',
    standalone: true,
    imports: [DropdownModule, FormsModule]
})
export class AgGridDropDownRendererComponent extends CustomCellRenderer {
    @ViewChild('targetElement') elementRef;
    constructor(
        changeHistoryService: ChangeHistoryService,
        notificationService: NotificationService,
        aGGridService: AGGridService) {
        super(changeHistoryService, notificationService, aGGridService);
      }

    dropdownArray = [];

    agInit(cellRendererParams: ICustomCellRendererParams): void {
        super.agInit(cellRendererParams);
        this.cellRendererParams = cellRendererParams;

        // ensures the current value is in the dropdown if it is not currently an option
        if (this.cellRendererParams.value !== undefined && this.cellRendererParams.value !== '' && !this.cellRendererParams.allowedValues.find(x => x.value === this.cellRendererParams.value)) {
            this.dropdownArray.push({
                label: this.cellRendererParams.value,
                value: this.cellRendererParams.value,
                disabled: !this.cellRendererParams.allowedValues.find(x => x.value === this.cellRendererParams.value)
            });
        }

        for (const allowedValue of this.cellRendererParams.allowedValues) {
            if (!this.dropdownArray.find(v => v.value === allowedValue.value)) {
                this.dropdownArray.push({
                    label: allowedValue.value,
                    value: allowedValue.value,
                    disabled: !this.cellRendererParams.allowUserUpdate
                });
            }
        }
    }

    focusElement(): void {
        this.elementRef?.focus();
    }

    onChangeData() {
        super.setData();
    }
}
