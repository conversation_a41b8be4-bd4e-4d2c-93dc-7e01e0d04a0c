{"ast": null, "code": "import { formatDate } from 'src/app/shared/utilities/format.functions';\nimport { CustomCellRenderer } from './ag-grid-custom-cell-renderer';\nimport { convertLocalDateToUTCDate, convertUTCDateToLocalDate, getDateByISO8601Format } from 'src/app/shared/utilities/date.functions';\nimport { EditorColumnType } from 'src/app/shared/enums/editor-column-type.enum';\nimport { CurrencySymbolPipe } from '../../../shared/pipes/currency-symbol.pipe';\nimport { InputNumberModule } from 'primeng/inputnumber';\nimport { CalendarModule } from 'primeng/calendar';\nimport { LookupSelectorComponent } from '../../../lookup/lookup-selector/lookup-selector.component';\nimport { InputTextModule } from 'primeng/inputtext';\nimport { FormsModule } from '@angular/forms';\nimport { TooltipModule } from 'primeng/tooltip';\nimport { NgIf } from '@angular/common';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/actionable-grid/services/change-history.service\";\nimport * as i2 from \"../../services/notification.service\";\nimport * as i3 from \"../../services/ag-grid.service\";\nimport * as i4 from \"primeng/tooltip\";\nimport * as i5 from \"@angular/forms\";\nimport * as i6 from \"primeng/inputtext\";\nimport * as i7 from \"primeng/calendar\";\nimport * as i8 from \"primeng/inputnumber\";\nconst _c0 = [\"targetElement\"];\nconst _c1 = (a0, a1) => [a0, a1];\nfunction AgGridEditableRendererComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    let tmp_1_0;\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (tmp_1_0 = ctx_r0.cellRendererParams == null ? null : ctx_r0.cellRendererParams.valueFormatted) !== null && tmp_1_0 !== undefined ? tmp_1_0 : ctx_r0.cellRendererParams == null ? null : ctx_r0.cellRendererParams.value, \"\\n\");\n  }\n}\nfunction AgGridEditableRendererComponent_div_1_div_1_app_lookup_selector_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"app-lookup-selector\", 10);\n    i0.ɵɵlistener(\"selectedDataChanged\", function AgGridEditableRendererComponent_div_1_div_1_app_lookup_selector_3_Template_app_lookup_selector_selectedDataChanged_0_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r0 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r0.setLookupData($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    let tmp_5_0;\n    const ctx_r0 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"fieldName\", ctx_r0.cellRendererParams.fieldName)(\"fieldDisplayName\", (tmp_5_0 = ctx_r0.cellRendererParams.fieldDisplayName) !== null && tmp_5_0 !== undefined ? tmp_5_0 : ctx_r0.cellRendererParams.fieldName)(\"fieldSchema\", ctx_r0.cellRendererParams.fieldBaseSchema)(\"projectVersionId\", ctx_r0.cellRendererParams.projectVersionId)(\"selectedValue\", ctx_r0.stringValue)(\"lookupConfig\", ctx_r0.cellRendererParams.fieldBaseSchema == null ? null : ctx_r0.cellRendererParams.fieldBaseSchema.presentationProperties == null ? null : ctx_r0.cellRendererParams.fieldBaseSchema.presentationProperties.lookupConfig);\n  }\n}\nfunction AgGridEditableRendererComponent_div_1_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 7)(1, \"input\", 8, 0);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function AgGridEditableRendererComponent_div_1_div_1_Template_input_ngModelChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r0.stringValue, $event) || (ctx_r0.stringValue = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"focusout\", function AgGridEditableRendererComponent_div_1_div_1_Template_input_focusout_1_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.onFocusOut());\n    })(\"keydown\", function AgGridEditableRendererComponent_div_1_div_1_Template_input_keydown_1_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.onKeydownEvent($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, AgGridEditableRendererComponent_div_1_div_1_app_lookup_selector_3_Template, 1, 6, \"app-lookup-selector\", 9);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r0.stringValue);\n    i0.ɵɵproperty(\"required\", ctx_r0.required);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.cellRendererParams.enableLookup && (ctx_r0.cellRendererParams.fieldBaseSchema == null ? null : ctx_r0.cellRendererParams.fieldBaseSchema.presentationProperties == null ? null : ctx_r0.cellRendererParams.fieldBaseSchema.presentationProperties.enableLookup));\n  }\n}\nfunction AgGridEditableRendererComponent_div_1_p_calendar_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-calendar\", 11, 0);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function AgGridEditableRendererComponent_div_1_p_calendar_2_Template_p_calendar_ngModelChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r0.dateValue, $event) || (ctx_r0.dateValue = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"focusout\", function AgGridEditableRendererComponent_div_1_p_calendar_2_Template_p_calendar_focusout_0_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.onFocusOut());\n    })(\"keydown\", function AgGridEditableRendererComponent_div_1_p_calendar_2_Template_p_calendar_keydown_0_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.onKeydownEvent($event));\n    })(\"onHide\", function AgGridEditableRendererComponent_div_1_p_calendar_2_Template_p_calendar_onHide_0_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.onFocusOut());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r0.dateValue);\n    i0.ɵɵproperty(\"showOtherMonths\", true)(\"selectOtherMonths\", true)(\"showSeconds\", ctx_r0.cellRendererParams.format.type === ctx_r0.actionableGridColumnType.DateTime)(\"showTime\", ctx_r0.cellRendererParams.format.type === ctx_r0.actionableGridColumnType.DateTime)(\"showWeek\", false)(\"iconDisplay\", \"input\")(\"showIcon\", true)(\"showOnFocus\", false)(\"monthNavigator\", true)(\"yearNavigator\", true)(\"readonlyInput\", false)(\"placeholder\", ctx_r0.validValue ? \"\" : ctx_r0.cellRendererParams.value)(\"required\", ctx_r0.required);\n  }\n}\nfunction AgGridEditableRendererComponent_div_1_p_inputNumber_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-inputNumber\", 12, 0);\n    i0.ɵɵpipe(2, \"currencySymbol\");\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function AgGridEditableRendererComponent_div_1_p_inputNumber_3_Template_p_inputNumber_ngModelChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r0.numericValue, $event) || (ctx_r0.numericValue = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"focusout\", function AgGridEditableRendererComponent_div_1_p_inputNumber_3_Template_p_inputNumber_focusout_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.onFocusOut());\n    })(\"keydown\", function AgGridEditableRendererComponent_div_1_p_inputNumber_3_Template_p_inputNumber_keydown_0_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.onKeydownEvent($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"required\", ctx_r0.required);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r0.numericValue);\n    i0.ɵɵproperty(\"prefix\", ctx_r0.cellRendererParams.format.type === ctx_r0.actionableGridColumnType.Currency ? i0.ɵɵpipeBind1(2, 6, ctx_r0.cellRendererParams.format.currency) : \"\")(\"minFractionDigits\", ctx_r0.cellRendererParams.format.decimalPlaces)(\"maxFractionDigits\", ctx_r0.cellRendererParams.format.decimalPlaces)(\"placeholder\", ctx_r0.validValue ? \"\" : ctx_r0.cellRendererParams.value);\n  }\n}\nfunction AgGridEditableRendererComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 3);\n    i0.ɵɵtemplate(1, AgGridEditableRendererComponent_div_1_div_1_Template, 4, 3, \"div\", 4)(2, AgGridEditableRendererComponent_div_1_p_calendar_2_Template, 2, 14, \"p-calendar\", 5)(3, AgGridEditableRendererComponent_div_1_p_inputNumber_3_Template, 3, 8, \"p-inputNumber\", 6);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"tooltipDisabled\", ctx_r0.validValue);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.cellRendererParams.format.type === ctx_r0.actionableGridColumnType.String);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", i0.ɵɵpureFunction2(4, _c1, ctx_r0.actionableGridColumnType.Date, ctx_r0.actionableGridColumnType.DateTime).includes(ctx_r0.cellRendererParams.format.type));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.cellRendererParams.format.type === ctx_r0.actionableGridColumnType.Currency || ctx_r0.cellRendererParams.format.type === ctx_r0.actionableGridColumnType.Numeric);\n  }\n}\nexport class AgGridEditableRendererComponent extends CustomCellRenderer {\n  constructor(changeHistoryService, notificationService, aGGridService) {\n    super(changeHistoryService, notificationService, aGGridService);\n    this.actionableGridColumnType = EditorColumnType;\n    // Used by p-calendar and p-inputNumber to enable placeholder and tooltip that states the value is not valid.\n    this.validValue = true;\n  }\n  agInit(cellRendererParams) {\n    super.agInit(cellRendererParams);\n    if (this.validateValue() && this.cellRendererParams.value != null) this.setInitialValue();\n  }\n  setLookupData(data) {\n    if (data) super.groupUpdate(data);\n  }\n  setInitialValue() {\n    switch (this.cellRendererParams.format.type) {\n      case EditorColumnType.Date:\n        this.dateValue = getDateByISO8601Format(this.cellRendererParams.value);\n        break;\n      case EditorColumnType.DateTime:\n        // Note: currently we are ignoring the time zone, if we don't want to ignore the time zone it should be: new Date(this.cellRendererParams.value);\n        this.dateValue = convertUTCDateToLocalDate(this.cellRendererParams.value);\n        break;\n      case EditorColumnType.Numeric:\n      case EditorColumnType.Currency:\n        this.numericValue = Number(this.cellRendererParams.value);\n        break;\n      case EditorColumnType.String:\n        this.stringValue = this.cellRendererParams.value;\n        break;\n    }\n  }\n  onFocusOut() {\n    // **Note: We use a short delay for the calendars because when the datepicker is open and the user is clicking on specific date, \n    // first onFocusOut happens then onChange and it resets the datetime to the initial value!! \n    // Also we don't run the same function with 0 timer because if the next editor is a datepicker it open twice\n    if ([EditorColumnType.Date, EditorColumnType.DateTime].includes(this.cellRendererParams.format.type)) {\n      setTimeout(() => {\n        if (this.setParamValue()) super.setData();\n      }, 200);\n    } else {\n      if (this.setParamValue()) super.setData();\n    }\n  }\n  setParamValue() {\n    switch (this.cellRendererParams.format.type) {\n      case EditorColumnType.DateTime:\n      case EditorColumnType.Date:\n        // if user selected date and datarow dates are different\n        if (this.cellRendererParams.value != this.dateValue && this.dateValue?.toISOString() != convertUTCDateToLocalDate(this.cellRendererParams.value)?.toISOString()) {\n          // Note: currently we are ignoring the time zone, if we don't want to ignore the time zone it should be returning: this.dateValue?.toISOString()\n          this.cellRendererParams.value = this.cellRendererParams.format.type == EditorColumnType.DateTime ? this.dateValue ? convertLocalDateToUTCDate(this.dateValue) : undefined : this.dateValue ? formatDate(this.dateValue.toDateString()) : undefined;\n          return true;\n        }\n        return false;\n      case EditorColumnType.Numeric:\n      case EditorColumnType.Currency:\n        if (this.cellRendererParams.value != this.numericValue) {\n          this.cellRendererParams.value = this.numericValue;\n          return true;\n        }\n        return false;\n      case EditorColumnType.String:\n        if (this.cellRendererParams.value != this.stringValue?.trim()) {\n          this.cellRendererParams.value = this.stringValue?.trim();\n          return true;\n        }\n        return false;\n    }\n  }\n  // This function is to check whether the value is valid according to it's type.\n  validateValue() {\n    // This is to check if the value is a Date.\n    if (this.cellRendererParams.value != null && [EditorColumnType.Date, EditorColumnType.DateTime].includes(this.cellRendererParams.format.type)) {\n      this.validValue = formatDate(this.cellRendererParams.value) !== 'Invalid date';\n    }\n    // This is to check if the value is a Number.\n    if ([EditorColumnType.Numeric, EditorColumnType.Currency].includes(this.cellRendererParams.format.type)) {\n      this.validValue = this.cellRendererParams.value != null && !isNaN(Number(this.cellRendererParams.value));\n    }\n    return this.validValue;\n  }\n  focusElement() {\n    let elementRef;\n    switch (this.cellRendererParams.format.type) {\n      case EditorColumnType.String:\n        elementRef = this.elementRef;\n        break;\n      case EditorColumnType.DateTime:\n      case EditorColumnType.Date:\n        elementRef = this.elementRef?.inputfieldViewChild;\n        break;\n      case EditorColumnType.Numeric:\n      case EditorColumnType.Currency:\n        elementRef = this.elementRef?.input;\n        break;\n    }\n    elementRef?.nativeElement?.focus();\n    elementRef?.nativeElement?.select();\n  }\n  onKeydownEvent(event) {\n    // Stop propagation for certain keys\n    if (event.ctrlKey && ['C', 'c', 'V', 'v', 'X', 'x'].includes(event.key) || ['ArrowLeft', 'ArrowRight', 'ArrowUp', 'ArrowDown', 'Home', 'End'].includes(event.key)) {\n      event.stopPropagation();\n    }\n  }\n  static {\n    this.ɵfac = function AgGridEditableRendererComponent_Factory(t) {\n      return new (t || AgGridEditableRendererComponent)(i0.ɵɵdirectiveInject(i1.ChangeHistoryService), i0.ɵɵdirectiveInject(i2.NotificationService), i0.ɵɵdirectiveInject(i3.AGGridService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AgGridEditableRendererComponent,\n      selectors: [[\"app-input-text-renderer\"]],\n      viewQuery: function AgGridEditableRendererComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.elementRef = _t.first);\n        }\n      },\n      standalone: true,\n      features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n      decls: 2,\n      vars: 2,\n      consts: [[\"targetElement\", \"\"], [4, \"ngIf\"], [\"class\", \"actionable-grid-renderer\", \"pTooltip\", \"Invalid Value\", \"tooltipPosition\", \"top\", 3, \"tooltipDisabled\", 4, \"ngIf\"], [\"pTooltip\", \"Invalid Value\", \"tooltipPosition\", \"top\", 1, \"actionable-grid-renderer\", 3, \"tooltipDisabled\"], [\"class\", \"inline-flex p-inputgroup\", 4, \"ngIf\"], [\"appendTo\", \"body\", \"yearRange\", \"1900:2100\", \"dateFormat\", \"mm/dd/yy\", 3, \"ngModel\", \"showOtherMonths\", \"selectOtherMonths\", \"showSeconds\", \"showTime\", \"showWeek\", \"iconDisplay\", \"showIcon\", \"showOnFocus\", \"monthNavigator\", \"yearNavigator\", \"readonlyInput\", \"placeholder\", \"required\", \"ngModelChange\", \"focusout\", \"keydown\", \"onHide\", 4, \"ngIf\"], [\"showButtons\", \"true\", 3, \"required\", \"ngModel\", \"prefix\", \"minFractionDigits\", \"maxFractionDigits\", \"placeholder\", \"ngModelChange\", \"focusout\", \"keydown\", 4, \"ngIf\"], [1, \"inline-flex\", \"p-inputgroup\"], [\"type\", \"text\", \"pInputText\", \"\", 3, \"ngModelChange\", \"focusout\", \"keydown\", \"ngModel\", \"required\"], [\"class\", \"p-inputgroup-addon\", 3, \"fieldName\", \"fieldDisplayName\", \"fieldSchema\", \"projectVersionId\", \"selectedValue\", \"lookupConfig\", \"selectedDataChanged\", 4, \"ngIf\"], [1, \"p-inputgroup-addon\", 3, \"selectedDataChanged\", \"fieldName\", \"fieldDisplayName\", \"fieldSchema\", \"projectVersionId\", \"selectedValue\", \"lookupConfig\"], [\"appendTo\", \"body\", \"yearRange\", \"1900:2100\", \"dateFormat\", \"mm/dd/yy\", 3, \"ngModelChange\", \"focusout\", \"keydown\", \"onHide\", \"ngModel\", \"showOtherMonths\", \"selectOtherMonths\", \"showSeconds\", \"showTime\", \"showWeek\", \"iconDisplay\", \"showIcon\", \"showOnFocus\", \"monthNavigator\", \"yearNavigator\", \"readonlyInput\", \"placeholder\", \"required\"], [\"showButtons\", \"true\", 3, \"ngModelChange\", \"focusout\", \"keydown\", \"required\", \"ngModel\", \"prefix\", \"minFractionDigits\", \"maxFractionDigits\", \"placeholder\"]],\n      template: function AgGridEditableRendererComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, AgGridEditableRendererComponent_div_0_Template, 2, 1, \"div\", 1)(1, AgGridEditableRendererComponent_div_1_Template, 4, 7, \"div\", 2);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", ctx.cellRendererParams == null ? null : ctx.cellRendererParams.node == null ? null : ctx.cellRendererParams.node.group);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showEditor);\n        }\n      },\n      dependencies: [NgIf, TooltipModule, i4.Tooltip, FormsModule, i5.DefaultValueAccessor, i5.NgControlStatus, i5.RequiredValidator, i5.NgModel, InputTextModule, i6.InputText, LookupSelectorComponent, CalendarModule, i7.Calendar, InputNumberModule, i8.InputNumber, CurrencySymbolPipe],\n      encapsulation: 2\n    });\n  }\n}", "map": {"version": 3, "names": ["formatDate", "CustomCellRenderer", "convertLocalDateToUTCDate", "convertUTCDateToLocalDate", "getDateByISO8601Format", "EditorColumnType", "CurrencySymbolPipe", "InputNumberModule", "CalendarModule", "LookupSelectorComponent", "InputTextModule", "FormsModule", "TooltipModule", "NgIf", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "tmp_1_0", "ctx_r0", "cellRendererParams", "valueFormatted", "undefined", "value", "ɵɵlistener", "AgGridEditableRendererComponent_div_1_div_1_app_lookup_selector_3_Template_app_lookup_selector_selectedDataChanged_0_listener", "$event", "ɵɵrestoreView", "_r3", "ɵɵnextContext", "ɵɵresetView", "setLookupData", "ɵɵproperty", "fieldName", "tmp_5_0", "fieldDisplayName", "fieldBaseSchema", "projectVersionId", "stringValue", "presentationProperties", "lookupConfig", "ɵɵtwoWayListener", "AgGridEditableRendererComponent_div_1_div_1_Template_input_ngModelChange_1_listener", "_r2", "ɵɵtwoWayBindingSet", "AgGridEditableRendererComponent_div_1_div_1_Template_input_focusout_1_listener", "onFocusOut", "AgGridEditableRendererComponent_div_1_div_1_Template_input_keydown_1_listener", "onKeydownEvent", "ɵɵtemplate", "AgGridEditableRendererComponent_div_1_div_1_app_lookup_selector_3_Template", "ɵɵtwoWayProperty", "required", "enableLookup", "AgGridEditableRendererComponent_div_1_p_calendar_2_Template_p_calendar_ngModelChange_0_listener", "_r4", "dateValue", "AgGridEditableRendererComponent_div_1_p_calendar_2_Template_p_calendar_focusout_0_listener", "AgGridEditableRendererComponent_div_1_p_calendar_2_Template_p_calendar_keydown_0_listener", "AgGridEditableRendererComponent_div_1_p_calendar_2_Template_p_calendar_onHide_0_listener", "format", "type", "actionableGridColumnType", "DateTime", "validValue", "AgGridEditableRendererComponent_div_1_p_inputNumber_3_Template_p_inputNumber_ngModelChange_0_listener", "_r5", "numericValue", "AgGridEditableRendererComponent_div_1_p_inputNumber_3_Template_p_inputNumber_focusout_0_listener", "AgGridEditableRendererComponent_div_1_p_inputNumber_3_Template_p_inputNumber_keydown_0_listener", "<PERSON><PERSON><PERSON><PERSON>", "ɵɵpipeBind1", "currency", "decimalPlaces", "AgGridEditableRendererComponent_div_1_div_1_Template", "AgGridEditableRendererComponent_div_1_p_calendar_2_Template", "AgGridEditableRendererComponent_div_1_p_inputNumber_3_Template", "String", "ɵɵpureFunction2", "_c1", "Date", "includes", "Numeric", "AgGridEditableRendererComponent", "constructor", "changeHistoryService", "notificationService", "aGGridService", "agInit", "validate<PERSON><PERSON>ue", "setInitialValue", "data", "groupUpdate", "Number", "setTimeout", "setParamValue", "setData", "toISOString", "toDateString", "trim", "isNaN", "focusElement", "elementRef", "inputfieldViewChild", "input", "nativeElement", "focus", "select", "event", "ctrl<PERSON>ey", "key", "stopPropagation", "ɵɵdirectiveInject", "i1", "ChangeHistoryService", "i2", "NotificationService", "i3", "AGGridService", "selectors", "viewQuery", "AgGridEditableRendererComponent_Query", "rf", "ctx", "AgGridEditableRendererComponent_div_0_Template", "AgGridEditableRendererComponent_div_1_Template", "node", "group", "showEditor", "i4", "<PERSON><PERSON><PERSON>", "i5", "DefaultValueAccessor", "NgControlStatus", "RequiredValidator", "NgModel", "i6", "InputText", "i7", "Calendar", "i8", "InputNumber", "encapsulation"], "sources": ["C:\\Projects\\SaltboxActionableGrid\\Saltbox-Data-App\\saltbox-data-app\\src\\app\\core\\ag-grid\\renderers\\ag-grid-editable-renderer.component.ts", "C:\\Projects\\SaltboxActionableGrid\\Saltbox-Data-App\\saltbox-data-app\\src\\app\\core\\ag-grid\\renderers\\ag-grid-editable-renderer.component.html"], "sourcesContent": ["import { Component, ElementRef, ViewChild } from '@angular/core';\r\nimport { formatDate } from 'src/app/shared/utilities/format.functions';\r\nimport { ICustomCellRendererParams } from './ag-grid-custom-cell-renderer-params';\r\nimport { CustomCellRenderer } from './ag-grid-custom-cell-renderer';\r\nimport { NotificationService } from '../../services/notification.service';\r\nimport { convertLocalDateToUTCDate, convertUTCDateToLocalDate, getDateByISO8601Format } from 'src/app/shared/utilities/date.functions';\r\nimport { EditorColumnType } from 'src/app/shared/enums/editor-column-type.enum';\r\nimport { ChangeHistoryService } from 'src/app/actionable-grid/services/change-history.service';\r\nimport { AGGridService } from '../../services/ag-grid.service';\r\nimport { CurrencySymbolPipe } from '../../../shared/pipes/currency-symbol.pipe';\r\nimport { InputNumberModule } from 'primeng/inputnumber';\r\nimport { CalendarModule } from 'primeng/calendar';\r\nimport { LookupSelectorComponent } from '../../../lookup/lookup-selector/lookup-selector.component';\r\nimport { InputTextModule } from 'primeng/inputtext';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { TooltipModule } from 'primeng/tooltip';\r\nimport { NgIf } from '@angular/common';\r\n\r\n@Component({\r\n    selector: 'app-input-text-renderer',\r\n    templateUrl: 'ag-grid-editable-renderer.component.html',\r\n    standalone: true,\r\n    imports: [NgIf, TooltipModule, FormsModule, InputTextModule, LookupSelectorComponent, CalendarModule, InputNumberModule, CurrencySymbolPipe]\r\n})\r\nexport class AgGridEditableRendererComponent extends CustomCellRenderer {\r\n  @ViewChild('targetElement') elementRef;\r\n  constructor(\r\n    changeHistoryService: ChangeHistoryService,\r\n    notificationService: NotificationService,\r\n    aGGridService: AGGridService) {\r\n    super(changeHistoryService, notificationService, aGGridService);\r\n  }\r\n\r\n  actionableGridColumnType = EditorColumnType;\r\n\r\n  // This is used by p-calendar\r\n  dateValue: Date;\r\n\r\n  // This is used by p-inputNumber\r\n  numericValue: number;\r\n\r\n  // This is used by input\r\n  stringValue: string;\r\n\r\n  // Used by p-calendar and p-inputNumber to enable placeholder and tooltip that states the value is not valid.\r\n  validValue = true;\r\n\r\n  agInit(cellRendererParams: ICustomCellRendererParams): void {\r\n    super.agInit(cellRendererParams);\r\n\r\n    if (this.validateValue() && this.cellRendererParams.value != null)\r\n      this.setInitialValue();\r\n  }\r\n\r\n  setLookupData(data: any) {\r\n    if (data)\r\n      super.groupUpdate(data);\r\n  }\r\n\r\n  setInitialValue() {\r\n    switch (this.cellRendererParams.format.type) {\r\n      case EditorColumnType.Date:\r\n        this.dateValue = getDateByISO8601Format(this.cellRendererParams.value);\r\n        break;\r\n      case EditorColumnType.DateTime:\r\n        // Note: currently we are ignoring the time zone, if we don't want to ignore the time zone it should be: new Date(this.cellRendererParams.value);\r\n        this.dateValue = convertUTCDateToLocalDate(this.cellRendererParams.value);\r\n        break;\r\n      case EditorColumnType.Numeric:\r\n      case EditorColumnType.Currency:\r\n        this.numericValue = Number(this.cellRendererParams.value);\r\n        break;\r\n      case EditorColumnType.String:\r\n        this.stringValue = this.cellRendererParams.value;\r\n        break;\r\n    }\r\n  }\r\n\r\n  onFocusOut() {\r\n    // **Note: We use a short delay for the calendars because when the datepicker is open and the user is clicking on specific date, \r\n    // first onFocusOut happens then onChange and it resets the datetime to the initial value!! \r\n    // Also we don't run the same function with 0 timer because if the next editor is a datepicker it open twice\r\n    if ([EditorColumnType.Date, EditorColumnType.DateTime].includes(this.cellRendererParams.format.type)) {\r\n      setTimeout(() => {\r\n        if (this.setParamValue())\r\n          super.setData();\r\n      }, 200);\r\n    }\r\n    else {\r\n      if (this.setParamValue())\r\n        super.setData();\r\n    }\r\n  }\r\n\r\n  setParamValue(): boolean {\r\n    switch (this.cellRendererParams.format.type) {\r\n      case EditorColumnType.DateTime:\r\n      case EditorColumnType.Date:\r\n        // if user selected date and datarow dates are different\r\n        if (this.cellRendererParams.value != this.dateValue &&\r\n          this.dateValue?.toISOString() != convertUTCDateToLocalDate(this.cellRendererParams.value)?.toISOString()) {\r\n\r\n          // Note: currently we are ignoring the time zone, if we don't want to ignore the time zone it should be returning: this.dateValue?.toISOString()\r\n          this.cellRendererParams.value = this.cellRendererParams.format.type == EditorColumnType.DateTime\r\n            ? this.dateValue ? convertLocalDateToUTCDate(this.dateValue) : undefined\r\n            : this.dateValue ? formatDate(this.dateValue.toDateString()) : undefined;\r\n          return true;\r\n        }\r\n\r\n        return false;\r\n      case EditorColumnType.Numeric:\r\n      case EditorColumnType.Currency:\r\n        if (this.cellRendererParams.value != this.numericValue) {\r\n          this.cellRendererParams.value = this.numericValue;\r\n          return true;\r\n        }\r\n\r\n        return false;\r\n      case EditorColumnType.String:\r\n        if (this.cellRendererParams.value != this.stringValue?.trim()) {\r\n          this.cellRendererParams.value = this.stringValue?.trim()\r\n          return true;\r\n        }\r\n\r\n        return false;\r\n    }\r\n  }\r\n\r\n  // This function is to check whether the value is valid according to it's type.\r\n  validateValue(): boolean {\r\n    // This is to check if the value is a Date.\r\n    if (this.cellRendererParams.value != null && [EditorColumnType.Date, EditorColumnType.DateTime].includes(this.cellRendererParams.format.type)) {\r\n      this.validValue = formatDate(this.cellRendererParams.value) !== 'Invalid date';\r\n    }\r\n\r\n    // This is to check if the value is a Number.\r\n    if ([EditorColumnType.Numeric, EditorColumnType.Currency].includes(this.cellRendererParams.format.type)) {\r\n      this.validValue = this.cellRendererParams.value != null && !isNaN(Number(this.cellRendererParams.value));\r\n    }\r\n\r\n    return this.validValue;\r\n  }\r\n\r\n  focusElement(): void {\r\n    let elementRef: ElementRef;\r\n\r\n    switch (this.cellRendererParams.format.type) {\r\n      case EditorColumnType.String:\r\n        elementRef = this.elementRef;\r\n        break;\r\n      case EditorColumnType.DateTime:\r\n      case EditorColumnType.Date:\r\n        elementRef = this.elementRef?.inputfieldViewChild;\r\n        break;\r\n      case EditorColumnType.Numeric:\r\n      case EditorColumnType.Currency:\r\n        elementRef = this.elementRef?.input;\r\n        break;\r\n    }\r\n\r\n    elementRef?.nativeElement?.focus();\r\n    elementRef?.nativeElement?.select();\r\n  }\r\n\r\n  onKeydownEvent(event: KeyboardEvent): void {\r\n    // Stop propagation for certain keys\r\n    if ((event.ctrlKey && ['C', 'c', 'V', 'v', 'X', 'x'].includes(event.key))\r\n      || (['ArrowLeft', 'ArrowRight', 'ArrowUp', 'ArrowDown', 'Home', 'End'].includes(event.key))) {\r\n      event.stopPropagation();\r\n    }\r\n  }\r\n}\r\n", "<div *ngIf=\"cellRendererParams?.node?.group\">\r\n  {{ cellRendererParams?.valueFormatted ?? cellRendererParams?.value }}\r\n</div>\r\n<div *ngIf=\"showEditor\" class=\"actionable-grid-renderer\" pTooltip=\"Invalid Value\" tooltipPosition=\"top\"\r\n       [tooltipDisabled]=\"validValue\">\r\n       <div class=\"inline-flex p-inputgroup\" *ngIf=\"cellRendererParams.format.type === actionableGridColumnType.String\">\r\n              <input #targetElement type=\"text\" pInputText [(ngModel)]=\"stringValue\" [required]=\"required\"\r\n                     (focusout)=\"onFocusOut()\" (keydown)=\"onKeydownEvent($event)\">\r\n              <app-lookup-selector\r\n                     *ngIf=\"cellRendererParams.enableLookup && cellRendererParams.fieldBaseSchema?.presentationProperties?.enableLookup\"\r\n                     [fieldName]=\"cellRendererParams.fieldName\"\r\n                     [fieldDisplayName]=\"cellRendererParams.fieldDisplayName ?? cellRendererParams.fieldName\"\r\n                     [fieldSchema]=\"cellRendererParams.fieldBaseSchema\" (selectedDataChanged)=\"setLookupData($event)\"\r\n                     class=\"p-inputgroup-addon\" [projectVersionId]=\"cellRendererParams.projectVersionId\"\r\n                     [selectedValue]=\"stringValue\"\r\n                     [lookupConfig]=\"cellRendererParams.fieldBaseSchema?.presentationProperties?.lookupConfig\"></app-lookup-selector>\r\n       </div>\r\n       <p-calendar #targetElement\r\n              *ngIf=\"[actionableGridColumnType.Date, actionableGridColumnType.DateTime].includes(this.cellRendererParams.format.type)\"\r\n              [(ngModel)]=\"dateValue\" appendTo=\"body\" [showOtherMonths]=\"true\" [selectOtherMonths]=\"true\"\r\n              [showSeconds]=\"cellRendererParams.format.type === actionableGridColumnType.DateTime\"\r\n              [showTime]=\"cellRendererParams.format.type === actionableGridColumnType.DateTime\" [showWeek]=\"false\"\r\n              [iconDisplay]=\"'input'\" [showIcon]=\"true\" [showOnFocus]=\"false\" [monthNavigator]=\"true\"\r\n              [yearNavigator]=\"true\" yearRange=\"1900:2100\" [readonlyInput]=\"false\"\r\n              [placeholder]=\"validValue ? '' : cellRendererParams.value\" dateFormat=\"mm/dd/yy\" [required]=\"required\"\r\n              (focusout)=\"onFocusOut()\" (keydown)=\"onKeydownEvent($event)\" (onHide)=\"onFocusOut()\">\r\n       </p-calendar>\r\n       <p-inputNumber #targetElement showButtons=\"true\" *ngIf=\"cellRendererParams.format.type === actionableGridColumnType.Currency ||\r\n                               cellRendererParams.format.type === actionableGridColumnType.Numeric\"\r\n              [required]=\"required\" [(ngModel)]=\"numericValue\" [prefix]=\"(cellRendererParams.format.type === actionableGridColumnType.Currency) ?\r\n                               (cellRendererParams.format.currency | currencySymbol) : ''\"\r\n              [minFractionDigits]=\"cellRendererParams.format.decimalPlaces\"\r\n              [maxFractionDigits]=\"cellRendererParams.format.decimalPlaces\" (focusout)=\"onFocusOut()\"\r\n              [placeholder]=\"validValue ? '':cellRendererParams.value\" (keydown)=\"onKeydownEvent($event)\">\r\n       </p-inputNumber>\r\n</div>"], "mappings": "AACA,SAASA,UAAU,QAAQ,2CAA2C;AAEtE,SAASC,kBAAkB,QAAQ,gCAAgC;AAEnE,SAASC,yBAAyB,EAAEC,yBAAyB,EAAEC,sBAAsB,QAAQ,yCAAyC;AACtI,SAASC,gBAAgB,QAAQ,8CAA8C;AAG/E,SAASC,kBAAkB,QAAQ,4CAA4C;AAC/E,SAASC,iBAAiB,QAAQ,qBAAqB;AACvD,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,uBAAuB,QAAQ,2DAA2D;AACnG,SAASC,eAAe,QAAQ,mBAAmB;AACnD,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,IAAI,QAAQ,iBAAiB;;;;;;;;;;;;;;IChBtCC,EAAA,CAAAC,cAAA,UAA6C;IAC3CD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IADJH,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,OAAAC,OAAA,GAAAC,MAAA,CAAAC,kBAAA,kBAAAD,MAAA,CAAAC,kBAAA,CAAAC,cAAA,cAAAH,OAAA,KAAAI,SAAA,GAAAJ,OAAA,GAAAC,MAAA,CAAAC,kBAAA,kBAAAD,MAAA,CAAAC,kBAAA,CAAAG,KAAA,OACF;;;;;;IAMcX,EAAA,CAAAC,cAAA,8BAOiG;IAHvCD,EAAA,CAAAY,UAAA,iCAAAC,8HAAAC,MAAA;MAAAd,EAAA,CAAAe,aAAA,CAAAC,GAAA;MAAA,MAAAT,MAAA,GAAAP,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAAuBX,MAAA,CAAAY,aAAA,CAAAL,MAAA,CAAqB;IAAA,EAAC;IAGNd,EAAA,CAAAG,YAAA,EAAsB;;;;;IAAhHH,EALA,CAAAoB,UAAA,cAAAb,MAAA,CAAAC,kBAAA,CAAAa,SAAA,CAA0C,sBAAAC,OAAA,GAAAf,MAAA,CAAAC,kBAAA,CAAAe,gBAAA,cAAAD,OAAA,KAAAZ,SAAA,GAAAY,OAAA,GAAAf,MAAA,CAAAC,kBAAA,CAAAa,SAAA,CAC8C,gBAAAd,MAAA,CAAAC,kBAAA,CAAAgB,eAAA,CACtC,qBAAAjB,MAAA,CAAAC,kBAAA,CAAAiB,gBAAA,CACiC,kBAAAlB,MAAA,CAAAmB,WAAA,CACtD,iBAAAnB,MAAA,CAAAC,kBAAA,CAAAgB,eAAA,kBAAAjB,MAAA,CAAAC,kBAAA,CAAAgB,eAAA,CAAAG,sBAAA,kBAAApB,MAAA,CAAAC,kBAAA,CAAAgB,eAAA,CAAAG,sBAAA,CAAAC,YAAA,CAC4D;;;;;;IAThG5B,EADP,CAAAC,cAAA,aAAiH,kBAEtC;IADvBD,EAAA,CAAA6B,gBAAA,2BAAAC,oFAAAhB,MAAA;MAAAd,EAAA,CAAAe,aAAA,CAAAgB,GAAA;MAAA,MAAAxB,MAAA,GAAAP,EAAA,CAAAiB,aAAA;MAAAjB,EAAA,CAAAgC,kBAAA,CAAAzB,MAAA,CAAAmB,WAAA,EAAAZ,MAAA,MAAAP,MAAA,CAAAmB,WAAA,GAAAZ,MAAA;MAAA,OAAAd,EAAA,CAAAkB,WAAA,CAAAJ,MAAA;IAAA,EAAyB;IACrCd,EAA1B,CAAAY,UAAA,sBAAAqB,+EAAA;MAAAjC,EAAA,CAAAe,aAAA,CAAAgB,GAAA;MAAA,MAAAxB,MAAA,GAAAP,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAAYX,MAAA,CAAA2B,UAAA,EAAY;IAAA,EAAC,qBAAAC,8EAAArB,MAAA;MAAAd,EAAA,CAAAe,aAAA,CAAAgB,GAAA;MAAA,MAAAxB,MAAA,GAAAP,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAAYX,MAAA,CAAA6B,cAAA,CAAAtB,MAAA,CAAsB;IAAA,EAAC;IADnEd,EAAA,CAAAG,YAAA,EACoE;IACpEH,EAAA,CAAAqC,UAAA,IAAAC,0EAAA,iCAOiG;IACxGtC,EAAA,CAAAG,YAAA,EAAM;;;;IAV8CH,EAAA,CAAAI,SAAA,EAAyB;IAAzBJ,EAAA,CAAAuC,gBAAA,YAAAhC,MAAA,CAAAmB,WAAA,CAAyB;IAAC1B,EAAA,CAAAoB,UAAA,aAAAb,MAAA,CAAAiC,QAAA,CAAqB;IAGpFxC,EAAA,CAAAI,SAAA,GAAiH;IAAjHJ,EAAA,CAAAoB,UAAA,SAAAb,MAAA,CAAAC,kBAAA,CAAAiC,YAAA,KAAAlC,MAAA,CAAAC,kBAAA,CAAAgB,eAAA,kBAAAjB,MAAA,CAAAC,kBAAA,CAAAgB,eAAA,CAAAG,sBAAA,kBAAApB,MAAA,CAAAC,kBAAA,CAAAgB,eAAA,CAAAG,sBAAA,CAAAc,YAAA,EAAiH;;;;;;IAQhIzC,EAAA,CAAAC,cAAA,wBAQ4F;IANrFD,EAAA,CAAA6B,gBAAA,2BAAAa,gGAAA5B,MAAA;MAAAd,EAAA,CAAAe,aAAA,CAAA4B,GAAA;MAAA,MAAApC,MAAA,GAAAP,EAAA,CAAAiB,aAAA;MAAAjB,EAAA,CAAAgC,kBAAA,CAAAzB,MAAA,CAAAqC,SAAA,EAAA9B,MAAA,MAAAP,MAAA,CAAAqC,SAAA,GAAA9B,MAAA;MAAA,OAAAd,EAAA,CAAAkB,WAAA,CAAAJ,MAAA;IAAA,EAAuB;IAMsCd,EAA7D,CAAAY,UAAA,sBAAAiC,2FAAA;MAAA7C,EAAA,CAAAe,aAAA,CAAA4B,GAAA;MAAA,MAAApC,MAAA,GAAAP,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAAYX,MAAA,CAAA2B,UAAA,EAAY;IAAA,EAAC,qBAAAY,0FAAAhC,MAAA;MAAAd,EAAA,CAAAe,aAAA,CAAA4B,GAAA;MAAA,MAAApC,MAAA,GAAAP,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAAYX,MAAA,CAAA6B,cAAA,CAAAtB,MAAA,CAAsB;IAAA,EAAC,oBAAAiC,yFAAA;MAAA/C,EAAA,CAAAe,aAAA,CAAA4B,GAAA;MAAA,MAAApC,MAAA,GAAAP,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAAWX,MAAA,CAAA2B,UAAA,EAAY;IAAA,EAAC;IAC3FlC,EAAA,CAAAG,YAAA,EAAa;;;;IAPNH,EAAA,CAAAuC,gBAAA,YAAAhC,MAAA,CAAAqC,SAAA,CAAuB;IAK0D5C,EALzC,CAAAoB,UAAA,yBAAwB,2BAA2B,gBAAAb,MAAA,CAAAC,kBAAA,CAAAwC,MAAA,CAAAC,IAAA,KAAA1C,MAAA,CAAA2C,wBAAA,CAAAC,QAAA,CACP,aAAA5C,MAAA,CAAAC,kBAAA,CAAAwC,MAAA,CAAAC,IAAA,KAAA1C,MAAA,CAAA2C,wBAAA,CAAAC,QAAA,CACH,mBAAmB,wBAC7E,kBAAkB,sBAAsB,wBAAwB,uBACjE,wBAA8C,gBAAA5C,MAAA,CAAA6C,UAAA,QAAA7C,MAAA,CAAAC,kBAAA,CAAAG,KAAA,CACV,aAAAJ,MAAA,CAAAiC,QAAA,CAA4C;;;;;;IAG7GxC,EAAA,CAAAC,cAAA,2BAMmG;;IAJtED,EAAA,CAAA6B,gBAAA,2BAAAwB,sGAAAvC,MAAA;MAAAd,EAAA,CAAAe,aAAA,CAAAuC,GAAA;MAAA,MAAA/C,MAAA,GAAAP,EAAA,CAAAiB,aAAA;MAAAjB,EAAA,CAAAgC,kBAAA,CAAAzB,MAAA,CAAAgD,YAAA,EAAAzC,MAAA,MAAAP,MAAA,CAAAgD,YAAA,GAAAzC,MAAA;MAAA,OAAAd,EAAA,CAAAkB,WAAA,CAAAJ,MAAA;IAAA,EAA0B;IAISd,EADK,CAAAY,UAAA,sBAAA4C,iGAAA;MAAAxD,EAAA,CAAAe,aAAA,CAAAuC,GAAA;MAAA,MAAA/C,MAAA,GAAAP,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAAYX,MAAA,CAAA2B,UAAA,EAAY;IAAA,EAAC,qBAAAuB,gGAAA3C,MAAA;MAAAd,EAAA,CAAAe,aAAA,CAAAuC,GAAA;MAAA,MAAA/C,MAAA,GAAAP,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CACnBX,MAAA,CAAA6B,cAAA,CAAAtB,MAAA,CAAsB;IAAA,EAAC;IAClGd,EAAA,CAAAG,YAAA,EAAgB;;;;IALTH,EAAA,CAAAoB,UAAA,aAAAb,MAAA,CAAAiC,QAAA,CAAqB;IAACxC,EAAA,CAAAuC,gBAAA,YAAAhC,MAAA,CAAAgD,YAAA,CAA0B;IAIhDvD,EAJiD,CAAAoB,UAAA,WAAAb,MAAA,CAAAC,kBAAA,CAAAwC,MAAA,CAAAC,IAAA,KAAA1C,MAAA,CAAA2C,wBAAA,CAAAQ,QAAA,GAAA1D,EAAA,CAAA2D,WAAA,OAAApD,MAAA,CAAAC,kBAAA,CAAAwC,MAAA,CAAAY,QAAA,OAC2B,sBAAArD,MAAA,CAAAC,kBAAA,CAAAwC,MAAA,CAAAa,aAAA,CACf,sBAAAtD,MAAA,CAAAC,kBAAA,CAAAwC,MAAA,CAAAa,aAAA,CACA,gBAAAtD,MAAA,CAAA6C,UAAA,QAAA7C,MAAA,CAAAC,kBAAA,CAAAG,KAAA,CACL;;;;;IA9BtEX,EAAA,CAAAC,cAAA,aACsC;IAuB/BD,EAtBA,CAAAqC,UAAA,IAAAyB,oDAAA,iBAAiH,IAAAC,2DAAA,yBAoBrB,IAAAC,8DAAA,2BAQO;IAE1GhE,EAAA,CAAAG,YAAA,EAAM;;;;IA/BCH,EAAA,CAAAoB,UAAA,oBAAAb,MAAA,CAAA6C,UAAA,CAA8B;IACSpD,EAAA,CAAAI,SAAA,EAAwE;IAAxEJ,EAAA,CAAAoB,UAAA,SAAAb,MAAA,CAAAC,kBAAA,CAAAwC,MAAA,CAAAC,IAAA,KAAA1C,MAAA,CAAA2C,wBAAA,CAAAe,MAAA,CAAwE;IAavGjE,EAAA,CAAAI,SAAA,EAAsH;IAAtHJ,EAAA,CAAAoB,UAAA,SAAApB,EAAA,CAAAkE,eAAA,IAAAC,GAAA,EAAA5D,MAAA,CAAA2C,wBAAA,CAAAkB,IAAA,EAAA7D,MAAA,CAAA2C,wBAAA,CAAAC,QAAA,EAAAkB,QAAA,CAAA9D,MAAA,CAAAC,kBAAA,CAAAwC,MAAA,CAAAC,IAAA,EAAsH;IAS5EjD,EAAA,CAAAI,SAAA,EACwC;IADxCJ,EAAA,CAAAoB,UAAA,SAAAb,MAAA,CAAAC,kBAAA,CAAAwC,MAAA,CAAAC,IAAA,KAAA1C,MAAA,CAAA2C,wBAAA,CAAAQ,QAAA,IAAAnD,MAAA,CAAAC,kBAAA,CAAAwC,MAAA,CAAAC,IAAA,KAAA1C,MAAA,CAAA2C,wBAAA,CAAAoB,OAAA,CACwC;;;ADJjG,OAAM,MAAOC,+BAAgC,SAAQpF,kBAAkB;EAErEqF,YACEC,oBAA0C,EAC1CC,mBAAwC,EACxCC,aAA4B;IAC5B,KAAK,CAACF,oBAAoB,EAAEC,mBAAmB,EAAEC,aAAa,CAAC;IAGjE,KAAAzB,wBAAwB,GAAG3D,gBAAgB;IAW3C;IACA,KAAA6D,UAAU,GAAG,IAAI;EAdjB;EAgBAwB,MAAMA,CAACpE,kBAA6C;IAClD,KAAK,CAACoE,MAAM,CAACpE,kBAAkB,CAAC;IAEhC,IAAI,IAAI,CAACqE,aAAa,EAAE,IAAI,IAAI,CAACrE,kBAAkB,CAACG,KAAK,IAAI,IAAI,EAC/D,IAAI,CAACmE,eAAe,EAAE;EAC1B;EAEA3D,aAAaA,CAAC4D,IAAS;IACrB,IAAIA,IAAI,EACN,KAAK,CAACC,WAAW,CAACD,IAAI,CAAC;EAC3B;EAEAD,eAAeA,CAAA;IACb,QAAQ,IAAI,CAACtE,kBAAkB,CAACwC,MAAM,CAACC,IAAI;MACzC,KAAK1D,gBAAgB,CAAC6E,IAAI;QACxB,IAAI,CAACxB,SAAS,GAAGtD,sBAAsB,CAAC,IAAI,CAACkB,kBAAkB,CAACG,KAAK,CAAC;QACtE;MACF,KAAKpB,gBAAgB,CAAC4D,QAAQ;QAC5B;QACA,IAAI,CAACP,SAAS,GAAGvD,yBAAyB,CAAC,IAAI,CAACmB,kBAAkB,CAACG,KAAK,CAAC;QACzE;MACF,KAAKpB,gBAAgB,CAAC+E,OAAO;MAC7B,KAAK/E,gBAAgB,CAACmE,QAAQ;QAC5B,IAAI,CAACH,YAAY,GAAG0B,MAAM,CAAC,IAAI,CAACzE,kBAAkB,CAACG,KAAK,CAAC;QACzD;MACF,KAAKpB,gBAAgB,CAAC0E,MAAM;QAC1B,IAAI,CAACvC,WAAW,GAAG,IAAI,CAAClB,kBAAkB,CAACG,KAAK;QAChD;IACJ;EACF;EAEAuB,UAAUA,CAAA;IACR;IACA;IACA;IACA,IAAI,CAAC3C,gBAAgB,CAAC6E,IAAI,EAAE7E,gBAAgB,CAAC4D,QAAQ,CAAC,CAACkB,QAAQ,CAAC,IAAI,CAAC7D,kBAAkB,CAACwC,MAAM,CAACC,IAAI,CAAC,EAAE;MACpGiC,UAAU,CAAC,MAAK;QACd,IAAI,IAAI,CAACC,aAAa,EAAE,EACtB,KAAK,CAACC,OAAO,EAAE;MACnB,CAAC,EAAE,GAAG,CAAC;IACT,CAAC,MACI;MACH,IAAI,IAAI,CAACD,aAAa,EAAE,EACtB,KAAK,CAACC,OAAO,EAAE;IACnB;EACF;EAEAD,aAAaA,CAAA;IACX,QAAQ,IAAI,CAAC3E,kBAAkB,CAACwC,MAAM,CAACC,IAAI;MACzC,KAAK1D,gBAAgB,CAAC4D,QAAQ;MAC9B,KAAK5D,gBAAgB,CAAC6E,IAAI;QACxB;QACA,IAAI,IAAI,CAAC5D,kBAAkB,CAACG,KAAK,IAAI,IAAI,CAACiC,SAAS,IACjD,IAAI,CAACA,SAAS,EAAEyC,WAAW,EAAE,IAAIhG,yBAAyB,CAAC,IAAI,CAACmB,kBAAkB,CAACG,KAAK,CAAC,EAAE0E,WAAW,EAAE,EAAE;UAE1G;UACA,IAAI,CAAC7E,kBAAkB,CAACG,KAAK,GAAG,IAAI,CAACH,kBAAkB,CAACwC,MAAM,CAACC,IAAI,IAAI1D,gBAAgB,CAAC4D,QAAQ,GAC5F,IAAI,CAACP,SAAS,GAAGxD,yBAAyB,CAAC,IAAI,CAACwD,SAAS,CAAC,GAAGlC,SAAS,GACtE,IAAI,CAACkC,SAAS,GAAG1D,UAAU,CAAC,IAAI,CAAC0D,SAAS,CAAC0C,YAAY,EAAE,CAAC,GAAG5E,SAAS;UAC1E,OAAO,IAAI;QACb;QAEA,OAAO,KAAK;MACd,KAAKnB,gBAAgB,CAAC+E,OAAO;MAC7B,KAAK/E,gBAAgB,CAACmE,QAAQ;QAC5B,IAAI,IAAI,CAAClD,kBAAkB,CAACG,KAAK,IAAI,IAAI,CAAC4C,YAAY,EAAE;UACtD,IAAI,CAAC/C,kBAAkB,CAACG,KAAK,GAAG,IAAI,CAAC4C,YAAY;UACjD,OAAO,IAAI;QACb;QAEA,OAAO,KAAK;MACd,KAAKhE,gBAAgB,CAAC0E,MAAM;QAC1B,IAAI,IAAI,CAACzD,kBAAkB,CAACG,KAAK,IAAI,IAAI,CAACe,WAAW,EAAE6D,IAAI,EAAE,EAAE;UAC7D,IAAI,CAAC/E,kBAAkB,CAACG,KAAK,GAAG,IAAI,CAACe,WAAW,EAAE6D,IAAI,EAAE;UACxD,OAAO,IAAI;QACb;QAEA,OAAO,KAAK;IAChB;EACF;EAEA;EACAV,aAAaA,CAAA;IACX;IACA,IAAI,IAAI,CAACrE,kBAAkB,CAACG,KAAK,IAAI,IAAI,IAAI,CAACpB,gBAAgB,CAAC6E,IAAI,EAAE7E,gBAAgB,CAAC4D,QAAQ,CAAC,CAACkB,QAAQ,CAAC,IAAI,CAAC7D,kBAAkB,CAACwC,MAAM,CAACC,IAAI,CAAC,EAAE;MAC7I,IAAI,CAACG,UAAU,GAAGlE,UAAU,CAAC,IAAI,CAACsB,kBAAkB,CAACG,KAAK,CAAC,KAAK,cAAc;IAChF;IAEA;IACA,IAAI,CAACpB,gBAAgB,CAAC+E,OAAO,EAAE/E,gBAAgB,CAACmE,QAAQ,CAAC,CAACW,QAAQ,CAAC,IAAI,CAAC7D,kBAAkB,CAACwC,MAAM,CAACC,IAAI,CAAC,EAAE;MACvG,IAAI,CAACG,UAAU,GAAG,IAAI,CAAC5C,kBAAkB,CAACG,KAAK,IAAI,IAAI,IAAI,CAAC6E,KAAK,CAACP,MAAM,CAAC,IAAI,CAACzE,kBAAkB,CAACG,KAAK,CAAC,CAAC;IAC1G;IAEA,OAAO,IAAI,CAACyC,UAAU;EACxB;EAEAqC,YAAYA,CAAA;IACV,IAAIC,UAAsB;IAE1B,QAAQ,IAAI,CAAClF,kBAAkB,CAACwC,MAAM,CAACC,IAAI;MACzC,KAAK1D,gBAAgB,CAAC0E,MAAM;QAC1ByB,UAAU,GAAG,IAAI,CAACA,UAAU;QAC5B;MACF,KAAKnG,gBAAgB,CAAC4D,QAAQ;MAC9B,KAAK5D,gBAAgB,CAAC6E,IAAI;QACxBsB,UAAU,GAAG,IAAI,CAACA,UAAU,EAAEC,mBAAmB;QACjD;MACF,KAAKpG,gBAAgB,CAAC+E,OAAO;MAC7B,KAAK/E,gBAAgB,CAACmE,QAAQ;QAC5BgC,UAAU,GAAG,IAAI,CAACA,UAAU,EAAEE,KAAK;QACnC;IACJ;IAEAF,UAAU,EAAEG,aAAa,EAAEC,KAAK,EAAE;IAClCJ,UAAU,EAAEG,aAAa,EAAEE,MAAM,EAAE;EACrC;EAEA3D,cAAcA,CAAC4D,KAAoB;IACjC;IACA,IAAKA,KAAK,CAACC,OAAO,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC5B,QAAQ,CAAC2B,KAAK,CAACE,GAAG,CAAC,IAClE,CAAC,WAAW,EAAE,YAAY,EAAE,SAAS,EAAE,WAAW,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC7B,QAAQ,CAAC2B,KAAK,CAACE,GAAG,CAAE,EAAE;MAC7FF,KAAK,CAACG,eAAe,EAAE;IACzB;EACF;;;uBAlJW5B,+BAA+B,EAAAvE,EAAA,CAAAoG,iBAAA,CAAAC,EAAA,CAAAC,oBAAA,GAAAtG,EAAA,CAAAoG,iBAAA,CAAAG,EAAA,CAAAC,mBAAA,GAAAxG,EAAA,CAAAoG,iBAAA,CAAAK,EAAA,CAAAC,aAAA;IAAA;EAAA;;;YAA/BnC,+BAA+B;MAAAoC,SAAA;MAAAC,SAAA,WAAAC,sCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;;;UCrB5C9G,EAHA,CAAAqC,UAAA,IAAA2E,8CAAA,iBAA6C,IAAAC,8CAAA,iBAIP;;;UAJhCjH,EAAA,CAAAoB,UAAA,SAAA2F,GAAA,CAAAvG,kBAAA,kBAAAuG,GAAA,CAAAvG,kBAAA,CAAA0G,IAAA,kBAAAH,GAAA,CAAAvG,kBAAA,CAAA0G,IAAA,CAAAC,KAAA,CAAqC;UAGrCnH,EAAA,CAAAI,SAAA,EAAgB;UAAhBJ,EAAA,CAAAoB,UAAA,SAAA2F,GAAA,CAAAK,UAAA,CAAgB;;;qBDmBRrH,IAAI,EAAED,aAAa,EAAAuH,EAAA,CAAAC,OAAA,EAAEzH,WAAW,EAAA0H,EAAA,CAAAC,oBAAA,EAAAD,EAAA,CAAAE,eAAA,EAAAF,EAAA,CAAAG,iBAAA,EAAAH,EAAA,CAAAI,OAAA,EAAE/H,eAAe,EAAAgI,EAAA,CAAAC,SAAA,EAAElI,uBAAuB,EAAED,cAAc,EAAAoI,EAAA,CAAAC,QAAA,EAAEtI,iBAAiB,EAAAuI,EAAA,CAAAC,WAAA,EAAEzI,kBAAkB;MAAA0I,aAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}